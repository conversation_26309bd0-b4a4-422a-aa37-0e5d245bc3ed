using MasterDataModule.StatusMasterObjects;
using Microsoft.AspNetCore.Mvc;
using OutboundModule.DoPoManages;
using OutboundModule.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace OutboundModule.GroupPrcvHistories
{
    public class EfCoreGroupPrcvHistoryRepository : EfCoreRepository<OutboundModuleDbContext, GroupPrcvHistory, long>, IGroupPrcvHistoryRepository
    {
        public EfCoreGroupPrcvHistoryRepository(IDbContextProvider<OutboundModuleDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<PagedResult<ReceivedGroup>> GetPagedListReceivedGroupByAsync(string scaleBy, long? mawbId, long? hawbId, long? doPoId,
            int maxResultCount, int skipCount, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = from grh in dbContext.GroupPrcvHistories
                            .Where(x => x.GrhDeleted == false && x.GrhObjectIsn == mawbId)
                            .WhereIf(scaleBy == "HAWB", x => x.GrhHawb == hawbId.GetValueOrDefault().ToString())
                            .WhereIf(scaleBy == "DOPO", x => x.GrhDoNumber == doPoId.GetValueOrDefault().ToString())
                        join hawb in dbContext.HawbHouseWaybillDetails on grh.GrhHawb equals hawb.Id.ToString() into hawbGroups
                        from hawbGroup in hawbGroups.DefaultIfEmpty()
                        join dopo in dbContext.DoPoManages on grh.GrhDoNumber equals dopo.Id.ToString() into doPoGroups
                        from doPoGroup in doPoGroups.DefaultIfEmpty()
                        join locs in dbContext.LocsLocations
                             on new { i = grh.GrhObjectIsn, g = grh.GrhGroupIdentNo } equals new { i = locs.LocsObjectIsn, g = locs.LocsGroupIsn } into locsGroups
                        from locs in locsGroups.DefaultIfEmpty()
                        join phys in dbContext.SslpPhysicalLocations.Where(x => !x.SslpDeleted)
                            on locs.LocsPhysicalIsn equals phys.Id into phySub
                        from phys in phySub.DefaultIfEmpty()
                        orderby grh.GrhReceivedDate descending
                        select new ReceivedGroup
                        {
                            GrhId = grh.Id,
                            GrhObjectIsn = grh.GrhObjectIsn,
                            GrhPrefix = grh.GrhPrefix,
                            GrhSerial = grh.GrhSerial,
                            GrhGroupIdentNo = grh.GrhGroupIdentNo,
                            GrhReceivedPieces = grh.GrhReceivedPieces,
                            GrhReceivedWeight = grh.GrhReceivedWeight,
                            GrhTotalPieces = grh.GrhTotalPieces,
                            GrhReceivedGrossWeight = grh.GrhReceivedGrossWeight,
                            GrhClass = grh.GrhClass,
                            GrhReceivedBaseWeight = grh.GrhReceivedBaseWeight,
                            GrhRemark = grh.GrhRemark,
                            GrhReceivedDate = grh.GrhReceivedDate,
                            GrhHawb = grh.GrhHawb,
                            HawbHouseNumber = hawbGroup != null ? hawbGroup.HawbHouseNumber : "",
                            DoPoNumber = doPoGroup != null ? doPoGroup.DomgrDono : "",
                            Location = $"{phys.SslpRackRow}{phys.SslpRackLocation:D2}-{phys.SslpRackHeight}"
                        };
            return query.PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<InventoryInfo>> GetListInventoryBalanceAsync(
            long kundId,
            string kundCode,
            string locationId,
            string dest,
            string hawbNo,
            string mawbPrefix,
            string mawbSerialNo,
            List<string> lstPalletNo,
            List<string> lstDoNo,
            int maxResultCount,
            int skipCount)
        {
            var dbContext = await GetDbContextAsync();

            var lstPalletStatus = new List<string>
            {
                DoStatusKeyConst.RECEIVE_WEIGHT_SCALING,
                DoStatusKeyConst.STORED_IN_WAREHOURE,
                DoStatusKeyConst.TRUCK_LOADING_PLAN
            };

            var query = (from pallet in dbContext.DoDnnPalletMgrs.Where(x => x.PltDeleted == false && lstPalletStatus.Contains(x.PltReceivedStatus)
                                                                          && x.PltReceivedDate.Value.Year >= 2025 && x.PltReceivedDate.Value.Month >= 5
                                                                          && x.PltAlsbNbaDatetimeOut == null && x.PltReceivedPieces > 0)
                            .WhereIf(lstPalletNo != null && lstPalletNo.Count > 0, x => lstPalletNo.Contains(x.PltGroupPalletNo))
                         join doPo in dbContext.DoPoManages.Where(x => x.DoMgrDeleted == false)
                            .WhereIf(lstDoNo != null && lstDoNo.Count > 0, x => lstDoNo.Contains(x.DomgrDono.ToUpper()))
                             on pallet.PltDoNo equals doPo.DomgrDono
                         join lab in dbContext.Labs.Where(x => x.LabsDeleted == false)
                             on doPo.MawbId equals lab.Id into labTemp
                         from lab in labTemp.DefaultIfEmpty()
                         join hawb in dbContext.HawbHouseWaybillDetails
                             on doPo.HawbId equals hawb.Id into temp
                         from hawb in temp.DefaultIfEmpty()
                         join b in dbContext.BookBookings
                             on lab.Id equals b.BookLabsIdent into b
                         from bb in b.DefaultIfEmpty()
                         join locs in dbContext.LocsLocations.Where(x => !x.LocsDeleted.Value)
                             on pallet.PltGroupPalletNo equals locs.LocsGroupIsn into locsGroups
                         from locs in locsGroups.DefaultIfEmpty()
                         join phys in dbContext.SslpPhysicalLocations.Where(x => !x.SslpDeleted)
                             on locs.LocsPhysicalIsn equals phys.Id into phySub
                         from phys in phySub.DefaultIfEmpty()
                         join kund in (await GetDbContextAsync()).Kunds
                             on lab.LabsAgentAccCode equals kund.Id into kundGroup
                         from kund in kundGroup.DefaultIfEmpty()
                         orderby pallet.PltCreatedAt descending
                         select new InventoryInfo
                         {
                             MawbId = lab.Id,
                             Prefix = lab.LabsMawbPrefix,
                             Serial = lab.LabsMawbSerialNo,
                             GroupIdentNo = pallet.PltGroupPalletNo,
                             ReceivedPieces = pallet.PltReceivedPieces,
                             ReceivedWeight = pallet.PltReceivedWeight,
                             ////Class = grh.GrhClass,
                             HawbHouseNumber = hawb.HawbHouseNumber,
                             DoPoNumber = doPo.DomgrDono,
                             Forwarder = string.IsNullOrWhiteSpace(doPo.DomgrForwarder) ? kund.Kund3letterCode : doPo.DomgrForwarder,
                             Dest = string.IsNullOrWhiteSpace(doPo.DongrDest) ? lab.LabsDestination : doPo.DongrDest,
                             Location = $"{phys.SslpRackRow}{phys.SslpRackLocation:D2}-{phys.SslpRackHeight}",
                             LocsPhysicalIsn = locs.LocsPhysicalIsn,
                             FlightAirline = bb.BookFlightAirline,
                             FlightNumber = bb.BookFlightNumber,
                             FlightDate = bb.BookFlightDate,
                             Scanned = pallet.PltMobiScan,
                             PltSeaAirType = pallet.PltSeaAirType
                         })
                         .WhereIf(!hawbNo.IsNullOrWhiteSpace(), x => x.HawbHouseNumber.Contains(hawbNo))
                         .WhereIf(!string.IsNullOrWhiteSpace(kundCode), x => x.Forwarder == kundCode)
                         .WhereIf(!string.IsNullOrWhiteSpace(dest), x => x.Dest == dest)
                         .WhereIf(!mawbPrefix.IsNullOrWhiteSpace(), x => x.Prefix == mawbPrefix)
                         .WhereIf(!mawbSerialNo.IsNullOrWhiteSpace(), x => x.Serial.ToString().Contains(mawbSerialNo));

            var data = from a in query.ToList()
                       group a by a.GroupIdentNo into g
                       select new InventoryInfo
                       {
                           MawbId = g.FirstOrDefault().MawbId,
                           Prefix = g.FirstOrDefault().Prefix,
                           Serial = g.FirstOrDefault().Serial,
                           ReceivedPieces = g.Select(x => new { x.ReceivedPieces, x.GroupIdentNo }).Distinct().Sum(x => x.ReceivedPieces),
                           ReceivedWeight = g.Select(x => new { x.ReceivedWeight, x.GroupIdentNo }).Distinct().Sum(x => x.ReceivedWeight),
                           GroupIdentNo = g.Key,
                           HawbHouseNumber = g.FirstOrDefault().HawbHouseNumber,
                           DoPoNumber = g.FirstOrDefault().DoPoNumber,
                           Location = g.FirstOrDefault().Location,
                           Forwarder = g.FirstOrDefault().Forwarder,
                           Dest = g.FirstOrDefault().Dest,
                           LocsPhysicalIsn = g.FirstOrDefault().LocsPhysicalIsn,
                           FlightAirline = g.FirstOrDefault().FlightAirline,
                           FlightNumber = g.FirstOrDefault().FlightNumber,
                           FlightDate = g.FirstOrDefault().FlightDate,
                           Scanned = g.FirstOrDefault().Scanned,
                           PltSeaAirType = g.FirstOrDefault().PltSeaAirType,
                       };

            data = data.WhereIf(!string.IsNullOrWhiteSpace(locationId), x => x.Location == locationId);

            return data.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<List<string>> GetListDestMawbInventoryAsync()
        {
            var dbContext = await GetDbContextAsync();
            return (from lab in dbContext.Labs.Where(x => x.LabsDeleted == false)
                    where lab.LabsDestination.Length >= 3
                    select lab.LabsDestination).Distinct().ToList();
        }

        public async Task<List<PoDoByVehicleScale>> GetPoDoByVehicleScaleList(long vehicleIsn = 0, string PoNumber = null, string groupId = null)
        {
            var latestGrhs = (await GetDbSetAsync())
                .Where(x => !x.GrhDeleted && x.GrhReceivedDate.HasValue)
                .WhereIf(!string.IsNullOrWhiteSpace(groupId), x => x.GrhGroupPalletNo.Equals(groupId.ToUpper()))
                .WhereIf(!string.IsNullOrWhiteSpace(PoNumber), x => x.GrhDoNumber.ToUpper().Equals(PoNumber.ToUpper()))
                .ToList()
                .GroupBy(x => x.GrhGroupPalletNo)
                .Select(g => g.OrderByDescending(x => x.GrhReceivedDate).ThenByDescending(x => x.Id).First())
                .ToList();

            var poQuery = (from po in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                            .WhereIf(!string.IsNullOrWhiteSpace(PoNumber), x => x.DomgrDono.Equals(PoNumber.ToUpper()))
                           join pl in (await GetDbContextAsync()).DoDnnPalletMgrs.Where(x => x.PltDeleted == false && x.PltReceivedStatus != "5") //Không lấy thông tin pallet return
                            .WhereIf(!string.IsNullOrWhiteSpace(groupId), x => x.PltGroupPalletNo.Equals(groupId.ToUpper()))
                           on po.DomgrDono equals pl.PltDoNo into tempPl
                           from pll in tempPl.DefaultIfEmpty()
                           join l in (await GetDbContextAsync()).Labs.Where(x => x.LabsDeleted == false)
                           on po.MawbId equals l.Id into tempLab
                           from ll in tempLab.DefaultIfEmpty()
                           join v in (await GetDbContextAsync()).VhldVehicleDetail.WhereIf(vehicleIsn > 0, x => x.VhldVehicleisn == vehicleIsn && x.VhldDeleted == false)
                           on po.Id equals v.VhldObjectisn
                           select new { po, pll, ll }).ToList();

            var query = (from row in poQuery
                         join gr in latestGrhs on row.pll?.PltGroupPalletNo equals gr.GrhGroupPalletNo into ljGr
                         from gr in ljGr.DefaultIfEmpty()
                         select new PoDoByVehicleScale
                         {
                             MawbId = row.ll?.Id,
                             DoId = row.po.Id,
                             Prefix = row.ll?.LabsMawbPrefix,
                             Serial = row.ll?.LabsMawbSerialNo,
                             PalletNo = row.pll?.PltGroupPalletNo,
                             PoNumber = row.po.DomgrDono,
                             ReceivedPieces = row.pll?.PltReceivedPieces,
                             ReceivedWeight = row.pll?.PltReceivedWeight,
                             TotalPieces = gr?.GrhTotalPieces,
                             ReceivedGrossWeight = row.pll?.PltReceivedWeight,
                             ReceivedDate = row.pll?.PltReceivedDate,
                             UserName = gr?.GrhReceivedUser,
                             Remark = row.pll?.PltRemarks ?? " ",
                             PltDimL = row.pll?.PltDimL,
                             PltDimW = row.pll?.PltDimW,
                             PltDimH = row.pll?.PltDimH,
                             GrdId = gr?.Id,
                             Printed = gr?.GrhLabelPrinted,
                             Dest = row.po.DongrDest,
                             Agent = row.po.DomgrForwarder,
                             GroupId = gr?.GrhGroupIdentNo,
                             Pin = row.pll?.PltRemarks,
                             AirSeaMixDo = row.po.DomgrMasterIsn.HasValue ? "Mix" : row.pll?.PltSeaAirType,
                         })
                          .OrderBy(x => x.ReceivedDate)
                          .ToList();

            return query.OrderBy(x => x.ReceivedDate.HasValue ? 0 : 1).ThenBy(x => x.ReceivedDate).ToList();
        }

        public async Task<JsonResult> CheckPalletIDScale(long vehicleIsn = 0, string palletId = null)
        {
            var queryScale = (from po in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)

                              join pl in (await GetDbContextAsync()).DoDnnPalletMgrs
                              .WhereIf(!string.IsNullOrWhiteSpace(palletId), x => x.PltGroupPalletNo.Equals(palletId.ToUpper()))
                              on po.DomgrDono equals pl.PltDoNo
                              join v in (await GetDbContextAsync()).VhldVehicleDetail
                              .Where(x => x.VhldVehicleisn == vehicleIsn && x.VhldDeleted == false)
                              on po.Id equals v.VhldObjectisn
                              join grh in (await GetDbSetAsync())
                              on pl.PltGroupPalletNo.ToString() equals grh.GrhGroupPalletNo
                              select new
                              {
                                  DoId = po.Id,
                              }).Distinct();

            var queryExsit = (from po in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                              join pl in (await GetDbContextAsync()).DoDnnPalletMgrs
                              .WhereIf(!string.IsNullOrWhiteSpace(palletId), x => x.PltGroupPalletNo.Equals(palletId.ToUpper()))
                              on po.DomgrDono equals pl.PltDoNo
                              join v in (await GetDbContextAsync()).VhldVehicleDetail
                              .Where(x => x.VhldVehicleisn == vehicleIsn && x.VhldDeleted == false)
                              on po.Id equals v.VhldObjectisn
                              select new
                              {
                                  DoId = po.Id,
                              }).Distinct();
            if (queryScale.Any())
            {
                //PalletID đã được cân
                return new JsonResult(new { Result = 1, queryScale.FirstOrDefault().DoId });
            }
            else
            {
                if (queryExsit.Any())
                {
                    //PalletID tồn tại trên xe và chưa được cân
                    return new JsonResult(new { Result = 2, queryExsit.FirstOrDefault().DoId });
                }
            }

            //PalletID không tồn tại trên xe
            return new JsonResult(new { Result = 0 });
        }

        public async Task<JsonResult> CheckDoNumberScale(long vehicleIsn = 0, string doNumber = null)
        {
            var query = (from po in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                         .WhereIf(!string.IsNullOrWhiteSpace(doNumber), x => x.DomgrDono.ToUpper().Equals(doNumber.ToUpper()))
                             //join pl in (await GetDbContextAsync()).DoDnnPalletMgrs
                             //.WhereIf(!string.IsNullOrWhiteSpace(doNumber), x => x.PltDoNo.Equals(doNumber.ToUpper()))
                             //on po.DomgrDono equals pl.PltDoNo
                         join v in (await GetDbContextAsync()).VhldVehicleDetail
                         .Where(x => x.VhldVehicleisn == vehicleIsn && x.VhldDeleted == false)
                         on po.Id equals v.VhldObjectisn
                         select new
                         {
                             DoId = po.Id,
                             po.MawbId
                         }).Distinct();
            if (query.Any())
            {
                return new JsonResult(new { Result = 1, query.FirstOrDefault().DoId, query.FirstOrDefault().MawbId });
            }
            // Trả về các status
            // 1 đã tồn tại trên xe
            return new JsonResult(new { Result = 0 });
        }

        public async Task<CheckDoNumberInTruck> CheckDoNumberInTruck(string doNumber = null)
        {
            var query = (from po in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                         .WhereIf(!string.IsNullOrWhiteSpace(doNumber), x => x.DomgrDono.ToUpper().Equals(doNumber.ToUpper()))
                         join vhld in (await GetDbContextAsync()).VhldVehicleDetail
                         .Where(x => x.VhldDeleted == false)
                         on po.Id equals vhld.VhldObjectisn into ljVhld
                         from vhld in ljVhld.DefaultIfEmpty()
                         join vhcl in (await GetDbContextAsync()).VehiclesRegistrations
                         on vhld.VhldVehicleisn equals vhcl.Id into ljVhcl
                         from vhcl in ljVhcl.DefaultIfEmpty()
                         select new CheckDoNumberInTruck
                         {
                             DoId = po.Id,
                             DoNumber = po.DomgrDono,
                             VehicleId = vhld.VhldVehicleisn,
                             VehicleRegNo = vhcl.VehicRegNo,
                             Result = 1 // Trạng thái có tồn tại DO
                         }).Distinct();

            var result = query.FirstOrDefault();

            return result ?? new CheckDoNumberInTruck
            {
                Result = 0 // Trạng thái không tồn tại DO
            };
        }

        public async Task<CheckPalletInTruck> CheckPalletInTruck(string palletNo = null)
        {
            var query = (from pallet in (await GetDbContextAsync()).DoDnnPalletMgrs.Where(x => x.PltDeleted == false)
                         .WhereIf(!string.IsNullOrWhiteSpace(palletNo), x => x.PltGroupPalletNo.ToUpper().Equals(palletNo.ToUpper()))
                         join d in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                         on pallet.PltDoNo equals d.DomgrDono into ljDo
                         from d in ljDo.DefaultIfEmpty()
                         join vhld in (await GetDbContextAsync()).VhldVehicleDetail
                         .Where(x => x.VhldDeleted == false)
                         on d.Id equals vhld.VhldObjectisn into ljVhld
                         from vhld in ljVhld.DefaultIfEmpty()
                         join vhcl in (await GetDbContextAsync()).VehiclesRegistrations
                         on vhld.VhldVehicleisn equals vhcl.Id into ljVhcl
                         from vhcl in ljVhcl.DefaultIfEmpty()
                         select new CheckPalletInTruck
                         {
                             PalletId = pallet.Id,
                             DoId = d.Id,
                             DoNumber = pallet.PltDoNo,
                             PalletNo = pallet.PltGroupPalletNo,
                             VehicleId = vhld.VhldVehicleisn,
                             VehicleRegNo = vhcl.VehicRegNo,
                             Result = 1 // Trạng thái có tồn tại pallet
                         }).Distinct();

            var result = query.FirstOrDefault();

            return result ?? new CheckPalletInTruck
            {
                Result = 0 // Trạng thái không tồn tại pallet
            };
        }

        public async Task<JsonResult> CheckDoNumber(string doNumber = null)
        {
            var query = (from po in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                         .WhereIf(!string.IsNullOrWhiteSpace(doNumber), x => x.DomgrDono.Equals(doNumber.ToUpper()))
                         join pl in (await GetDbContextAsync()).DoDnnPalletMgrs
                         on po.DomgrDono equals pl.PltDoNo
                         into tempPl
                         from pl in tempPl.DefaultIfEmpty()
                         select new
                         {
                             DoId = po.Id,
                             po.MawbId,
                             Pallet = pl.PltGroupPalletNo
                         }).Distinct();
            if (query.Any())
            {
                return new JsonResult(new { Result = 1, query.FirstOrDefault().DoId, query.FirstOrDefault().MawbId, query.FirstOrDefault().Pallet });
            }
            return new JsonResult(new { Result = 0 });
        }

        public async Task<List<PieceWeightScale>> GetPcsWeightScaleList(long vehicleIsn = 0)
        {
            var query = (from po in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                         join pl in (await GetDbContextAsync()).DoDnnPalletMgrs.Where(x => x.PltDeleted == false)
                         on po.DomgrDono equals pl.PltDoNo
                         into tempPl
                         from pll in tempPl.DefaultIfEmpty()
                         join v in (await GetDbContextAsync()).VhldVehicleDetail
                         .Where(x => x.VhldVehicleisn == vehicleIsn && x.VhldDeleted == false)
                         on po.Id equals v.VhldObjectisn
                         join grh in (await GetDbSetAsync()).Where(x => x.GrhDeleted == false)
                         on new { a = pll.PltDoNo, b = pll.PltGroupPalletNo } equals new { a = grh.GrhDoNumber, b = grh.GrhGroupPalletNo }
                         into tempGrh
                         from gr in tempGrh.DefaultIfEmpty()
                         select new PieceWeightScale
                         {
                             ReceivedPieces = gr.GrhReceivedPieces,
                             ReceivedGrossWeight = gr.GrhReceivedGrossWeight,
                         });

            return query.ToList();
        }

        public async Task<List<PieceWeightScale>> GetPcsWeightListByVehicle(long vehicleIsn = 0)
        {
            var query = (from po in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                         join pl in (await GetDbContextAsync()).DoDnnPalletMgrs.Where(x => x.PltDeleted == false)
                         on po.DomgrDono equals pl.PltDoNo into ljPl
                         from pl in ljPl.DefaultIfEmpty()
                         join v in (await GetDbContextAsync()).VhldVehicleDetail
                         .Where(x => x.VhldVehicleisn == vehicleIsn && x.VhldDeleted == false)
                         on po.Id equals v.VhldObjectisn
                         select new PieceWeightScale
                         {
                             DoNo = po.DomgrDono,
                             ReceivedPieces = pl.PltReceivedPieces,
                             ReceivedGrossWeight = pl.PltReceivedWeight,
                         });

            return query.ToList();
        }

        public async Task<List<PieceWeightScale>> GetTotalPcsWeightListByVehicle(long vehicleIsn = 0)
        {
            var dbcontext = await GetDbContextAsync();
            var query = (from po in dbcontext.DoPoManages.Where(x => x.DoMgrDeleted == false)
                         join pl in dbcontext.DoDnnPalletMgrs.Where(x => x.PltDeleted == false)
                         on po.DomgrDono equals pl.PltDoNo into ljPl
                         from pl in ljPl.DefaultIfEmpty()
                         join vhld in dbcontext.VhldVehicleDetail.Where(x => x.VhldVehicleisn == vehicleIsn && x.VhldDeleted == false) on po.Id equals vhld.VhldObjectisn
                         select new PieceWeightScale()
                         {
                             DoNo = po.DomgrDono,
                             ReceivedPieces = vhld.VhldLoadedPieces,
                             ReceivedGrossWeight = pl.PltReceivedWeight,
                         });

            return query.Distinct().ToList();
        }
    }
}