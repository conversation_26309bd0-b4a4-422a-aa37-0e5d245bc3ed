using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using OutboundModule.CheckDocuments;
using System;
using System.IO.Compression;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Content;
using Volo.FileManagement.Files;
using System.Linq;

namespace ReportModule.Outbound
{
    [RemoteService(Name = "ReportModule")]
    [Area("reportModule")]
    [ControllerName("OutboundReport")]
    [Route("api/report-module/outbound/report")]
    public class ReportController : AbpController
    {
        private readonly IReportAppService _reportAppService;
        private readonly IConfiguration _configuration;
        private readonly MasterDataModule.VehiclesRegistrations.IVehiclesRegistrationRepository _vehiclesRegistrationRepositoryMaster;

        protected IFileDescriptorAppService FileDescriptorAppService { get; }

        public ReportController(IReportAppService reportAppService,
            IConfiguration configuration,
            IFileDescriptorAppService fileDescriptorAppService,
            MasterDataModule.VehiclesRegistrations.IVehiclesRegistrationRepository vehiclesRegistrationRepositoryMaster)
        {
            _reportAppService = reportAppService;
            _configuration = configuration;
            FileDescriptorAppService = fileDescriptorAppService;
            _vehiclesRegistrationRepositoryMaster = vehiclesRegistrationRepositoryMaster;
        }

        [HttpGet]
        [Route("download-daily-mining-report")]
        public async Task<IRemoteStreamContent> DownloadDailyMiningReportAsync(DownloadDailyMiningReportInput input)
        {
            // get template của BC khai thác ngày (ca trực)
            var templateFileName = _configuration[$"{CurrentTenant.Name}:TemplateExportExcel:OutboundDailyMiningReport"];
            var fileDescriptor = await FileDescriptorAppService.GetFileIdByFileNameAsync(templateFileName);
            var fileName = $"Bao_cao_khai_thac_ngay_{input.p_Date}_{DateTime.Now:HHmmss}.xlsx";

            Response.Headers.Add("Content-Disposition", $"attachment;filename=\"{fileName}\"");
            Response.Headers.Add("Accept-Ranges", "bytes");
            Response.ContentType = fileDescriptor.MimeType;

            return await _reportAppService.DownloadDailyMiningReportAsync(
                new DownloadDailyMiningReportInput()
                {
                    FileId = fileDescriptor.Id.ToString(),
                    p_Date = input.p_Date,
                    p_TimeRange = input.p_TimeRange
                });
        }

        //Báo cáo 2
        [HttpGet]
        [Route("download-list-do-ve-kho")]
        public async Task<IRemoteStreamContent> DownloadListDOVeKhoAsync(DownloadListDOVeKhoInput input)
        {
            var templateFileName = _configuration[$"{CurrentTenant.Name}:TemplateExportExcel:OutboundListDoVeKho"];
            var fileDescriptor = await FileDescriptorAppService.GetFileIdByFileNameAsync(templateFileName);
            var fileName = $"{DateTime.Now:yyyyMMdd_HHmmss}_ListDoVeKho_{input.p_FromDate.Substring(0,2)}-{input.p_ToDate.Substring(0,2)}.xlsx";

            Response.Headers.Add("Content-Disposition", $"attachment;filename=\"{fileName}\"");
            Response.Headers.Add("Accept-Ranges", "bytes");
            Response.ContentType = fileDescriptor.MimeType;

            return await _reportAppService.DownloadListDOVeKhoAsync(
                new DownloadListDOVeKhoInput()
                {
                    FileId = fileDescriptor.Id.ToString(),
                    p_FromDate = input.p_FromDate,
                    p_FromTime = input.p_FromTime,
                    p_ToDate = input.p_ToDate,
                    p_ToTime = input.p_ToTime
                });
        }

        [HttpGet]
        [Route("download-so-tong-hop-hhxk")]
        public async Task<IRemoteStreamContent> DownloadSoTongHopHHXKAsync(DownloadSoTongHopHHXKInput input)
        {
            // get template của BC sổ tổng hợp HHXK
            var templateFileName = _configuration[$"{CurrentTenant.Name}:TemplateExportExcel:DSTongHopHangXuatKho"];
            var fileDescriptor = await FileDescriptorAppService.GetFileIdByFileNameAsync(templateFileName);
            var fileName = $"{DateTime.Now:yyyyMMddHHmmss}_DSTongHopHangXuatKho.xlsx";

            Response.Headers.Add("Content-Disposition", $"attachment;filename=\"{fileName}\"");
            Response.Headers.Add("Accept-Ranges", "bytes");
            Response.ContentType = fileDescriptor.MimeType;

            return await _reportAppService.DownloadSoTongHopHHXKAsync(
                new DownloadSoTongHopHHXKInput()
                {
                    FileId = fileDescriptor.Id.ToString(),
                    p_FromDate = input.p_FromDate,
                    p_ToDate = input.p_ToDate
                });
        }

        [HttpGet]
        [Route("download-bb-nhan-va-van-chuyen-hang")]
        public async Task<IActionResult> DownloadBBNhanVaVanChuyenHangAsync(DownloadBBNhanVaVanChuyenHangInput input)
        {
            // get template của biên bản nhận và vận chuyển hàng:
            var templateFileName = _configuration[$"{CurrentTenant.Name}:TemplateExportExcel:BienBanNhanVaVanChuyenHang"];
            var fileDescriptor = await FileDescriptorAppService.GetFileIdByFileNameAsync(templateFileName);
            var streamContent = await _reportAppService.DownloadBBNhanVaVanChuyenHangAsync(
                new DownloadBBNhanVaVanChuyenHangInput()
                {
                    FileId = fileDescriptor.Id.ToString(),
                    p_FromDate = input.p_FromDate,
                    p_ToDate = input.p_ToDate,
                    p_Vendor = input.p_Vendor,
                    p_ExportType = input.p_ExportType
                });
            //var fileName = $"{DateTime.Now:yyyyMMddHHmmss}_BienBanNhanVaVanChuyenHang.xlsx";
            var zipFileName = $"BB_Nhan_Va_Van_Chuyen_Hang_SEV_{input.p_ToDate}_{DateTime.Now:HHmmss}.zip";
            if (input.p_Vendor == "VENDOR")
            {
                zipFileName = $"BB_Nhan_Va_Van_Chuyen_Hang_VENDOR_{input.p_ToDate}_{DateTime.Now:HHmmss}.zip";
            }
            Response.Headers.Add("Content-Disposition", $"attachment;filename=\"{zipFileName}\"");
            Response.Headers["Content-Type"] = "application/zip";
            Response.Headers.Add("Accept-Ranges", "bytes");
            
            return new FileStreamResult(streamContent.GetStream(), "application/zip")
            {
                FileDownloadName = zipFileName
            };
        }

        [HttpGet]
        [Route("download-bb-can-hang")]
        public async Task<IActionResult> DownloadBBCanHangAsync(DownloadBBNhanVaVanChuyenHangInput input)
        {
            // get template của biên bản nhận và vận chuyển hàng:
            var templateFileName = _configuration[$"{CurrentTenant.Name}:TemplateExportExcel:BienBanCanHang"];
            var fileDescriptor = await FileDescriptorAppService.GetFileIdByFileNameAsync(templateFileName);
            var streamContent = await _reportAppService.DownloadBBCanHangAsync(
                new DownloadBBNhanVaVanChuyenHangInput()
                {
                    FileId = fileDescriptor.Id.ToString(),
                    p_FromDate = input.p_FromDate,
                    p_ToDate = input.p_ToDate,
                    p_Vendor = input.p_Vendor,
                    p_ExportType = input.p_ExportType
                });
            //var fileName = $"{DateTime.Now:yyyyMMddHHmmss}_BienBanNhanVaVanChuyenHang.xlsx";
            
            var zipFileName = $"BB_Can_Hang_SEV_{input.p_ExportType}_{input.p_ToDate}_{DateTime.Now:HHmmss}.zip";
            if (input.p_Vendor == "VENDOR")
            {
                zipFileName = $"BB_Can_Hang_VENDOR_{input.p_ExportType}_{input.p_ToDate}_{DateTime.Now:HHmmss}.zip";
            }
            Response.Headers.Add("Content-Disposition", $"attachment;filename=\"{zipFileName}\"");
            Response.Headers["Content-Type"] = "application/zip";
            Response.Headers.Add("Accept-Ranges", "bytes");

            return new FileStreamResult(streamContent.GetStream(), "application/zip")
            {
                FileDownloadName = zipFileName
            };
        }

        [HttpGet]
        [Route("download-bb-nhan-hang-do")]
        public async Task<IRemoteStreamContent> DownloadBBNhanHangDOAsync(DownloadBBNhanVaVanChuyenHangInput input)
        {
            // get template của biên bản nhận và vận chuyển hàng:
            var templateFileName = _configuration[$"{CurrentTenant.Name}:TemplateExportExcel:BienBanNhanVaVanChuyenHang"];
            var fileDescriptor = await FileDescriptorAppService.GetFileIdByFileNameAsync(templateFileName);
            var truckUnloading = (await _vehiclesRegistrationRepositoryMaster.GetVehiclesUnLoadingExportListAsync(vehicRegId:input.p_TruckId,type:"EXPORT",truckType:"PICK UP")).ToList()[0];
            var dateUnload = truckUnloading.UnloadingArrivalDate.Value;
            //var fileName = (await _reportAppService.CountTruckUnloadingForReport(input.p_TruckId) + $"{DateTime.Now:yyyyMMddHHmmss}_DSTongHopHangXuatKho.xlsx";
            string soBienBan = await _reportAppService.CountTruckUnloadingForReport(input.p_TruckId);
            if (input.p_ExportType == "PDF")
            {
                soBienBan = soBienBan + ".pdf";
            }
            else
            {
                soBienBan = soBienBan + ".xlsx";
            }
            Response.Headers.Add("Content-Disposition", $"attachment;filename=\"{soBienBan}\"");
            Response.Headers.Add("Accept-Ranges", "bytes");
            Response.ContentType = fileDescriptor.MimeType;
            return await _reportAppService.DownloadBBNhanHangDOAsync(
                new DownloadBBNhanVaVanChuyenHangInput()
                {
                    FileId = fileDescriptor.Id.ToString(),
                    p_TruckId = input.p_TruckId,
                    p_ExportType = input.p_ExportType
                });
        }

        [HttpGet]
        [Route("download-bb-can-hang-detail")]
        public async Task<IRemoteStreamContent> DownloadBBCanHangDetailAsync(DownloadBBNhanVaVanChuyenHangInput input)
        {
            // get template của biên bản cân hàng:
            var templateFileName = _configuration[$"{CurrentTenant.Name}:TemplateExportExcel:BienBanCanHang"];
            var fileDescriptor = await FileDescriptorAppService.GetFileIdByFileNameAsync(templateFileName);
            var truckUnloading = (await _vehiclesRegistrationRepositoryMaster.GetVehiclesUnLoadingExportListAsync(vehicRegId: input.p_TruckId, type: "EXPORT", truckType: "PICK UP")).ToList()[0];
            var dateUnload = truckUnloading.UnloadingArrivalDate.Value;
            //var fileName = (await _reportAppService.CountTruckUnloadingForReport(input.p_TruckId) + $"{DateTime.Now:yyyyMMddHHmmss}_DSTongHopHangXuatKho.xlsx";
            string soBienBan = await _reportAppService.CountTruckUnloadingForReport(input.p_TruckId);
            if (input.p_ExportType == "PDF")
            {
                soBienBan = soBienBan + ".pdf";
            }
            else
            {
                soBienBan = soBienBan + ".xlsx";
            }
            Response.Headers.Add("Content-Disposition", $"attachment;filename=\"{soBienBan}\"");
            Response.Headers.Add("Accept-Ranges", "bytes");
            Response.ContentType = fileDescriptor.MimeType;
            return await _reportAppService.DownloadBBCanHangDetailAsync(
                new DownloadBBNhanVaVanChuyenHangInput()
                {
                    FileId = fileDescriptor.Id.ToString(),
                    p_TruckId = input.p_TruckId,
                    p_ExportType = input.p_ExportType
                });
        }
    }
}
