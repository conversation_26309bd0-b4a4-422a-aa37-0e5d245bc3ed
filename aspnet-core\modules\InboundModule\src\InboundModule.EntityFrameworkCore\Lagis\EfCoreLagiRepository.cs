using InboundModule.EntityFrameworkCore;
using MasterDataModule.GraiGroupAdditionalInfos;
using MasterDataModule.VehiclesRegistrations;
using Microsoft.EntityFrameworkCore;
using ShareDataModule.Fluis;
using ShareDataModule.Shared;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace InboundModule.Lagis
{
    public class EfCoreLagiRepository : EfCoreRepository<InboundModuleDbContext, Lagi, long>, ILagiRepository
    {
        public readonly IFluiRepository _fluiRepository;
        private readonly IGraiGroupAdditionalInfoRepository _graiGroupAdditionalInfoRepository;

        public EfCoreLagiRepository(IDbContextProvider<InboundModuleDbContext> dbContextProvider, IFluiRepository fluiRepository
            , IGraiGroupAdditionalInfoRepository graiGroupAdditionalInfoRepository)
            : base(dbContextProvider)
        {
            _fluiRepository = fluiRepository;
            _graiGroupAdditionalInfoRepository = graiGroupAdditionalInfoRepository;
        }

        public async Task<List<Lagi>> GetListAsync(
            string filterText = null,
            string lagiMawbPrefix = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter(await GetQueryableAsync(), filterText, lagiMawbPrefix);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? LagiConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string lagiMawbPrefix = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter(await GetDbSetAsync(), filterText, lagiMawbPrefix);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<Lagi> ApplyFilter(
            IQueryable<Lagi> query,
            string filterText,
            string lagiMawbPrefix = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.LagiMawbPrefix.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(lagiMawbPrefix), e => e.LagiMawbPrefix.Contains(lagiMawbPrefix));
        }

        private async Task<IQueryable<Hawb>> GetQueryListHawbSuggestion(string strHawb)
        {
            var query = from lagi in (await GetDbSetAsync()).WhereIf(!string.IsNullOrEmpty(strHawb), x => x.LagiHawb.Contains(strHawb.Trim().ToUpper()))
                        .Where(x => x.LagiQuantityReceived > 0 && x.LagiDeleted == false)
                        select new Hawb
                        {
                            LagiId = lagi.Id,
                            LagiHawb = lagi.LagiHawb,
                            MawbNo = lagi.LagiMawbNo,
                            MawbPrefix = lagi.LagiMawbPrefix
                        };
            return query.Distinct();
        }

        private async Task<IQueryable<Hawb>> GetQueryListHawbSuggestionDLV(string strHawb)
        {
            var dbContext = await GetDbContextAsync();
            var query = from lagi in (await GetDbSetAsync())
                        .WhereIf(!string.IsNullOrEmpty(strHawb), x => x.LagiHawb.Contains(strHawb.Trim().ToUpper()))
                        .Where(x => x.LagiQuantityReceived > 0 && x.LagiDeleted == false)
                        join shipment in dbContext.ShipmentOrders.Where(x => x.OrderType == "AIR")
                        on lagi.Id equals shipment.LagiId
                        select new Hawb
                        {
                            LagiId = lagi.Id,
                            LagiHawb = lagi.LagiHawb,
                            MawbNo = lagi.LagiMawbNo,
                            MawbPrefix = lagi.LagiMawbPrefix
                        };
            return query.Distinct();
        }

        public async Task<List<Hawb>> GetListHawbSuggestionDLVAsync(
         string strHawb,
         int maxResultCount = int.MaxValue,
         int skipCount = 0,
         CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListHawbSuggestionDLV(strHawb);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<List<Hawb>> GetListHawbSuggestionAsync(
         string strHawb,
         int maxResultCount = int.MaxValue,
         int skipCount = 0,
         CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListHawbSuggestion(strHawb);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountListHawbSuggestionAsync(
         string strHawb,
         CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListHawbSuggestion(strHawb);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> GetCountListHawbSuggestionDLVAsync(
        string strHawb,
        CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListHawbSuggestionDLV(strHawb);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        private async Task<IQueryable<HawbGroup>> GetQueryListGroupHawb(
            long? lagiId = 0,
            string prefix = null,
            long? serialNo = 0,
            string hawb = null,
            string group = null,
            string flight = null,
            string cargoTerminal = null,
            string awb = null,
            bool checkDelivery = false,
            long? warehouseId = null)
        {
            var dbContext = await GetDbContextAsync();

            var wareHouse = (from warehouse in (await GetDbContextAsync()).WareHouses.Where(x => x.Id == warehouseId && x.WarehouseActive == true)
                             select warehouse).FirstOrDefault();

            var query = (from lagi in (await GetDbSetAsync()).Where(x => x.LagiDeleted == false)
                               .WhereIf(!string.IsNullOrWhiteSpace(awb), e => e.LagiHawb.Contains(awb)
                                                                           || e.LagiMawbPrefix.Contains(awb)
                                                                           || e.LagiMawbNo.ToString().Contains(awb)
                                                                           || (e.LagiMawbPrefix + e.LagiMawbNo.ToString()).Contains(awb))
                               .WhereIf(lagiId.HasValue, e => e.Id == lagiId)
                               .WhereIf(serialNo.HasValue, e => e.LagiMawbNo == serialNo)
                               .WhereIf(!string.IsNullOrWhiteSpace(hawb), e => e.LagiHawb.Contains(hawb.Trim().ToUpper()))
                               .WhereIf(!string.IsNullOrWhiteSpace(prefix), e => e.LagiMawbPrefix.Contains(prefix.Trim()))
                               .WhereIf(!string.IsNullOrWhiteSpace(flight), e => (e.LagiLvgIn + e.LagiFlightNoIn).Contains(flight))
                               .WhereIf(!string.IsNullOrWhiteSpace(cargoTerminal), e => e.LagiShedCode == cargoTerminal)
                               .WhereIf(checkDelivery, e => e.LagiQuantityDelivered < e.LagiQuantityExpected)// Không list ra lô hàng có số kiện DLV = NPX
                               .WhereIf(warehouseId.HasValue, e => e.LagiTso == (wareHouse != null ? wareHouse.WarehouseShortCode : ""))
                         join grai in dbContext.GraiGroupAdditionalInfos
                         on lagi.Id equals grai.GraiObjectIsn
                         where !(from a in dbContext.VhldVehicleDetails where a.VhldDeleted == false && a.VhldRecordtype == "TRANSIT" select a.VhldGroupIsn)
                         .Contains(grai.GraiObjectGroupIsn)
                       && grai.GraiGroupType == "CLASS" && grai.GraiValue == "FKE"
                         select new HawbGroup
                         {
                             LagiId = lagi.Id,
                             GraiObjectIsn = grai.GraiObjectIsn,
                             MawbPrefix = lagi.LagiMawbPrefix,
                             Mawb = lagi.LagiMawbNo,
                             GroupNo = grai.GraiObjectGroupIsn,
                             Hawb = lagi.LagiHawb,
                             LagiShedCode = lagi.LagiShedCode,
                             LagiLvgIn = lagi.LagiLvgIn,
                             LagiFlightNoIn = lagi.LagiFlightNoIn,
                             Pieces = lagi.LagiQuantityReceived.ToString(),
                             Weight = lagi.LagiWeightReceived.ToString(),
                         }).Distinct();
            return query.WhereIf(!string.IsNullOrWhiteSpace(group), e => e.GroupNo == group).OrderBy(x => x.GroupNo);
        }

        private async Task<IQueryable<HawbGroup>> GetQueryListGroupHawbDLV(long? lagiId = 0,
           string prefix = null,
           long? serialNo = 0,
           string hawb = null,
           string group = null)
        {
            var dbContext = await GetDbContextAsync();
            var query = (from lagi in (await GetDbSetAsync()).Where(x => x.LagiDeleted == false)
                               .WhereIf(lagiId.HasValue, e => e.Id == lagiId)
                               .WhereIf(serialNo.HasValue, e => e.LagiMawbNo == serialNo)
                               .WhereIf(!string.IsNullOrWhiteSpace(hawb), e => e.LagiHawb.Contains(hawb.Trim().ToUpper()))
                               .WhereIf(!string.IsNullOrWhiteSpace(prefix), e => e.LagiMawbPrefix.Contains(prefix.Trim()))
                         join grai in dbContext.GraiGroupAdditionalInfos
                         on lagi.Id equals grai.GraiObjectIsn
                         join shipment in dbContext.ShipmentOrders.Where(x => x.OrderType == "AIR")
                         on lagi.Id equals shipment.LagiId
                         join vhld in dbContext.VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn
                         join vhcl in dbContext.VehiclesRegistrations on vhld.VhldVehicleisn equals vhcl.Id
                         where vhcl.VhclImportExport == "DELIVERY_PLAN" && vhld.VhldRecordtype == "SHIPMENT_ORDER"
                         where !(from a in dbContext.VhldVehicleDetails where a.VhldDeleted == false && a.VhldRecordtype == "DELIVERY" select a.VhldGroupIsn)
                            .Contains(grai.GraiObjectGroupIsn)
                            && grai.GraiValue != "FKE" && grai.GraiGroupType == "CLASS"
                         select new HawbGroup
                         {
                             LagiId = lagi.Id,
                             GraiObjectIsn = grai.GraiObjectIsn,
                             MawbPrefix = lagi.LagiMawbPrefix,
                             Mawb = lagi.LagiMawbNo,
                             GroupNo = grai.GraiObjectGroupIsn,
                             Hawb = lagi.LagiHawb,
                             UnloadingWarehouse = vhcl.VhclUnloadingWarehouse,
                             UnloadingDoor = vhcl.VhclUnloadingDoor,
                         }).Distinct();
            return query.WhereIf(!string.IsNullOrWhiteSpace(group), e => e.GroupNo == group).OrderBy(x => x.GroupNo);
        }

        private async Task<IQueryable<HawbDLV>> GetQueryListHawbDLVV1(long? lagiId = 0,
          string prefix = null,
          long? serialNo = 0,
          string hawb = null)
        {
            var dbContext = await GetDbContextAsync();
            var query = (from lagi in (await GetDbSetAsync()).Where(x => x.LagiDeleted == false)
                               .WhereIf(lagiId.HasValue, e => e.Id == lagiId)
                               .WhereIf(serialNo.HasValue, e => e.LagiMawbNo == serialNo)
                               .WhereIf(!string.IsNullOrWhiteSpace(hawb), e => e.LagiHawb.Contains(hawb.Trim().ToUpper()))
                               .WhereIf(!string.IsNullOrWhiteSpace(prefix), e => e.LagiMawbPrefix.Contains(prefix.Trim()))
                         join shipment in dbContext.ShipmentOrders.Where(x => x.OrderType == "AIR")
                         on lagi.Id equals shipment.LagiId
                         join vhld in dbContext.VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn
                         join vhcl in dbContext.VehiclesRegistrations on vhld.VhldVehicleisn equals vhcl.Id
                         where vhcl.VhclImportExport == "DELIVERY_PLAN" && vhld.VhldRecordtype == "SHIPMENT_ORDER"
                         select new HawbDLV
                         {
                             LagiId = lagi.Id,
                             MawbPrefix = lagi.LagiMawbPrefix,
                             Mawb = lagi.LagiMawbNo,
                             Hawb = lagi.LagiHawb,
                             Pieces = lagi.LagiQuantityReceived,
                             Weight = lagi.LagiWeightReceived,
                             UnloadingWarehouse = vhcl.VhclUnloadingWarehouse,
                             UnloadingDoor = vhcl.VhclUnloadingDoor,
                         }).Distinct();
            return query.Where(x => x.Pieces > dbContext.VehicleExploitHistories.Where(a => a.VehObjectIsn == x.LagiId && a.IsDeleted == false && a.VehType == "DELIVERY").Sum(x => x.VehQuantity));
        }

        public async Task<List<HawbGroup>> GetListGroupHawbAsync(
            long? lagiId = 0,
            string prefix = null,
            long? serialNo = 0,
            string hawb = null,
            string group = null,
            string flight = null,
            string cargoTerminal = null,
            string awb = null,
            bool checkDelivery = false,
            long? warehouseId = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListGroupHawb(lagiId, prefix, serialNo, hawb, group, flight, cargoTerminal, awb, checkDelivery, warehouseId);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<List<HawbGroup>> GetListGroupHawbDLVAsync(
            long? lagiId = 0,
            string prefix = null,
            long? serialNo = 0,
            string hawb = null,
            string group = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListGroupHawbDLV(lagiId, prefix, serialNo, hawb, group);
            var datas = await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            foreach (var item in datas)
            {
                item.Pieces = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.GraiObjectIsn)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "PIECES")
                                    .Where(x => x.GraiObjectGroupIsn == item.GroupNo)
                                    .Select(x => x.GraiNumericValue.ToString(CultureInfo.InvariantCulture))
                                    .SingleOrDefault();
                item.Weight = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.GraiObjectIsn)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "WEIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.GroupNo)
                                    .Select(x => x.GraiNumericValue.ToString(CultureInfo.InvariantCulture))
                                    .SingleOrDefault();
            }

            return datas;
        }

        public async Task<List<HawbDLV>> GetListHawbDLVV1Async(
            long? lagiId = 0,
            string prefix = null,
            long? serialNo = 0,
            string hawb = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListHawbDLVV1(lagiId, prefix, serialNo, hawb);
            var datas = await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            return datas;
        }

        public async Task<long> GetCountListGroupHawbAsync(
           long? lagiId = 0,
           string prefix = null,
           long? serialNo = 0,
           string hawb = null,
           string group = null,
           string flight = null,
           string cargoTerminal = null,
           string awb = null,
           bool checkDelivery = false,
           long? warehouseId = null,
           CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListGroupHawb(lagiId, prefix, serialNo, hawb, group, flight, cargoTerminal, awb, checkDelivery, warehouseId);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> GetCountListGroupHawbDLVAsync(
          long? lagiId = 0,
          string prefix = null,
          long? serialNo = 0,
          string hawb = null,
          string group = null,
          CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListGroupHawbDLV(lagiId, prefix, serialNo, hawb, group);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> GetCountListHawbDLVV1Async(
        long? lagiId = 0,
        string prefix = null,
        long? serialNo = 0,
        string hawb = null,
        CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListHawbDLVV1(lagiId, prefix, serialNo, hawb);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<GraiGroupAdditionalInfoVsLagi> GraiGroupAdditionalInfoVsLagi(string GraiObjectGroupIsn, long ObjectIsn)
        {
            //var queryPieces = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
            //                  where g.GraiGroupType == "PIECES"
            //                  where g.GraiObjectGroupIsn == GraiObjectGroupIsn && g.GraiObjectIsn == ObjectIsn
            //                  select g.GraiNumericValue;
            //var queryWeight = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
            //                  where g.GraiGroupType == "WEIGHT"
            //                  where g.GraiObjectGroupIsn == GraiObjectGroupIsn && g.GraiObjectIsn == ObjectIsn
            //                  select g.GraiNumericValue;

            var queryLabs = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                            join l in (await GetDbSetAsync())
                            on g.GraiObjectIsn equals l.Id
                            where g.GraiObjectGroupIsn == GraiObjectGroupIsn
                            && g.GraiObjectIsn == ObjectIsn && l.LagiDeleted == false
                            select new GraiGroupAdditionalInfoVsLagi
                            {
                                LagiMawbPrefix = l.LagiMawbPrefix,
                                LagiMawbSerialNo = l.LagiMawbNo,
                                Pieces = l.LagiQuantityReceived,
                                Weight = l.LagiWeightReceived,
                                GraiObjectIsn = g.GraiObjectIsn,
                            };

            return queryLabs.FirstOrDefault();
        }

        public async Task<LagiInfo> GetLagiFlightInfo(long ObjectIsn)
        {
            var query = from f in (await GetDbSetAsync())
                        where f.Id == ObjectIsn
                        select new LagiInfo
                        {
                            LagiCustomerNoAirline = f.LagiCustomerNoAirline,
                            LagiFlightNoIn = f.LagiFlightNoIn,
                            LagiFlightDateIn = f.LagiFlightDateIn
                        };
            return query.FirstOrDefault();
        }

        private async Task<IQueryable<MawbByVehicleIsn>> GetQueryMawbByVehicleIsn(long VehicleIsn, string Hawb)
        {
            return (from v in (await GetDbContextAsync()).VhldVehicleDetails
                    join l in (await GetDbSetAsync())
                    .WhereIf(!string.IsNullOrWhiteSpace(Hawb), x => x.LagiHawb.Contains(Hawb.ToUpper()))
                    on v.VhldObjectisn equals l.Id
                    where v.VhldVehicleisn == VehicleIsn && v.VhldLoadedPieces != 0 && v.VhldDeleted == false
                    //orderby v.VhldCreateDate descending
                    select new MawbByVehicleIsn
                    {
                        LagiId = v.VhldObjectisn,
                        VehicleRegID = VehicleIsn,
                        AwbSerial = v.VhldAwbserial,
                        MawbPrefix = v.VhldAwbprefix,
                        MawbSerial = v.VhldAwbserial,
                        Status1 = l.LagiDateStatus1set,
                        Status2 = l.LagiDateStatus2set,
                        Status3 = l.LagiDateStatus3set,
                        Status4 = l.LagiDateStatus4set,
                        Hawb = l.LagiHawb,
                        Remark = l.LagiShipmentRemarks,
                        Pieces = l.LagiQuantityReceived,
                        Weight = l.LagiWeightReceived,
                        Unload = v.VhldUnloading,
                        CreateDate = v.VhldCreateDate
                    }).Distinct();
        }

        private async Task<IQueryable<HawbByVehicleIsn>> GetQueryHawbByVehicleIsn(long VehicleIsn, string Hawb)
        {
            return (from v in (await GetDbContextAsync()).VhldVehicleDetails
                    join l in (await GetDbSetAsync())
                    .WhereIf(!string.IsNullOrWhiteSpace(Hawb), x => x.LagiHawb.Contains(Hawb.ToUpper()))
                    on v.VhldObjectisn equals l.Id
                    where v.VhldVehicleisn == VehicleIsn && v.VhldLoadedPieces != 0 && v.VhldDeleted == false
                    //orderby v.VhldCreateDate descending
                    select new HawbByVehicleIsn
                    {
                        LagiId = v.VhldObjectisn,
                        VehicleRegID = VehicleIsn,
                        AwbSerial = v.VhldAwbserial,
                        MawbPrefix = v.VhldAwbprefix,
                        MawbSerial = v.VhldAwbserial,
                        Hawb = l.LagiHawb,
                        Remark = l.LagiShipmentRemarks,
                        Pieces = l.LagiQuantityReceived,
                        Weight = l.LagiWeightReceived,
                        Unload = v.VhldUnloading,
                        CreateDate = v.VhldCreateDate
                    }).Distinct();
        }

        public async Task<List<MawbByVehicleIsn>> GetMawbByVehicleIsnAsync(
            long vehicleRegId = 0,
            string hawb = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryMawbByVehicleIsn(vehicleRegId, hawb);
            var datas = await query.OrderBy(x => x.MawbPrefix).ThenBy(x => x.MawbSerial).ThenBy(x => x.Hawb).PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);


            foreach (var item in datas)
            {
                var queryPieces = (from dt in (await GetDbContextAsync()).VhldVehicleDetails where dt.VhldObjectisn == item.LagiId && dt.VhldDeleted == false && dt.VhldVehicleisn == vehicleRegId select dt);
                if (queryPieces.Count() != 0)
                {
                    //item.Pieces = (int)queryPieces.Sum(x => x.VhldQtyreceived);
                    item.PiecesLoaded = (int)queryPieces.Sum(x => x.VhldLoadedPieces);
                    item.PiecesUnload = (int)queryPieces.Sum(x => x.VhldUnloadingPieces);
                    //item.Weight = queryPieces.Sum(x => x.VhldWeightReceived);
                    item.WeightLoaded = queryPieces.Sum(x => x.VhldLoadedWeight);
                    item.WeightUnload = queryPieces.Sum(x => x.VhldUnloadingWeight);
                    item.Groups = queryPieces.Count();
                    item.GroupsUnload = queryPieces.Count(x => x.VhldUnloading == true);
                    item.FlightNo = queryPieces.FirstOrDefault().VhldFlightno;
                    if (queryPieces.FirstOrDefault().VhldFlightDate > 0)
                        item.FlightDate = CommonFunction.ConverNumberToDate(queryPieces.FirstOrDefault().VhldFlightDate);
                    foreach (var itemdt in queryPieces)
                    {
                        item.Location = GetLocationVehicleDetail(itemdt.VhldObjectisn).Result;
                    }
                }
            }
            return datas;
        }

        public async Task<long> GetCountMawbByVehicleIsnAsync(
            long vehicleRegId = 0,
            string hawb = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryMawbByVehicleIsn(vehicleRegId, hawb);

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> GetCountHawbByVehicleIsnAsync(
            long vehicleRegId = 0,
            string hawb = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryHawbByVehicleIsn(vehicleRegId, hawb);

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<HawbByVehicleIsn>> GetHawbByVehicleIsnAsync(
            long vehicleRegId = 0,
            string hawb = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryHawbByVehicleIsn(vehicleRegId, hawb);
            var datas = await query.OrderBy(x => x.MawbPrefix).ThenBy(x => x.MawbSerial).ThenBy(x => x.Hawb).PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);

            var listGroupByVehicle = await (from grai in (await GetDbContextAsync())
                                    .GraiGroupAdditionalInfos.Where(x => x.GraiGroupCode == "RECEIVED" && x.GraiGroupType == "VEHICLE_ISN" && x.GraiNumericValue == vehicleRegId)
                                    .OrderByDescending(e => e.GraiObjectGroupIsn)
                                            select new HawbByVehicleIsn
                                            {
                                                LagiId = grai.GraiObjectIsn,
                                                GroupIsn = grai.GraiObjectGroupIsn
                                            }).Distinct()
                    .OrderByDescending(x => x.GroupIsn).PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            foreach (var item in listGroupByVehicle)
            {
                // get số pcs đã add group
                item.Pieces = (int?)(await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.LagiId)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "PIECES")
                                    .Where(x => x.GraiObjectGroupIsn == item.GroupIsn)
                                    .Select(x => x.GraiNumericValue)
                                    .SingleOrDefault();
            }

            foreach (var item in datas)
            {
                var queryPieces = (from dt in (await GetDbContextAsync()).VhldVehicleDetails where dt.VhldObjectisn == item.LagiId && dt.VhldDeleted == false && dt.VhldVehicleisn == vehicleRegId select dt);
                if (queryPieces.Count() != 0)
                {
                    item.PiecesLoaded = (int)queryPieces.Sum(x => x.VhldLoadedPieces);
                    item.PiecesUnload = (int)queryPieces.Sum(x => x.VhldUnloadingPieces);
                    item.Groups = queryPieces.Count();
                    item.GroupsUnload = queryPieces.Count(x => x.VhldUnloading == true);
                }

                item.PiecesHaveGroup = (int)listGroupByVehicle.Where(x => x.LagiId == item.LagiId).Select(x => x.Pieces).Sum();
            }
            return datas;
        }

        private async Task<IQueryable<VehicleDetail>> GetQueryVehicleDetail(
            long vehicleRegId,
            string prefix = null,
            int? serial = null,
            string grade = null)
        {
            return (from v in (await GetDbContextAsync()).VhldVehicleDetails
                      .Where(x => x.VhldVehicleisn == vehicleRegId && x.VhldDeleted == false)
                      .WhereIf(!string.IsNullOrWhiteSpace(prefix), x => x.VhldAwbprefix == prefix)
                      .WhereIf(serial.HasValue, x => x.VhldAwbserial == serial)
                      .WhereIf(!string.IsNullOrWhiteSpace(grade), x => x.VhldDeliveryDoor == grade)
                    join l in (await GetDbSetAsync())
                    on v.VhldObjectisn equals l.Id
                    join vt in (await GetDbContextAsync()).VehiclesRegistrations
                    on v.VhldVehicleisn equals vt.Id
                    where v.VhldDeleted == false && v.VhldLoadedPieces != 0
                    join k in (await GetDbContextAsync()).Kunds
                    on l.LagiConsigneeNumber equals k.Id into kund
                    from c in kund.DefaultIfEmpty()
                    join pod in (await GetDbContextAsync()).PodDetails
                    on v.VhldPodIsn equals pod.Id into pd
                    from pod in pd.DefaultIfEmpty()
                    select new VehicleDetail
                    {
                        Id = v.Id,
                        ObjectIsn = v.VhldObjectisn,
                        VehicleRegID = v.VhldVehicleisn,
                        Mawb = v.VhldAwbprefix + "-" + v.VhldAwbserial.ToString("D8"),
                        VhldAwbprefix = v.VhldAwbprefix,
                        VhldAwbserial = v.VhldAwbserial,
                        Hawb = l.LagiHawb,
                        AwbSerial = v.VhldAwbserial,
                        PiecesExpected = v.VhldQtyexpected,
                        WeightExpected = v.VhldWeightExpected,
                        PiecesReceived = v.VhldLoadedPieces == null ? 0 : (int)v.VhldLoadedPieces,
                        WeightReceived = v.VhldLoadedWeight == null ? 0 : (decimal)v.VhldLoadedWeight,
                        PiecesUnload = v.VhldUnloadingPieces == null ? 0 : (int)v.VhldUnloadingPieces,
                        WeightUnload = v.VhldUnloadingWeight == null ? 0 : (decimal)v.VhldUnloadingWeight,
                        PiecesLoaded = v.VhldLoadedPieces == null ? 0 : (int)v.VhldLoadedPieces,
                        WeightLoaded = v.VhldLoadedWeight == null ? 0 : (decimal)v.VhldLoadedWeight,
                        Destination = l.LagiAwbOrigin,
                        Forwarder = c.Kund3letterCode,
                        GroupIsn = v.VhldGroupIsn,
                        Status = (v.VhldQtyexpected == v.VhldQtyreceived) ? "LOADED" : "UNLOADED",
                        Unload = (bool)v.VhldUnloading,
                        Grade = v.VhldDeliveryDoor,
                        DeliveryWarehouse = v.VhldDeliveryWarehouse,
                        PodNumber = vt.VhclPodNumber,
                        //PodSubNumber = v.VhldPodSubNumber,
                        PodSubNumber = pod.VhlpPodNumber,
                        LagiId = l.Id,
                        VhclSealNumber = vt.VhclSealNumber,
                        VehicRegNo = vt.VehicRegNo,
                        VhclArrivedFactoryDate = vt.VhclArrivedFactoryDate,
                        VhclTransitToFactoryDate = vt.VhclTransitToFactoryDate,
                        Remarks = l.LagiShipmentRemarks
                    });
        }

        public async Task<string> GetLocationVehicleDetail(long objectIsn = 0)
        {
            string locationValue = "";
            var query = (from location in (await GetDbContextAsync()).LocsLocations.Where(x => !x.LocsDeleted.Value && x.LocsObjectIsn == objectIsn)
                         join locationName in (await GetDbContextAsync()).SslpPhysicalLocations on location.LocsPhysicalIsn equals locationName.Id into locationNameGroups
                         from locationNameGroup in locationNameGroups.DefaultIfEmpty()
                         select new
                         {
                             Location = " " + locationNameGroup.SslpRackRow + locationNameGroup.SslpRackLocation + " - " + locationNameGroup.SslpRackHeight,
                         }).Distinct();
            if (query.Any())
            {
                locationValue = string.Join(',', query.Where(x => !string.IsNullOrWhiteSpace(x.Location)).Select(x => x.Location).Distinct());
            }
            return locationValue;
        }

        public async Task<List<VehicleDetail>> GetVehicleDetailAsync(
            long vehicleRegId = 0,
            long awbSerial = 0,
            bool isPlan = true,
            string hawb = null,
            string prefix = null,
            int? serial = null,
            string grade = null,
            int? podSubNumber = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehicleDetail(vehicleRegId, prefix, serial, grade);
            query = query.WhereIf(awbSerial != 0, x => x.AwbSerial == awbSerial)
                         .WhereIf(!string.IsNullOrWhiteSpace(hawb), x => x.Hawb == hawb)
                         .WhereIf(podSubNumber.HasValue, x => x.PodSubNumber.Equals(podSubNumber.ToString()))
                         .OrderBy(x => x.VhldAwbprefix).ThenBy(x => x.VhldAwbserial).ThenBy(x => x.Hawb);

            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<SumHawb> GetSumVehicleDetailAsync(
            long vehicleRegId = 0,
            bool isPlan = true,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehicleDetail(vehicleRegId);
            var items = await query.ToListAsync(cancellationToken);
            if (items.Count == 0)
            {
                return new SumHawb();
            }

            return new()
            {
                Awb = items.GroupBy(g => g.Mawb).Count(),
                Hawb = items.Where(x => !string.IsNullOrEmpty(x.Hawb)).GroupBy(g => g.Hawb).Count(),
                HawbUnload = items.Where(x => !string.IsNullOrEmpty(x.Hawb) && x.PiecesUnload > 0).GroupBy(g => g.Hawb).Count(),
                Group = items.Count,
                GroupUnload = items.Count(x => x.Unload),
                PiciesExpected = items.Sum(x => x.PiecesExpected),
                WeightExpected = items.Sum(x => x.WeightExpected),
                PiciesReceived = items.Sum(x => x.PiecesReceived),
                WeightReceived = items.Sum(x => x.WeightReceived),
                PiciesUnload = items.Sum(x => x.PiecesUnload),
                WeightUnload = items.Sum(x => x.WeightUnload),
                PiciesLoaded = items.Sum(x => x.PiecesLoaded),
                WeightLoaded = items.Sum(x => x.WeightLoaded),
                VhclSealNumber = items.FirstOrDefault().VhclSealNumber,
                VehicRegNo = items.FirstOrDefault().VehicRegNo,
                VhclArrivedFactoryDate = items.FirstOrDefault().VhclArrivedFactoryDate,
                VhclTransitToFactoryDate = items.FirstOrDefault().VhclTransitToFactoryDate,
                PcsPodTotal = items.Where(x => x.PodSubNumber != null).Sum(x => x.PiecesReceived),
            };
        }

        public async Task<long> GetCountVehicleDetailAsync(
            long vehicleRegId = 0,
            long awbSerial = 0,
            bool isPlan = true,
            string hawb = null,
            string prefix = null,
            int? serial = null,
            string grade = null,
            int? podSubNumber = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehicleDetail(vehicleRegId, prefix, serial, grade);
            query = query.WhereIf(awbSerial != 0, x => x.AwbSerial == awbSerial)
                         .WhereIf(!string.IsNullOrWhiteSpace(hawb), x => x.Hawb == hawb)
                         .WhereIf(podSubNumber.HasValue, x => x.PodSubNumber.Equals(podSubNumber.ToString()));

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> RemoveAllGroupByHawb(long vehicleRegId = 0, string listLagiId = null)
        {
            int result = 0;
            var dbContext = await GetDbContextAsync();
            if (vehicleRegId > 0)
            {
                string[] listId = listLagiId.Split(',');
                foreach (var item in listId)
                {
                    if (!string.IsNullOrEmpty(item))
                    {
                        long Id = long.Parse(item);
                        //dbContext.VhldVehicleDetails.RemoveRange(dbContext.VhldVehicleDetails.Where(x => x.VhldObjectisn == Id && x.VhldVehicleisn == vehicleRegId));
                        var lstVhldVehicleDetails = dbContext.VhldVehicleDetails.Where(x => x.VhldObjectisn == Id && x.VhldVehicleisn == vehicleRegId && x.VhldDeleted == false).ToList();
                        lstVhldVehicleDetails.ForEach(a => a.VhldDeleted = true);
                        var queryHistory = (from dt in (await GetDbContextAsync()).VehicleExploitHistories where dt.VehVehicleIsn == vehicleRegId && dt.IsDeleted == false && dt.VehObjectIsn == Id select dt).ToList();
                        queryHistory.ForEach(a => a.IsDeleted = true);
                        dbContext.SaveChanges();
                        result++;
                    }
                }
            }

            return result;
        }

        public async Task<long> UnloadAllGroupByHawb(long vehicleRegId = 0, string listLagiId = null)
        {
            int result = 0;
            var dbContext = await GetDbContextAsync();
            if (vehicleRegId > 0)
            {
                string[] listId = listLagiId.Split(',');
                foreach (var item in listId)
                {
                    if (!string.IsNullOrEmpty(item))
                    {
                        long Id = long.Parse(item);
                        var listVhldDetail = dbContext.VhldVehicleDetails.Where(x => x.VhldObjectisn == Id && x.VhldVehicleisn == vehicleRegId && x.VhldDeleted == false).ToList();
                        listVhldDetail.ForEach(a => a.VhldUnloading = true);
                        dbContext.SaveChanges();
                        result++;
                    }
                }
            }

            return result;
        }

        public async Task<long> RemoveManyGroup(long vehicleRegId = 0, string listGroup = null)
        {
            int result = 0;
            var dbContext = await GetDbContextAsync();
            if (vehicleRegId > 0)
            {
                string[] listGroupArr = listGroup.Split(',');
                var query = (from dt in (await GetDbContextAsync()).VhldVehicleDetails where dt.VhldVehicleisn == vehicleRegId && dt.VhldDeleted == false && listGroupArr.Contains(dt.VhldGroupIsn) select dt).ToList();
                if (query != null)
                {
                    //dbContext.VhldVehicleDetails.RemoveRange(query);
                    query.ForEach(a => a.VhldDeleted = true);
                    dbContext.SaveChanges();
                    result = 1;
                }
            }

            return result;
        }

        public async Task<long> UnloadManyGroup(long vehicleRegId = 0, string listGroup = null)
        {
            int result = 0;
            var dbContext = await GetDbContextAsync();
            if (vehicleRegId > 0)
            {
                foreach (var item in listGroup.Split(','))
                {
                    var groupIsn = item.Split('-')[0];
                    var vehicleDetail = (from dt in (await GetDbContextAsync()).VhldVehicleDetails where dt.VhldVehicleisn == vehicleRegId && dt.VhldDeleted == false && dt.VhldGroupIsn == groupIsn select dt).FirstOrDefault();
                    var pcsUnload = int.Parse(item.Split('-')[1]);
                    var weightUnload = (pcsUnload * (vehicleDetail.VhldWeightReceived)) / (vehicleDetail.VhldQtyreceived);
                    vehicleDetail.VhldUnloadingPieces = pcsUnload;
                    vehicleDetail.VhldUnloadingWeight = weightUnload;
                    vehicleDetail.VhldUnloading = true;
                    dbContext.SaveChanges();
                }
                result = 1;
            }
            return result;
        }

        public async Task<long> UpdateLocationManyGroup(long vehicleRegId = 0, string listGroup = null, long locationId = 0)
        {
            int result = 0;
            var dbContext = await GetDbContextAsync();
            if (vehicleRegId > 0)
            {
                string[] listGroupArr = listGroup.Split(',');
                var query = (from dt in (await GetDbContextAsync()).VhldVehicleDetails where dt.VhldVehicleisn == vehicleRegId && dt.VhldDeleted == false && listGroupArr.Contains(dt.VhldGroupIsn) select dt).ToList();
                query.ForEach(a => a.VhldPhysicalLocationIsn = locationId);
                dbContext.SaveChanges();
                result = 1;
            }
            return result;
        }

        private async Task<IQueryable<HawbGroup>> GetQueryListGroupByLagiId(long vehicleRegId)
        {
            return from d in (await GetDbContextAsync()).VhldVehicleDetails
                   join l in (await GetDbSetAsync())
                   on d.VhldObjectisn equals l.Id
                   where d.VhldVehicleisn == vehicleRegId && d.VhldDeleted == false && l.LagiDeleted == false
                   select new HawbGroup
                   {
                       GroupNo = d.VhldGroupIsn,
                       Mawb = d.VhldAwbserial,
                       MawbPrefix = d.VhldAwbprefix,
                       Pieces = d.VhldLoadedPieces.ToString(),
                       PiecesUnload = d.VhldUnloadingPieces.ToString(),
                       Weight = d.VhldLoadedWeight.ToString(),
                       Hawb = l.LagiHawb,
                       LagiId = l.Id,
                       Unload = (bool)d.VhldUnloading,
                       LocationId = d.VhldPhysicalLocationIsn,
                       GraiObjectIsn = l.Id
                   };
        }

        public async Task<List<HawbGroup>> GetListGroupByLagiIdAsync(
            string prefix = null,
            string hawb = null,
            long? serialNo = 0,
            string group = null,
            long vehicleRegId = 0,
            bool? unLoad = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListGroupByLagiId(vehicleRegId);
            query = query.OrderBy(x => x.MawbPrefix).ThenBy(x => x.Mawb).ThenBy(x => x.Hawb)
                       .WhereIf(serialNo != null, e => e.Mawb == serialNo)
                       .WhereIf(!string.IsNullOrWhiteSpace(prefix), e => e.MawbPrefix.Contains(prefix.Trim()))
                       .WhereIf(!string.IsNullOrWhiteSpace(hawb), e => e.Hawb.Contains(hawb.Trim()))
                       .WhereIf(unLoad != null, x => x.Unload == unLoad)
                       .WhereIf(!string.IsNullOrWhiteSpace(group), e => e.GroupNo == group);
            var data = await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            foreach (var item in data)
            {
                if (item.LocationId != null)
                {
                    var location = from l in (await GetDbContextAsync()).SslpPhysicalLocations where l.Id == item.LocationId select l;
                    if (location.Count() > 0)
                    {
                        item.Location = location.FirstOrDefault().SslpRackRow;
                        if (location.FirstOrDefault().SslpLogicalIsn != 0)
                        {
                            long zoneId = location.FirstOrDefault().SslpLogicalIsn;
                            var zone = from z in (await GetDbContextAsync()).SsllLogicalLocations where z.Id == zoneId select z.SsllDescription;
                            if (zone != null)
                            {
                                item.Zone = zone.FirstOrDefault();
                            }
                        }
                    }
                    else
                    {
                        item.Location = " ";
                        item.Zone = " ";
                    }
                }
            }

            return data;
        }

        public async Task<long> GetCountGroupByLagiIdAsync(
            string prefix = null,
            string hawb = null,
            long? serialNo = 0,
            string group = null,
            long vehicleRegId = 0,
            bool? unLoad = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListGroupByLagiId(vehicleRegId);
            query = query
                       .WhereIf(serialNo != null, e => e.Mawb == serialNo)
                       .WhereIf(!string.IsNullOrWhiteSpace(prefix), e => e.MawbPrefix.Contains(prefix.Trim()))
                       .WhereIf(!string.IsNullOrWhiteSpace(hawb), e => e.MawbPrefix.Contains(hawb.Trim()))
                       .WhereIf(unLoad != null, x => x.Unload == unLoad)
                       .WhereIf(!string.IsNullOrWhiteSpace(group), e => e.GroupNo == group).OrderBy(x => x.GroupNo);

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> GetCountAWBDetailAsync(
            long? id = null,
            string mawbPrefix = null,
            int? mawbSerial = null,
            string shipperName = null,
            string agentName = null,
            string airline = null,
            string flightNumber = null,
            string hawb = null,
            string flightDate = null,
            bool deleted = false,
            CancellationToken cancellationToken = default)
        {
            var query = await AwbDetailQueryAsync(id, mawbPrefix, mawbSerial, shipperName, agentName, airline, flightNumber, hawb, flightDate, deleted);

            return await query.Distinct().LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<AWBDetailModel>> GetListAWBDetailAsync(
            long? id = null,
            string mawbPrefix = null,
            int? mawbSerial = null,
            string shipperName = null,
            string agentName = null,
            string airline = null,
            string flightNumber = null,
            string hawb = null,
            string flightDate = null,
            bool deleted = false,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await AwbDetailQueryAsync(id, mawbPrefix, mawbSerial, shipperName, agentName, airline, flightNumber, hawb, flightDate, deleted);
            return await query.Distinct().PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        protected virtual async Task<IQueryable<AWBDetailModel>> AwbDetailQueryAsync(
            long? id = null,
            string mawbPrefix = null,
            int? mawbSerial = null,
            string shipperName = null,
            string agentName = null,
            string airline = null,
            string flightNumber = null,
            string hawb = null,
            string flightDate = null,
            bool deleted = false)
        {
            int flightDateNumber = 0;
            if (!string.IsNullOrWhiteSpace(flightDate))
            {
                flightDateNumber = CommonFunction.ConverDateStringToNumber(flightDate);
            }

            if (!string.IsNullOrWhiteSpace(agentName) && agentName.Split("-").Length == 2)
            {
                agentName = agentName.Split("-")[1].Trim();
            }

            if (!string.IsNullOrWhiteSpace(shipperName) && shipperName.Split("-").Length == 2)
            {
                shipperName = shipperName.Split("-")[1].Trim();
            }

            return from lagi in (await GetDbSetAsync()).OrderByDescending(x => x.LagiMawbNo)
                                                           .WhereIf(!deleted, x => x.LagiDeleted == false)
                                                           .WhereIf(id.HasValue, x => x.Id.Equals(id))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(mawbPrefix), x => x.LagiMawbPrefix.Contains(mawbPrefix.ToUpper().Trim()))
                                                           .WhereIf(mawbSerial.HasValue, e => e.LagiMawbNo.ToString().Contains(mawbSerial.ToString().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(shipperName), x => x.LagiShipperName.ToUpper().Trim().Equals(shipperName.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(agentName), x => x.LagiNotifyName.ToUpper().Trim().Equals(agentName.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(airline), x => x.LagiLvgIn.ToUpper().Trim().Equals(airline.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightNumber), x => x.LagiFlightNoIn.Equals(flightNumber.Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(hawb), x => x.LagiHawb.Contains(hawb.Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightDate), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn == flightDateNumber)
                   select new AWBDetailModel
                   {
                       Id = lagi.Id,
                       Mawb = lagi.LagiMawbPrefix + '-' + lagi.LagiMawbNo,
                       LagiMawbPrefix = lagi.LagiMawbPrefix,
                       LagiMawbSerialNo = lagi.LagiMawbNo,
                       Hawb = lagi.LagiHawb,
                       ChargableWeight = lagi.LagiChargableWeight,
                       Pcs = lagi.LagiQuantityExpected,
                       GrossWeight = lagi.LagiWeightExpected,
                       Origin = lagi.LagiAwbOrigin,
                       Dest = lagi.LagiAwbDest,
                       Npr = lagi.LagiQuantityReceived,
                       Gwr = lagi.LagiWeightReceived,
                       LagiDeleted = lagi.LagiDeleted,
                       Flight = lagi.LagiLvgIn + lagi.LagiFlightNoIn,
                       BookFlightDate = lagi.LagiFlightDateIn,
                       Agent = lagi.LagiNotifyName,
                       LagiMasterIdentNo = lagi.LagiMasterIdentNo
                   };
        }

        public async Task<SummaryAWB> GetSummaryAwbAsync(
           string[] mawbHawbs, string shipperName = null, string agentName = null, string airline = null, string flightNumber = null, string flightDateFrom = null, string flightDateTo = null, bool deleted = false, CancellationToken cancellationToken = default)
        {
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            if (!string.IsNullOrWhiteSpace(agentName) && agentName.Split("-").Length == 2)
            {
                agentName = agentName.Split("-")[1].Trim();
            }

            if (!string.IsNullOrWhiteSpace(shipperName) && shipperName.Split("-").Length == 2)
            {
                shipperName = shipperName.Split("-")[1].Trim();
            }

            //Chia mảng filter thành 2 phần là search theo mawb và hawb
            var mawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') != -1 && x.IndexOf('/') == -1);
            var hawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('/') != -1 && x.IndexOf('/') != -1);
            var onlyHawbNumbers = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') == -1 && x.IndexOf('/') == -1);
            var unknows = mawbHawbs.Where(x => x.IndexOf('?') != -1);

            //lấy lagi có cả mawb và hawb
            var lagiMawbHawb = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo != 0)
                                select new
                                {
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix
                                }).Distinct();

            //loại trừ những lagi có cả mawb và hawb
            var lagiOnlyMawb = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo == 0)
                                .Where(x => !lagiMawbHawb.Any(y => x.LagiMawbNo == y.LagiMawbNo && x.LagiMawbPrefix == y.LagiMawbPrefix))
                               select new
                               {
                                   lagi.Id
                               };

            var lagiKundQuery = from lagi in (await GetDbSetAsync()).Where(x => x.LagiMasterIdentNo != 0 || lagiOnlyMawb.Any(y => x.Id == y.Id))
                               .WhereIf(!deleted, x => x.LagiDeleted == false)
                                                              .Where(x =>
                                                              (mawbs.Count() == 0 && hawbs.Count() == 0 && onlyHawbNumbers.Count() == 0 && unknows.Count() == 0)
                                                              || (mawbs.Count() > 0 && mawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString()))
                                                              || (hawbs.Count() > 0 && hawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString() + '/' + x.LagiHawb))
                                                              || (onlyHawbNumbers.Count() > 0 && onlyHawbNumbers.Contains(x.LagiHawb))
                                                              || (unknows.Count() > 0 &&
                                                              (
                                                                unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains("?-?/" + x.LagiHawb)
                                                                || unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/?")
                                                              )
                                                              ))
                                                             .WhereIf(!string.IsNullOrWhiteSpace(shipperName), x => x.LagiShipperName.ToUpper().Trim().Equals(shipperName.ToUpper().Trim()))
                                                             .WhereIf(!string.IsNullOrWhiteSpace(agentName), x => x.LagiNotifyName.ToUpper().Trim().Equals(agentName.ToUpper().Trim()))
                                                             .WhereIf(!string.IsNullOrWhiteSpace(airline), x => x.LagiLvgIn.ToUpper().Trim().Equals(airline.ToUpper().Trim()))
                                                             .WhereIf(!string.IsNullOrWhiteSpace(flightNumber), x => x.LagiFlightNoIn.Equals(flightNumber.Trim()))
                                                             .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                                join kund in (await GetDbContextAsync()).Kunds on lagi.LagiCurrentAgent.Value equals kund.Id into kundGroups
                                from kundGroup in kundGroups.DefaultIfEmpty()
                                group kundGroup by new { lagi.LagiMasterIdentNo } into lagiByLagiMasterIdentNoGroups
                                select new
                                {
                                    LagiMasterIdentNo = lagiByLagiMasterIdentNoGroups.Key.LagiMasterIdentNo,
                                    NumberOfOnlyMawb = lagiByLagiMasterIdentNoGroups.Key.LagiMasterIdentNo == 0 ? lagiByLagiMasterIdentNoGroups.Count() : 0,
                                };

            var numberOfOnlyMawb = lagiKundQuery.Where(x => x.LagiMasterIdentNo == 0).SingleOrDefault()?.NumberOfOnlyMawb ?? 0;
            var numberOfMawbWithHawb = lagiKundQuery.Where(x => x.LagiMasterIdentNo != 0).Count();
            var numberOfMawb = numberOfOnlyMawb + numberOfMawbWithHawb;

            return new SummaryAWB()
            {
                Mawb = numberOfMawb,
            };
        }

        public async Task<long> GetCountShipmentAsync(
            long? id,
            string groupNo = null,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = from grai in (await GetDbContextAsync())
                    .GraiGroupAdditionalInfos.Where(x => x.GraiGroupCode == "RECEIVED")
                    .WhereIf(id.HasValue, e => e.GraiObjectIsn == id)
                    .WhereIf(!string.IsNullOrWhiteSpace(groupNo), e => e.GraiObjectGroupIsn.Equals(groupNo))
                    .OrderByDescending(e => e.GraiObjectGroupIsn)
                        select new Shipment
                        {
                            Id = grai.GraiObjectIsn,
                            Group = grai.GraiObjectGroupIsn
                        };

            return await query.Distinct().LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<Shipment>> GetListShipmentAsync(
            long? id = null,
            string groupNo = null,
            long? vehicleIsn = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();

            var datas = await (from grai in (await GetDbContextAsync())
                                    .GraiGroupAdditionalInfos.Where(x => x.GraiGroupCode == "RECEIVED")
                                    .WhereIf(vehicleIsn.HasValue && vehicleIsn > 0, x => x.GraiGroupType == "VEHICLE_ISN" && x.GraiNumericValue == vehicleIsn)
                                    .WhereIf(id.HasValue, e => e.GraiObjectIsn == id)
                                    .WhereIf(!string.IsNullOrWhiteSpace(groupNo), e => e.GraiObjectGroupIsn.Equals(groupNo))
                                    .OrderByDescending(e => e.GraiObjectGroupIsn)
                               select new Shipment
                               {
                                   Id = grai.GraiObjectIsn,
                                   Group = grai.GraiObjectGroupIsn
                               }).Distinct()
                    .OrderByDescending(x => x.Group).PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);

            foreach (var item in datas)
            {
                var pcsGroup = dbContext.GraiGroupAdditionalInfos
                    .Where(x => x.GraiObjectIsn == item.Id)
                    .Where(x => x.GraiGroupCode == "RECEIVED")
                    .Where(x => x.GraiGroupType == "PIECES")
                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                    .Select(x => x)
                    .SingleOrDefault();
                if (pcsGroup != null)
                {
                    item.Pcs = pcsGroup.GraiValue ?? "0";
                }
                item.ChargableWeight = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "C_WEIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiNumericValue)
                                    .SingleOrDefault();
                item.GrossWeight = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "WEIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiNumericValue.ToString(CultureInfo.InvariantCulture))
                                    .SingleOrDefault();
                item.Class = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "STORAGE")
                                    .Where(x => x.GraiGroupType == "CLASS")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();
                item.ReceivedDate = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "DATE")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();
                item.Shc = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "SHC")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();

                item.Package = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                 .Where(x => x.GraiObjectIsn == item.Id)
                                 .Where(x => x.GraiGroupCode == "RECEIVED")
                                 .Where(x => x.GraiGroupType == "ISN")
                                 .Where(x => x.GraiObjectGroupIsn == item.Group)
                                 .Select(x => x.GraiValue ?? string.Empty)
                                 .SingleOrDefault();

                var flightId = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "ISN")
                                    .Where(x => x.GraiGroupType == "FLIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();

                var flight = _fluiRepository.Where(x => x.Id.ToString() == (flightId ?? "")).SingleOrDefault();
                if (flight != null)
                {
                    item.FluiAl23letterCode = flight.FluiAl23letterCode;
                    item.FluiFlightNo = flight.FluiFlightNo;
                    item.FluiLandedDate = flight.FluiLandedDate;
                    item.FluiLandedTime = flight.FluiLandedTime;
                }

                // get thông tin Location
                var subQuery = from locs in (await GetDbContextAsync()).LocsLocations.Where(x => x.LocsObjectIsn == id && x.LocsGroupIsn == item.Group && !x.LocsDeleted.Value)
                               join phys in (await GetDbContextAsync()).SslpPhysicalLocations.Where(x => !x.SslpDeleted)
                                 on locs.LocsPhysicalIsn equals phys.Id into phySub
                               from phys in phySub.DefaultIfEmpty()
                               select new Shipment
                               {
                                   //Location = phys.SslpRackRow + "-" + phys.SslpRackLocation + "-" + phys.SslpRackHeight
                                   Location = $"{phys.SslpRackRow}{phys.SslpRackLocation:D2}-{phys.SslpRackHeight}"
                               };

                var values = subQuery.FirstOrDefault();
                item.Location = values != null ? values.Location : string.Empty;
            }
            return datas;
        }

        public async Task<List<Shipment>> GetListShipmentClassUpdateAsync(
            long? id = null,
            string groupNo = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();

            var datas = await (from grai in (await GetDbContextAsync())
                                    .GraiGroupAdditionalInfos.Where(x => x.GraiGroupCode == "RECEIVED")
                                    .WhereIf(id.HasValue, e => e.GraiObjectIsn == id)
                                    .WhereIf(!string.IsNullOrWhiteSpace(groupNo), e => e.GraiObjectGroupIsn.Equals(groupNo))
                                    .OrderByDescending(e => e.GraiObjectGroupIsn)
                               select new Shipment
                               {
                                   Id = grai.GraiObjectIsn,
                                   Group = grai.GraiObjectGroupIsn
                               }).Distinct()
                    .OrderByDescending(x => x.Group).PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);

            foreach (var item in datas)
            {
                var pcsGroup = dbContext.GraiGroupAdditionalInfos
                    .Where(x => x.GraiObjectIsn == item.Id)
                    .Where(x => x.GraiGroupCode == "RECEIVED")
                    .Where(x => x.GraiGroupType == "PIECES")
                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                    .Select(x => x)
                    .SingleOrDefault();
                if (pcsGroup != null)
                {
                    item.Pcs = pcsGroup.GraiValue ?? "0";
                }
                item.ChargableWeight = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "C_WEIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiNumericValue)
                                    .SingleOrDefault();
                item.GrossWeight = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "WEIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiNumericValue.ToString(CultureInfo.InvariantCulture))
                                    .SingleOrDefault();

                var flightId = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "ISN")
                                    .Where(x => x.GraiGroupType == "FLIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();

                var flight = _fluiRepository.Where(x => x.Id.ToString() == (flightId ?? "")).SingleOrDefault();
                if (flight != null)
                {
                    item.FluiAl23letterCode = flight.FluiAl23letterCode;
                    item.FluiFlightNo = flight.FluiFlightNo;
                    item.FluiLandedDate = flight.FluiLandedDate;
                    item.FluiLandedTime = flight.FluiLandedTime;
                }
            }

            return datas;
        }

        public async Task<long> GetCountShipmentByMawbAsync(long? id, string groupNo = null, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = from lagi in dbContext.Lagis.Where(x => x.LagiMasterIdentNo == id || x.Id == id)
                        join grai in dbContext.GraiGroupAdditionalInfos
                            .Where(x => x.GraiGroupCode == "RECEIVED")
                            .WhereIf(!string.IsNullOrWhiteSpace(groupNo), e => e.GraiObjectGroupIsn.Equals(groupNo))
                            .OrderByDescending(e => e.GraiObjectGroupIsn)
                            on lagi.Id equals grai.GraiObjectIsn
                        select new Shipment
                        {
                            Id = grai.GraiObjectIsn,
                            Group = grai.GraiObjectGroupIsn
                        };

            return await query.Distinct().LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<Shipment>> GetListShipmentByMawbAsync(long? id = null, string groupNo = null, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = from lagi in dbContext.Lagis.Where(x => x.LagiMasterIdentNo == id || x.Id == id)
                        join grai in dbContext.GraiGroupAdditionalInfos
                            .Where(x => x.GraiGroupCode == "RECEIVED")
                            .WhereIf(!string.IsNullOrWhiteSpace(groupNo), e => e.GraiObjectGroupIsn.Equals(groupNo))
                            .OrderByDescending(e => e.GraiObjectGroupIsn)
                            on lagi.Id equals grai.GraiObjectIsn
                        select new Shipment
                        {
                            Id = grai.GraiObjectIsn,
                            Group = grai.GraiObjectGroupIsn
                        };

            var datas = await query.Distinct()
                .OrderByDescending(x => x.Group).PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            foreach (var item in datas)
            {
                var pcsGroup = dbContext.GraiGroupAdditionalInfos
                    .Where(x => x.GraiObjectIsn == item.Id)
                    .Where(x => x.GraiGroupCode == "RECEIVED")
                    .Where(x => x.GraiGroupType == "PIECES")
                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                    .Select(x => x)
                    .SingleOrDefault();
                if (pcsGroup != null)
                {
                    item.Pcs = pcsGroup.GraiValue ?? "0";
                }
                item.GrossWeight = dbContext.GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "WEIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? "0")
                                    .SingleOrDefault()?.Replace(",", ".");
                item.Class = dbContext.GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "STORAGE")
                                    .Where(x => x.GraiGroupType == "CLASS")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();
                item.ReceivedDate = dbContext.GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "DATE")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();
                item.Shc = dbContext.GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "RECEIVED")
                                    .Where(x => x.GraiGroupType == "SHC")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();

                var flightId = (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                    .Where(x => x.GraiObjectIsn == item.Id)
                                    .Where(x => x.GraiGroupCode == "ISN")
                                    .Where(x => x.GraiGroupType == "FLIGHT")
                                    .Where(x => x.GraiObjectGroupIsn == item.Group)
                                    .Select(x => x.GraiValue ?? string.Empty)
                                    .SingleOrDefault();

                var flight = _fluiRepository.Where(x => x.Id.ToString() == (flightId ?? "")).SingleOrDefault();
                if (flight != null)
                {
                    item.FluiAl23letterCode = flight.FluiAl23letterCode;
                    item.FluiFlightNo = flight.FluiFlightNo;
                    item.FluiLandedDate = flight.FluiLandedDate;
                    item.FluiLandedTime = flight.FluiLandedTime;
                }

                // get thông tin Location
                var subQuery = from locs in (await GetDbContextAsync()).LocsLocations.Where(x => !x.LocsDeleted.Value)
                               join phys in (await GetDbContextAsync()).SslpPhysicalLocations.Where(x => !x.SslpDeleted)
                                 on locs.LocsPhysicalIsn equals phys.Id into phySub
                               from phys in phySub.DefaultIfEmpty()
                               where locs.LocsGroupIsn == item.Group
                               select new Shipment
                               {
                                   //Location = phys.SslpRackRow + "-" + phys.SslpRackLocation + "-" + phys.SslpRackHeight
                                   Location = $"{phys.SslpRackRow}{phys.SslpRackLocation:D2}-{phys.SslpRackHeight}"
                               };

                var values = subQuery.FirstOrDefault();
                item.Location = values != null ? values.Location : string.Empty;
            }

            return datas;
        }

        public async Task<long> GetCountFlightAsync(long id, CancellationToken cancellationToken = default)
        {
            var query = (from lag in (await GetDbContextAsync()).Lagis
                         where lag.LagiMasterIdentNo == id && lag.LagiLvgIn != " " && lag.LagiFlightNoIn != " "
                         group lag by new { lag.LagiLvgIn, lag.LagiFlightNoIn, lag.LagiFlightDateIn, lag.LagiShedCode } into lagi
                         select new Flight
                         {
                             Airline = lagi.Key.LagiLvgIn,
                             BookFlight = lagi.Key.LagiFlightNoIn,
                             BookFlightDate = lagi.Key.LagiFlightDateIn,
                             Pcs = lagi.Sum(x => x.LagiQuantityExpected),
                             GrossWeight = lagi.Sum(x => x.LagiWeightExpected),
                             CargoTerminal = lagi.Key.LagiShedCode
                         }).OrderBy(x => x.BookFlightDate);

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<Flight>> GetListFlightAsync(long id, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var query = (from lag in (await GetDbContextAsync()).Lagis
                         where lag.LagiMasterIdentNo == id && lag.LagiLvgIn != " " && lag.LagiFlightNoIn != " "
                         group lag by new { lag.LagiLvgIn, lag.LagiFlightNoIn, lag.LagiFlightDateIn, lag.LagiFlightTimeIn, lag.LagiShedCode, lag.LagiCustomerNoAirline } into lagi
                         select new Flight
                         {
                             Airline = lagi.Key.LagiLvgIn,
                             BookFlight = lagi.Key.LagiFlightNoIn,
                             BookFlightDate = lagi.Key.LagiFlightDateIn,
                             BookFlightTime = lagi.Key.LagiFlightTimeIn,
                             LagiCustomerNoAirline = lagi.Key.LagiCustomerNoAirline,
                             Pcs = lagi.Sum(x => x.LagiQuantityExpected),
                             GrossWeight = lagi.Sum(x => x.LagiWeightExpected),
                             CargoTerminal = lagi.Key.LagiShedCode,
                         }).OrderBy(x => x.BookFlightDate);

            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<List<HawbByMawb>> GetListHawbByMawbAsync(long id, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = from lagi in dbContext.Lagis.Where(x => x.LagiDeleted == false)

                            //    // Join lấy thông tin kiện đã pickup
                            //join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn into vhldGroups
                            //from vhldGroup in vhldGroups.DefaultIfEmpty()

                        where lagi.LagiMasterIdentNo == id
                        select new HawbByMawb
                        {
                            AwbId = lagi.Id,
                            Hawb = lagi.LagiHawb,
                            Airline = lagi.LagiLvgIn,
                            FlightNo = lagi.LagiFlightNoIn,
                            FlightDateIn = lagi.LagiFlightDateIn,
                            Pcs = lagi.LagiQuantityExpected,
                            PiecesDLV = lagi.LagiQuantityDelivered,
                            ChargableWeight = (decimal)lagi.LagiChargableWeight,
                            GrossWeight = lagi.LagiWeightExpected,
                            PcsPickup = dbContext.VhldVehicleDetails
                             .Where(v => v.VhldDeleted == false && v.VhldObjectisn == lagi.Id && v.VhldRecordtype == "TRANSIT")
                             .Sum(v => (int?)v.VhldQtyreceived) ?? 0,
                            GrossPickup = dbContext.VhldVehicleDetails
                             .Where(v => v.VhldDeleted == false && v.VhldObjectisn == lagi.Id && v.VhldRecordtype == "TRANSIT")
                             .Sum(v => (decimal?)v.VhldWeightReceived) ?? 0,
                        };
            return await query.Distinct().PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountHawbByMawbAsync(long id, CancellationToken cancellationToken = default)
        {
            var query = from lagi in (await GetDbContextAsync()).Lagis
                        where lagi.LagiMasterIdentNo == id
                        select new HawbByMawb
                        {
                            AwbId = lagi.Id,
                            Hawb = lagi.LagiHawb,
                            Airline = lagi.LagiLvgIn,
                            FlightNo = lagi.LagiFlightNoIn,
                            FlightDateIn = lagi.LagiFlightDateIn,
                            Pcs = lagi.LagiQuantityExpected,
                            ChargableWeight = (decimal)lagi.LagiChargableWeight,
                            GrossWeight = lagi.LagiWeightExpected
                        };
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<PagedResult<ListMawb>> GetPagedListMawbALSBAsync(long? kundId, DateTime? flightDateFrom, DateTime? flightDateTo, DateTime? awbCreatedTimeFrom, DateTime? awbCreatedTimeTo
            , List<string> listMawb, string warehouse, string agent, string csn, string flightNumber, int maxResultCount, int skipCount, CancellationToken cancellationToken = default)
        {
            // Định nghĩa các điều kiện lọc
            bool hasWarehouse = !string.IsNullOrWhiteSpace(warehouse);
            bool flightDateRange = flightDateFrom.HasValue && flightDateTo.HasValue;
            bool uploadDateRange = awbCreatedTimeFrom.HasValue && awbCreatedTimeTo.HasValue;
            bool hasFlightNumber = !string.IsNullOrWhiteSpace(flightNumber);
            bool hasAwbCreatedTimeFrom = awbCreatedTimeFrom.HasValue;
            bool hasAwbCreatedTimeTo = awbCreatedTimeTo.HasValue;
            bool hasAgent = !agent.IsNullOrWhiteSpace();
            bool hasMawb = listMawb != null;
            bool hasCsn = !csn.IsNullOrWhiteSpace();

            long flightDateFromAsInt = 0;
            long flightDateToAsInt = 0;
            if (flightDateRange)
            {
                flightDateFromAsInt = CommonFunction.ConverDateToNumber(flightDateFrom.Value);
                flightDateToAsInt = CommonFunction.ConverDateToNumber(flightDateTo.Value);
            }

            /*var queryH = from d in (await GetDbContextAsync()).AwbPlanUploadDetails.Where(x => x.ApudDeleted == false)
                         .WhereIf(hasMawb, x => listMawb.Contains(x.ApudMawb))

                        join m in (await GetDbContextAsync()).Lagis.Where(x => x.LagiDeleted == false) on d.ApudSync equals m.Id

                        join v in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on d.ApudSync equals v.VhldObjectisn into vhldGroups
                        from v in vhldGroups.DefaultIfEmpty()

                        join l in (await GetDbContextAsync()).AwbPlanUploadLists on (d != null ? d.ApudListId : null) equals l.Id into ljp
                        from l in ljp.DefaultIfEmpty()

                        select new ListMawb
                        {
                            LagiId = m.LagiMasterIdentNo,
                            Mawb = d.ApudMawb,
                            Hawb = d.ApudHawb,
                            Pcs = d.ApudPcs ?? 0,
                            Gw = d.ApudGw ?? 0,
                            PcsPickUp = v.VhldQtyreceived,
                            GwPickUp = v.VhldWeightReceived,
                            Flight = d.ApudFlightNo,
                            FlightEtaDate = d.ApudFlightEtaDate,
                            FlightEtaTime = d.ApudFlightEtaTime,
                            KundAgent = d.ApudAgentCode,
                            KundConsignee = d.ApudCneemawb,
                            CargoTerminal = d.ApudWarehouse,
                            UploadDate = l.ApulUploadDatetime
                        };

            var listH = queryH.Distinct().ToList();*/
            var dbContext = (await GetDbContextAsync());
            var query = from m in (await GetDbContextAsync()).Lagis.Where(x => x.LagiDeleted == false)


                        join d in (await GetDbContextAsync()).AwbPlanUploadDetails
                            .Where(x => x.ApudDeleted == false && x.ApudImportExport.Equals("IMPORT") && x.ApudPlanType.Equals("PICKUP"))
                            .WhereIf(hasMawb, x => listMawb.Contains(x.ApudMawb))
                            .WhereIf(hasFlightNumber, x => x.ApudFlightNo.Contains(flightNumber))
                            .WhereIf(hasWarehouse, x => x.ApudWarehouse == warehouse)
                            .WhereIf(hasCsn, x => x.ApudCneemawb == csn)
                            .WhereIf(hasAgent, x => x.ApudAgentCode == agent)
                            on m.Id equals d.ApudSync

                        join v in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false && x.VhldRecordtype == "TRANSIT") on d.ApudSync equals v.VhldObjectisn into vhldGroups
                        from v in vhldGroups.DefaultIfEmpty()

                        join c in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclTruckType == "TRANSIT") on v.VhldVehicleisn equals c.Id into ljVhcl
                        from c in ljVhcl.DefaultIfEmpty()

                        join l in (await GetDbContextAsync()).AwbPlanUploadLists on (d != null ? d.ApudListId : null) equals l.Id into ljp
                        from l in ljp.DefaultIfEmpty()

                        group new { d, v } by new
                        {
                            MawbId = m.LagiMasterIdentNo,
                            d.ApudMawb,
                            //d.ApudMawb,
                            //d.ApudSync,
                            d.ApudFlightNo,
                            d.ApudFlightEtaDate,
                            d.ApudFlightEtaTime,
                            d.ApudAgentCode,
                            d.ApudCneemawb,
                            d.ApudWarehouse,
                            ApulUploadDatetime = l != null ? l.ApulUploadDatetime : (DateTime?)null
                        } into g
                        select new ListMawb
                        {
                            LagiId = g.Key.MawbId,
                            Mawb = g.Key.ApudMawb,
                            Pcs = g.Select(x => x.d.ApudPcs ?? 0).Sum(),
                            Gw = g.Select(x => x.d.ApudGw ?? 0).Sum(),
                            PcsPickUp = g.Select(x => x.v.VhldQtyreceived).Sum(),
                            GwPickUp = g.Select(x => x.v.VhldWeightReceived).Sum(),
                            Flight = g.Key.ApudFlightNo,
                            FlightEtaDate = g.Key.ApudFlightEtaDate,
                            FlightEtaTime = g.Key.ApudFlightEtaTime,
                            KundAgent = g.Key.ApudAgentCode,
                            KundConsignee = g.Key.ApudCneemawb,
                            CargoTerminal = g.Key.ApudWarehouse,
                            UploadDate = g.Key.ApulUploadDatetime
                        };

            var result = query.ToList();
            result = result
                            .WhereIf(flightDateRange, x => x.FlightDate >= flightDateFrom && x.FlightDate <= flightDateTo)
                            .WhereIf(uploadDateRange, x => x.UploadDate.HasValue && x.UploadDate.Value >= awbCreatedTimeFrom && x.UploadDate.Value <= awbCreatedTimeTo)
                            .OrderByDescending(x => x.UploadDate).ThenByDescending(x => x.FlightDate).ThenByDescending(x => x.Mawb).ToList();

            return result.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<IQueryable<ListMawb>> GetPagedListHawbALSBAsync(long? kundId, DateTime? flightDateFrom, DateTime? flightDateTo, DateTime? awbCreatedTimeFrom, DateTime? awbCreatedTimeTo
            , List<string> listMawb, List<string> listHawb, string warehouse, string agent, string csn, string flightNumber, int statusProgress, int maxResultCount, int skipCount, CancellationToken cancellationToken = default)
        {
            // Định nghĩa các điều kiện lọc
            bool hasWarehouse = !string.IsNullOrWhiteSpace(warehouse);
            bool flightDateRange = flightDateFrom.HasValue && flightDateTo.HasValue;
            bool uploadDateRange = awbCreatedTimeFrom.HasValue && awbCreatedTimeTo.HasValue;
            bool hasFlightNumber = !string.IsNullOrWhiteSpace(flightNumber);
            bool hasAwbCreatedTimeFrom = awbCreatedTimeFrom.HasValue;
            bool hasAwbCreatedTimeTo = awbCreatedTimeTo.HasValue;
            bool hasAgent = !agent.IsNullOrWhiteSpace();
            bool hasMawb = listMawb != null;
            bool hasHawb = listHawb != null;
            bool hasCsn = !csn.IsNullOrWhiteSpace();

            long flightDateFromAsInt = 0;
            long flightDateToAsInt = 0;
            if (flightDateRange)
            {
                flightDateFromAsInt = CommonFunction.ConverDateToNumber(flightDateFrom.Value);
                flightDateToAsInt = CommonFunction.ConverDateToNumber(flightDateTo.Value);
            }
            var dbContext = (await GetDbContextAsync());
            var query = from d in (await GetDbContextAsync()).AwbPlanUploadDetails
                            .Where(x => x.ApudDeleted == false && x.ApudImportExport.Equals("IMPORT") && x.ApudPlanType.Equals("PICKUP"))
                            .WhereIf(hasMawb, x => listMawb.Contains(x.ApudMawb))
                            .WhereIf(hasHawb, x => listHawb.Contains(x.ApudHawb))
                            .WhereIf(hasFlightNumber, x => x.ApudFlightNo.Contains(flightNumber))
                            .WhereIf(hasWarehouse, x => x.ApudWarehouse == warehouse)
                            .WhereIf(hasCsn, x => x.ApudCneemawb == csn)
                            .WhereIf(hasAgent, x => x.ApudAgentCode == agent)

                        join m in (await GetDbContextAsync()).Lagis.Where(x => x.LagiDeleted == false) on d.ApudSync equals m.Id

                        join v in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false && x.VhldRecordtype == "TRANSIT") on d.ApudSync equals v.VhldObjectisn into vhldGroups
                        from v in vhldGroups.DefaultIfEmpty()

                        join c in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclTruckType == "TRANSIT") on v.VhldVehicleisn equals c.Id into ljVhcl
                        from c in ljVhcl.DefaultIfEmpty()

                        join l in (await GetDbContextAsync()).AwbPlanUploadLists on (d != null ? d.ApudListId : null) equals l.Id into ljp
                        from l in ljp.DefaultIfEmpty()

                        group new { d, v } by new
                        {
                            MawbId = m.LagiMasterIdentNo,
                            d.ApudMawb,
                            d.ApudSync,
                            d.ApudHawb,
                            d.ApudFlightNo,
                            d.ApudFlightEtaDate,
                            d.ApudFlightEtaTime,
                            d.ApudAgentCode,
                            d.ApudCneemawb,
                            d.ApudWarehouse,
                            ApulUploadDatetime = l != null ? l.ApulUploadDatetime : (DateTime?)null
                        } into g
                        select new ListMawb
                        {
                            LagiId = g.Key.MawbId,
                            Mawb = g.Key.ApudMawb,
                            Hawb = g.Key.ApudHawb,
                            Pcs = g.Select(x => new { x.d.ApudPcs, x.d.Id }).Distinct().Sum(x => x.ApudPcs ?? 0),
                            Gw = g.Select(x => new { x.d.ApudGw, x.d.Id }).Distinct().Sum(x => x.ApudGw ?? 0),
                            PcsPickUp = g.Select(x => new { x.v.VhldQtyreceived, x.v.Id }).Distinct().Sum(x => x.VhldQtyreceived),
                            GwPickUp = g.Select(x => new { x.v.VhldWeightReceived, x.v.Id }).Distinct().Sum(x => x.VhldWeightReceived),
                            Flight = g.Key.ApudFlightNo,
                            FlightEtaDate = g.Key.ApudFlightEtaDate,
                            FlightEtaTime = g.Key.ApudFlightEtaTime,
                            KundAgent = g.Key.ApudAgentCode,
                            KundConsignee = g.Key.ApudCneemawb,
                            CargoTerminal = g.Key.ApudWarehouse,
                            UploadDate = g.Key.ApulUploadDatetime,
                        };

            var result = query
                .ToList()
                .WhereIf(flightDateRange, x => x.FlightDate >= flightDateFrom && x.FlightDate <= flightDateTo)
                .WhereIf(uploadDateRange, x => x.UploadDate.HasValue && x.UploadDate.Value >= awbCreatedTimeFrom && x.UploadDate.Value <= awbCreatedTimeTo)
                .WhereIf(statusProgress != 0 && statusProgress == 1, x => x.PcsPickUp == x.Pcs) // về đủ
                .WhereIf(statusProgress != 0 && statusProgress == 2, x => x.PcsPickUp > 0 && x.PcsPickUp < x.Pcs) // về thiếu
                .WhereIf(statusProgress != 0 && statusProgress == 3, x => x.PcsPickUp == 0).ToList() // chưa về
                .OrderByDescending(x => x.UploadDate)
                .ThenByDescending(x => x.Mawb);

            return result.AsQueryable();
            //return result.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<Hawb>> GetPagedListHawbByAsync(long kundId, DateTime flightDateFrom, DateTime flightDateTo, DateTime? awbCreatedTimeFrom, DateTime? awbCreatedTimeTo, string warehouse, string status, string exiservice, long cargoTerminal, string flightNumber, long[] hawbIds, int maxResultCount, int skipCount, CancellationToken cancellationToken = default)
        {
            var flightDateFromAsInt = CommonFunction.ConverDateToNumber(flightDateFrom);
            var flightDateToAsInt = CommonFunction.ConverDateToNumber(flightDateTo);

            //lấy lagi có cả mawb và hawb
            var lagiMawbHawb = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiFlightDateIn >= flightDateFromAsInt && x.LagiFlightDateIn <= flightDateToAsInt && x.LagiMasterIdentNo != 0)
                                                                              .WhereIf(!string.IsNullOrEmpty(warehouse), x => x.LagiTso == warehouse)
                                select new
                                {
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix
                                }).Distinct();

            //loại trừ những lagi có cả mawb và hawb
            var lagiOnlyMawb = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiFlightDateIn >= flightDateFromAsInt && x.LagiFlightDateIn <= flightDateToAsInt && x.LagiMasterIdentNo == 0)
                                                                             .WhereIf(!string.IsNullOrEmpty(warehouse), x => x.LagiTso == warehouse)
                                                                             .Where(x => !lagiMawbHawb.Any(y => x.LagiMawbNo == y.LagiMawbNo && x.LagiMawbPrefix == y.LagiMawbPrefix))
                               select new
                               {
                                   lagi.Id
                               };

            var lagiKundQuery = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiFlightDateIn >= flightDateFromAsInt && x.LagiFlightDateIn <= flightDateToAsInt && (x.LagiMasterIdentNo != 0 || lagiOnlyMawb.Any(y => x.Id == y.Id)))
                                                                              .WhereIf(hawbIds.Length > 0, x => hawbIds.Contains(x.Id))
                                                                              .WhereIf(!string.IsNullOrEmpty(flightNumber), x => (x.LagiLvgIn + x.LagiFlightNoIn).Contains(flightNumber))
                                                                              .WhereIf(awbCreatedTimeFrom.HasValue, x => x.LagiCreatedAt >= awbCreatedTimeFrom)
                                                                              .WhereIf(awbCreatedTimeTo.HasValue, x => x.LagiCreatedAt <= awbCreatedTimeTo)
                                                                              .WhereIf(cargoTerminal != 0, x => x.LagiShedCode == cargoTerminal.ToString())
                                                                              .WhereIf(!string.IsNullOrEmpty(warehouse), x => x.LagiTso == warehouse)
                                                                              .WhereIf(!string.IsNullOrEmpty(exiservice), x => x.LagiExpandedCommodity == exiservice)
                                join kund in (await GetDbContextAsync()).Kunds.Where(x => x.Id == kundId) on lagi.LagiCurrentAgent.Value equals kund.Id
                                select new Hawb
                                {
                                    LagiId = lagi.Id,
                                    LagiMawbNo = lagi.LagiMawbNo,
                                    LagiMawbPrefix = lagi.LagiMawbPrefix,
                                    LagiQuantityExpected = lagi.LagiQuantityExpected,
                                    LagiWeightExpected = lagi.LagiWeightExpected,
                                    KundId = kund.Id,
                                    LagiHawb = lagi.LagiHawb,
                                    LagiLvgIn = lagi.LagiLvgIn,
                                    LagiFlightNoIn = lagi.LagiFlightNoIn,
                                    LagiFlightDateIn = lagi.LagiFlightDateIn,
                                    LagiFlightTimeIn = lagi.LagiFlightTimeIn,
                                    LagiShedCode = lagi.LagiShedCode,
                                    LagiShedCodeAtDestination = lagi.LagiShedAtDestination,
                                    LagiExpandedCommodity = lagi.LagiExpandedCommodity,
                                    LagiTso = lagi.LagiTso,
                                    LagiDateStatus1Set = lagi.LagiDateStatus1set,
                                    LagiTimeStatus1Set = lagi.LagiTimeStatus1set,
                                    LagiDateStatus3Set = lagi.LagiDateStatus3set,
                                    LagiTimeStatus3Set = lagi.LagiTimeStatus3set,
                                    LagiDateStatus4Set = lagi.LagiDateStatus4set,
                                    LagiTimeStatus4Set = lagi.LagiTimeStatus4set,
                                    LagiDateStatus5Set = lagi.LagiDateStatus5set,
                                    LagiTimeStatus5Set = lagi.LagiTimeStatus5set,
                                    LagiDateStatus6Set = lagi.LagiDateStatus6set,
                                    LagiTimeStatus6Set = lagi.LagiTimeStatus6set,
                                    LagiDateStatus7Set = lagi.LagiDateStatus7set,
                                    LagiTimeStatus7Set = lagi.LagiTimeStatus7set,
                                    LagiDateStatus8Set = lagi.LagiDateStatus8set,
                                    LagiTimeStatus8Set = lagi.LagiTimeStatus8set,
                                };

            var lagisKund = lagiKundQuery.ToList();

            var flights = _fluiRepository.Select(x => new { x.FluiLandedDate, x.FluiLandedTime, x.FluiAl23letterCode, x.FluiFlightNo, x.FluiScheduleDate, x.FluiScheduleTime }).ToList();

            foreach (var lagiKund in lagisKund)
            {
                var flui = flights.Where(x => x.FluiAl23letterCode == lagiKund.LagiLvgIn && x.FluiFlightNo == lagiKund.LagiFlightNoIn && x.FluiScheduleDate == lagiKund.LagiFlightDateIn && x.FluiScheduleTime == lagiKund.LagiFlightTimeIn).FirstOrDefault();
                lagiKund.FluiLandedDate = flui?.FluiLandedDate;
                lagiKund.FluiLandedTime = flui?.FluiLandedTime;
            }

            var lagiTruckPickupQuery = from lagi in lagiKundQuery
                                       join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.LagiId equals vhld.VhldObjectisn into vhldGroups
                                       from vhldGroup in vhldGroups.DefaultIfEmpty()
                                       join vhcl in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclImportExport == "IMPORT") on vhldGroup.VhldVehicleisn equals vhcl.Id into vhclGroups
                                       from vhclGroup in vhclGroups.DefaultIfEmpty()
                                       select new TruckPickupHawb
                                       {
                                           LagiId = lagi.LagiId,
                                           VhclRegNo = vhclGroup.VehicRegNo,
                                           VhclLoadingLeftDate = vhclGroup.VhclLoadingLeftDate,
                                           VhclLoadingLeftTime = vhclGroup.VhclLoadingLeftTime,
                                           VhclLoadingArrivalDate = vhclGroup.VhclLoadingArrivalDate,
                                           VhclLoadingArrivalTime = vhclGroup.VhclLoadingArrivalTime,
                                           VhclCompletedDate = vhclGroup.VhclCompletedDate,
                                           VhclCompletedTime = vhclGroup.VhclCompletedTime,
                                           VhclTransitToFactoryDate = vhclGroup.VhclTransitToFactoryDate,
                                           VhclTransitToFactoryTime = vhclGroup.VhclTransitToFactoryTime,
                                           VhclArrivedFactoryDate = vhclGroup.VhclArrivedFactoryDate,
                                           VhclArrivedFactoryTime = vhclGroup.VhclArrivedFactoryTime,
                                           VhclUnloadingArrivalDate = vhclGroup.VhclUnloadingArrivalDate,
                                           VhclUnloadingArrivalTime = vhclGroup.VhclUnloadingArrivalTime,
                                       };

            var fquery = from lagiKund in lagisKund
                         join lagiTruckPickup in lagiTruckPickupQuery.ToList() on lagiKund.LagiId equals lagiTruckPickup.LagiId
                         group lagiTruckPickup by lagiKund into lagiTruckPickupGroups
                         select new Hawb
                         {
                             LagiId = lagiTruckPickupGroups.Key.LagiId,
                             LagiMawbNo = lagiTruckPickupGroups.Key.LagiMawbNo,
                             LagiMawbPrefix = lagiTruckPickupGroups.Key.LagiMawbPrefix,
                             LagiQuantityExpected = lagiTruckPickupGroups.Key.LagiQuantityExpected,
                             LagiWeightExpected = lagiTruckPickupGroups.Key.LagiWeightExpected,
                             KundId = lagiTruckPickupGroups.Key.KundId,
                             LagiHawb = lagiTruckPickupGroups.Key.LagiHawb,
                             LagiLvgIn = lagiTruckPickupGroups.Key.LagiLvgIn,
                             LagiFlightNoIn = lagiTruckPickupGroups.Key.LagiFlightNoIn,
                             LagiFlightDateIn = lagiTruckPickupGroups.Key.LagiFlightDateIn,
                             LagiFlightTimeIn = lagiTruckPickupGroups.Key.LagiFlightTimeIn,
                             LagiShedCode = lagiTruckPickupGroups.Key.LagiShedCode,
                             LagiShedCodeAtDestination = lagiTruckPickupGroups.Key.LagiShedCodeAtDestination,
                             VhclRegNo = string.Join(',', lagiTruckPickupGroups.Where(x => x.VhclRegNo != null).Select(x => x.VhclRegNo).Distinct()),
                             IsTransitToWarehouse = CheckTransitToWarehouse(lagiTruckPickupGroups),
                             IsArrivedWarehouse = CheckArrivedWarehouse(lagiTruckPickupGroups),
                             IsCompletedWarehouse = CheckCompletedWarehouse(lagiTruckPickupGroups),
                             IsTransitToFactory = CheckTransitToFactory(lagiTruckPickupGroups),
                             IsArrivedFactory = CheckArrivedFactory(lagiTruckPickupGroups),
                             NewestLoadingLeftDatetime = GetNewestLoadingLeftDatetime(lagiTruckPickupGroups),
                             NewestUnloadingArrivalDatetime = GetNewestUnloadingArrivalDatetime(lagiTruckPickupGroups),
                             NewestCompletedWarehouseDatetime = GetNewestCompletedWarehouseDatetime(lagiTruckPickupGroups),
                             NewestTransitToFactoryDatetime = GetNewestTransitToFactoryDatetime(lagiTruckPickupGroups),
                             NewestArrivedFactoryDatetime = GetNewestArrivedFactoryDatetime(lagiTruckPickupGroups),
                             LagiDateStatus1Set = lagiTruckPickupGroups.Key.LagiDateStatus1Set,
                             LagiTimeStatus1Set = lagiTruckPickupGroups.Key.LagiTimeStatus1Set,
                             LagiDateStatus3Set = lagiTruckPickupGroups.Key.LagiDateStatus3Set,
                             LagiTimeStatus3Set = lagiTruckPickupGroups.Key.LagiTimeStatus3Set,
                             LagiDateStatus4Set = lagiTruckPickupGroups.Key.LagiDateStatus4Set,
                             LagiTimeStatus4Set = lagiTruckPickupGroups.Key.LagiTimeStatus4Set,
                             LagiDateStatus5Set = lagiTruckPickupGroups.Key.LagiDateStatus5Set,
                             LagiTimeStatus5Set = lagiTruckPickupGroups.Key.LagiTimeStatus5Set,
                             LagiDateStatus6Set = lagiTruckPickupGroups.Key.LagiDateStatus6Set,
                             LagiTimeStatus6Set = lagiTruckPickupGroups.Key.LagiTimeStatus6Set,
                             LagiDateStatus7Set = lagiTruckPickupGroups.Key.LagiDateStatus7Set,
                             LagiTimeStatus7Set = lagiTruckPickupGroups.Key.LagiTimeStatus7Set,
                             LagiDateStatus8Set = lagiTruckPickupGroups.Key.LagiDateStatus8Set,
                             LagiTimeStatus8Set = lagiTruckPickupGroups.Key.LagiTimeStatus8Set,
                             LagiExpandedCommodity = lagiTruckPickupGroups.Key.LagiExpandedCommodity,
                             LagiTso = lagiTruckPickupGroups.Key.LagiTso,
                             NewestTruckStatusDatetime = GetNewestTruckStatusDateTime(lagiTruckPickupGroups),
                         };

            fquery = fquery.WhereIf(!string.IsNullOrEmpty(status), x => x.Status == status)
                           .OrderBy(x => x.LagiFlightDateIn)
                           .ThenBy(x => x.LagiFlightTimeIn);

            return fquery.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<Hawb>> GetPagedListHawbByAsync(long kundId, DateTime deliveryDateFrom, DateTime deliveryDateTo, long warehouse, string customs, string grade, string warehouseDeli, int maxResultCount, int skipCount, CancellationToken cancellationToken = default)
        {
            var lagiTruckDeliveryQuery = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo != 0)
                                            .WhereIf("OK".Equals(customs), x => x.LagiDateStatus3set != null)
                                            .WhereIf("NOT_OK".Equals(customs), x => x.LagiDateStatus3set == null)
                                            .WhereIf(kundId > 0, x => x.LagiNotifyNumber.Value == kundId)
                                         join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn into vhldGroups
                                         from vhldGroup in vhldGroups
                                         join vhcl in (await GetDbContextAsync()).VehiclesRegistrations
                                            .WhereIf(!string.IsNullOrEmpty(grade), x => x.VhclUnloadingDoor == grade)
                                            .WhereIf(!string.IsNullOrEmpty(warehouseDeli), x => x.VhclUnloadingWarehouse == warehouseDeli)
                                            .Where(x => x.VhclImportExport == "DELIVERY_PLAN" && x.VhclUnloadingEtaDate >= deliveryDateFrom && x.VhclUnloadingEtaDate <= deliveryDateTo) on vhldGroup.VhldVehicleisn equals vhcl.Id into vhclGroups
                                         from vhclGroup in vhclGroups
                                         select new Hawb
                                         {
                                             LagiId = lagi.Id,
                                             LagiMawbNo = lagi.LagiMawbNo,
                                             LagiMawbPrefix = lagi.LagiMawbPrefix,
                                             LagiHawb = lagi.LagiHawb,
                                             LagiQuantityExpected = lagi.LagiQuantityExpected,
                                             LagiWeightExpected = lagi.LagiWeightExpected,
                                             LagiDateStatus3Set = lagi.LagiDateStatus3set,
                                             VhclUnloadingEtaDate = vhclGroup.VhclUnloadingEtaDate,
                                             VhclUnloadingEtaTime = vhclGroup.VhclUnloadingEtaTime,
                                             VhclUnloadingDoor = vhclGroup.VhclUnloadingDoor,
                                             VhclUnloadingWarehouse = vhclGroup.VhclUnloadingWarehouse
                                         };

            return lagiTruckDeliveryQuery.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<List<AwbTruck>> GetListTruckAsync(long id, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var query = from vd in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false)
                        join vr in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclImportExport == "IMPORT") on vd.VhldVehicleisn equals vr.Id
                        where vd.VhldObjectisn == id // lagi.Id equals vd.VhldObjectisn
                        select new AwbTruck
                        {
                            Id = vr.Id,
                            TruckNo = vr.VehicRegNo,
                            Driver = vr.VhclDriverName,
                            TrucType = vr.VhclTruckType,
                            VhclLoadingArrivalDate = vr.VhclLoadingArrivalDate,
                            VhclLoadingAtDoorDate = vr.VhclLoadingAtDoorDate,
                            VhclLoadingArrivalTime = vr.VhclLoadingArrivalTime,
                            VhclUnloadingArrivalTime = vr.VhclUnloadingArrivalTime,
                            VhclLoadingAtDoorTime = vr.VhclLoadingAtDoorTime,
                            VhclLoadingLeftDate = vr.VhclLoadingLeftDate,
                            VhclUnloadingArrivalDate = vr.VhclUnloadingArrivalDate,
                            VhclUnloadingActivationDate = vr.VhclUnloadingActivationDate,
                            VhclVehicleComplete = vr.VhclVehicleComplete,
                            VhclCompletedDate = vr.VhclCompletedDate,
                            VhclUnloadingEtaDate = vr.VhclUnloadingEtaDate,
                            VhclMasterIsn = vr.VhclMasterIsn,
                        };

            var datas = await query.Distinct().PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            foreach (var item in datas)
            {
                if (item.TrucType.Equals("PICK UP"))
                {
                    item.PickUpTime = string.Format(
                        "{0:d-M-yyyy} {1}", item.VhclLoadingArrivalDate,
                        CommonFunction.ConvertIntToTime(item.VhclLoadingArrivalTime)
                    );
                }
                else if (item.TrucType.Equals("AT DOOR"))
                {
                    item.PickUpTime = string.Format(
                        "{0:d-M-yyyy} {1}", item.VhclUnloadingArrivalDate,
                        CommonFunction.ConvertIntToTime(item.VhclUnloadingArrivalTime)
                    );
                }
                else if (item.TrucType.Equals("TRANSIT"))
                {
                    item.PickUpTime = string.Format(
                        "{0:d-M-yyyy} {1}", item.VhclLoadingAtDoorDate,
                        CommonFunction.ConvertIntToTime(item.VhclLoadingAtDoorTime)
                    );
                }
            }

            return datas;
        }

        public async Task<long> GetCountTruckAsync(long id, CancellationToken cancellationToken = default)
        {
            var query = from vd in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false)
                        join vr in (await GetDbContextAsync()).VehiclesRegistrations on vd.VhldVehicleisn equals vr.Id
                        where vd.VhldObjectisn == id // lagi.Id equals vd.VhldObjectisn
                        select new AwbTruck
                        {
                            TruckNo = vr.VehicRegNo,
                            Driver = vr.VhclDriverName,
                            PickUpTime = string.Format("{0:dd/MM/yyyy}", vr.VhclLoadingArrivalDate) + " " + vr.VhclLoadingArrivalTime
                        };

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<AwbTruck>> GetListTruckByMawbAsync(long id, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var query = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo == id)
                        join vd in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vd.VhldObjectisn
                        join vr in (await GetDbContextAsync()).VehiclesRegistrations on vd.VhldVehicleisn equals vr.Id
                        select new AwbTruck
                        {
                            Id = vr.Id,
                            TruckNo = vr.VehicRegNo,
                            Driver = vr.VhclDriverName,
                            TrucType = vr.VhclTruckType,
                            VhclLoadingArrivalDate = vr.VhclLoadingArrivalDate,
                            VhclLoadingAtDoorDate = vr.VhclLoadingAtDoorDate,
                            VhclLoadingArrivalTime = vr.VhclLoadingArrivalTime,
                            VhclUnloadingArrivalTime = vr.VhclUnloadingArrivalTime,
                            VhclLoadingAtDoorTime = vr.VhclLoadingAtDoorTime,
                            VhclLoadingLeftDate = vr.VhclLoadingLeftDate,
                            VhclUnloadingArrivalDate = vr.VhclUnloadingArrivalDate,
                            VhclUnloadingActivationDate = vr.VhclUnloadingActivationDate,
                            VhclVehicleComplete = vr.VhclVehicleComplete,
                            VhclCompletedDate = vr.VhclCompletedDate,
                            VhclUnloadingEtaDate = vr.VhclUnloadingEtaDate,
                            VhclMasterIsn = vr.VhclMasterIsn,
                        };

            var datas = await query.Distinct().PageBy(skipCount, maxResultCount).OrderBy(x => x.Id).ToListAsync(cancellationToken);
            foreach (var item in datas)
            {
                if (item.TrucType.Equals("PICK UP"))
                {
                    item.PickUpTime = string.Format(
                        "{0:d-M-yyyy} {1}", item.VhclLoadingArrivalDate,
                        CommonFunction.ConvertIntToTime(item.VhclLoadingArrivalTime)
                    );
                }
                else if (item.TrucType.Equals("AT DOOR"))
                {
                    item.PickUpTime = string.Format(
                        "{0:d-M-yyyy} {1}", item.VhclUnloadingArrivalDate,
                        CommonFunction.ConvertIntToTime(item.VhclUnloadingArrivalTime)
                    );
                }
                else if (item.TrucType.Equals("TRANSIT"))
                {
                    item.PickUpTime = string.Format(
                        "{0:d-M-yyyy} {1}", item.VhclLoadingAtDoorDate,
                        CommonFunction.ConvertIntToTime(item.VhclLoadingAtDoorTime)
                    );
                }
            }

            return datas;
        }

        public async Task<long> GetCountTruckByMawbAsync(long id, CancellationToken cancellationToken = default)
        {
            var query = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo == id)
                        join vd in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vd.VhldObjectisn
                        join vr in (await GetDbContextAsync()).VehiclesRegistrations on vd.VhldVehicleisn equals vr.Id
                        select new AwbTruck
                        {
                            Id = vr.Id,
                            TruckNo = vr.VehicRegNo,
                            Driver = vr.VhclDriverName,
                            TrucType = vr.VhclTruckType,
                        };

            return await query.Distinct().LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<PagedResult<HawbModalUpdateStt>> GetHawbForModalUpdateSttAsync(
            long kundId = 0,
            string flightDateFrom = null,
            string flightDateTo = null,
            DateTime? createdDateFrom = null,
            DateTime? createdDateTo = null,
            string hawb = null,
            string mawbPrefix = null,
            int? mawbSerial = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0)
        {
            var lagiKundQuery = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo != 0 && x.LagiCurrentAgent == kundId && x.LagiDeleted == false)
                                    .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom), x => x.LagiFlightDateIn >= CommonFunction.ConverDateStringToNumber(flightDateFrom))
                                    .WhereIf(!string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn <= CommonFunction.ConverDateStringToNumber(flightDateTo))
                                    .WhereIf(createdDateFrom.HasValue, x => x.LagiCreatedAt >= createdDateFrom)
                                    .WhereIf(createdDateTo.HasValue, x => x.LagiCreatedAt <= createdDateTo)
                                    .WhereIf(!string.IsNullOrWhiteSpace(mawbPrefix), x => x.LagiMawbPrefix.Contains(mawbPrefix))
                                    .WhereIf(mawbSerial.HasValue, x => x.LagiMawbNo.ToString().Contains(mawbSerial.ToString()))
                                    .WhereIf(!string.IsNullOrWhiteSpace(hawb), x => x.LagiHawb.Contains(hawb))
                                select new HawbModalUpdateStt
                                {
                                    KundId = lagi.LagiCurrentAgent,
                                    LagiId = lagi.Id,
                                    LagiMawbNo = lagi.LagiMawbNo,
                                    LagiMawbPrefix = lagi.LagiMawbPrefix,
                                    LagiHawb = lagi.LagiHawb,
                                    Pcs = lagi.LagiQuantityExpected,
                                    GrossWeight = lagi.LagiWeightExpected,
                                    LagiDateStatus1set = lagi.LagiDateStatus1set,
                                    LagiDateStatus2set = lagi.LagiDateStatus2set,
                                    LagiDateStatus3set = lagi.LagiDateStatus3set,
                                    LagiDateStatus4set = lagi.LagiDateStatus4set,
                                    LagiDateStatus5set = lagi.LagiDateStatus5set,
                                    LagiDateStatus6set = lagi.LagiDateStatus6set,
                                    LagiDateStatus7set = lagi.LagiDateStatus7set,
                                    LagiDateStatus8set = lagi.LagiDateStatus8set
                                };
            return lagiKundQuery.AsQueryable().Distinct().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<long> UpdateStatusHawbAsync(string listLagisId, string status, string updateDatetime)
        {
            var dbContext = await GetDbContextAsync();

            string[] arrUpdateDatetime = updateDatetime.Split(' ');
            DateTime updateDate = CommonFunction.ConverDateStringToDate(arrUpdateDatetime[0]);
            string updateTime = CommonFunction.ConvertTimeToString(arrUpdateDatetime[1]);

            var query = (from lagi in (await GetDbContextAsync()).Lagis where listLagisId.Contains(lagi.Id.ToString()) select lagi).ToList();
            switch (status)
            {
                case "ĐÃ NHẬN HÀNG":
                    query.ForEach(a =>
                    {
                        // ALSW: đang dùng để lưu trạng thái đã nhận hàng
                        // CLC: đang dùng để lưu thời điểm unload completed
                        a.LagiDateStatus1set = updateDate;
                        a.LagiTimeStatus1set = updateTime;
                    });
                    break;

                //case "HOÀN THÀNH THỦ TỤC HẢI QUAN":
                //    query.ForEach(a =>
                //    {
                //        a.LagiDateStatus3set = updateDate;
                //        a.LagiTimeStatus3set = updateTime;
                //    });
                //    break;

                //case "TRẢ HÀNG THÀNH CÔNG":
                //    query.ForEach(a =>
                //    {
                //        a.LagiDateStatus4set = updateDate;
                //        a.LagiTimeStatus4set = updateTime;
                //    });
                //    break;

                case "HOÀN THÀNH THỦ TỤC XUẤT KHO NHÀ GA":
                    query.ForEach(a =>
                    {
                        a.LagiDateStatus5set = updateDate;
                        a.LagiTimeStatus5set = updateTime;
                    });
                    break;

                case "ĐANG SOI CHIẾU":
                    query.ForEach(a =>
                    {
                        a.LagiDateStatus6set = updateDate;
                        a.LagiTimeStatus6set = updateTime;
                    });
                    break;

                case "SOI CHIẾU THÀNH CÔNG":
                    query.ForEach(a =>
                    {
                        a.LagiDateStatus7set = updateDate;
                        a.LagiTimeStatus7set = updateTime;
                    });
                    break;

                case "SOI CHIẾU KHÔNG THÀNH CÔNG":
                    query.ForEach(a =>
                    {
                        a.LagiDateStatus8set = updateDate;
                        a.LagiTimeStatus8set = updateTime;
                    });
                    break;
            }

            return dbContext.SaveChanges();
        }

        public async Task<List<GoodInput>> ListGoodInput(long GraiObjectIsn)
        {
            var queryGraiObjectGroupIsn = (from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                           where g.GraiObjectIsn == GraiObjectIsn
                                             && g.GraiGroupType == "CLASS" && g.GraiValue != "FKE"
                                           select g.GraiObjectGroupIsn).Distinct().ToList();
            var queryLagi = (from v in (await GetDbSetAsync())
                             where v.Id == GraiObjectIsn
                             select v).FirstOrDefault();
            //var queryFlui = (await _fluiRepository.GetListAsync(x => x.FluiCustomerNo == queryLagi.LagiCustomerNoAirline && x.FluiFlightNo == queryLagi.LagiFlightNoIn && x.FluiScheduleDate == queryLagi.LagiFlightDateIn)).FirstOrDefault();
            // Lấy danh sách flight id theo grai group
            var lstFlightId = await (from grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                        .Where(x => x.GraiObjectIsn == GraiObjectIsn)
                        .Where(x => x.GraiGroupCode == "ISN")
                        .Where(x => x.GraiGroupType == "FLIGHT")
                                     select grai).Select(x => x.GraiNumericValue).Distinct().ToListAsync();
            // Ở phiên bản hiện tại sẽ lấy chuyến bay đầu tiên tính từ lúc hạ cánh.
            // Khi tính tiền theo chuyến bay thì cần thêm điều kiện là chuyến bay ấy đã được xuất kho chưa.
            var queryFlui = _fluiRepository.Where(x => lstFlightId.Contains(x.Id)).OrderBy(x => x.FluiLandedDate).FirstOrDefault();
            if (queryFlui == null)
            {
                throw new UserFriendlyException("Dữ liệu chuyến bay không đầy đủ!");
            }
            List<GoodInput> list = new();
            foreach (var item in queryGraiObjectGroupIsn)
            {
                var queryClass = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                 where g.GraiGroupType == "CLASS"
                                 where g.GraiGroupCode == "STORAGE"
                                 where g.GraiObjectGroupIsn == item
                                 where g.GraiObjectIsn == GraiObjectIsn
                                 where g.GraiValue != null
                                 select g.GraiValue;

                var queryWeight = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                  where g.GraiGroupType == "WEIGHT"
                                  where g.GraiGroupCode == "RECEIVED"
                                  where g.GraiObjectGroupIsn == item
                                  where g.GraiObjectIsn == GraiObjectIsn
                                  select g.GraiNumericValue;

                var queryGoodInput = (from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                      where g.GraiObjectGroupIsn == item
                                      where g.GraiObjectIsn == GraiObjectIsn
                                      select new GoodInput
                                      {
                                          Class = queryClass.FirstOrDefault() ?? "",
                                          AcceptDate = CommonFunction.ConverNumberToDate((int)queryFlui.FluiLandedDate).ToString("yyyy-MM-dd"),
                                          AcceptTime = queryFlui.FluiLandedTime.Remove(queryFlui.FluiLandedTime.Length - 2),
                                          LeaveDate = CommonFunction.ConverNumberToDate((int)queryFlui.FluiScheduleDate).ToString("yyyy-MM-dd"),
                                          LeaveTime = queryFlui.FluiLandedTime.Remove(queryFlui.FluiScheduleTime.Length - 2),
                                          Weight = queryWeight.FirstOrDefault(),
                                          CWeight = (decimal)queryLagi.LagiChargableWeight
                                      }).Distinct();
                list.Add(queryGoodInput.FirstOrDefault());
            }

            return list.Where(x => !string.IsNullOrEmpty(x.Class.Trim())).ToList();
        }

        public async Task<List<FlightWithGood>> ListFlightWithGood(long GraiObjectIsn)
        {
            List<FlightWithGood> list = new();
            var queryGraiObjectGroupIsn = (from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                           where g.GraiObjectIsn == GraiObjectIsn && g.GraiGroupType == "CLASS" && g.GraiValue != "FKE"
                                           select g.GraiObjectGroupIsn).Distinct().ToList();
            var queryLagi = (from v in (await GetDbSetAsync()) where v.Id == GraiObjectIsn select v).FirstOrDefault();

            var queryDLV = (from d in (await GetDbContextAsync()).DeliveryDockets
                            where d.LagiId == GraiObjectIsn && d.IsCanceled == false
                            select d).Distinct().ToList();
            if (queryDLV.Count == 0)
                throw new UserFriendlyException("Lô chưa có phiếu xuất kho !");
            //Trường hợp pxk tổng 1 lần
            if (queryDLV.Count == 1 && queryDLV.FirstOrDefault().Weight == queryLagi.LagiWeightExpected)
            {
                var flightQuery = (from grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                  .Where(x => x.GraiObjectIsn == GraiObjectIsn && x.GraiGroupCode == "ISN" && x.GraiGroupType == "FLIGHT")
                                   select grai.GraiNumericValue).Distinct().ToList();
                var results = _fluiRepository.Where(x => flightQuery.Contains(x.Id));

                foreach (var itemFlight in results)
                {
                    List<GoodInput> lstGoodInput = new();
                    FlightCharge flightCharge = new();
                    if (itemFlight == null)
                        throw new UserFriendlyException("Không tìm thấy thông tin chuyến bay trên hệ thống !");
                    if (itemFlight.FluiLandedDate == 0 || itemFlight.FluiLandedTime == null)
                        throw new UserFriendlyException("Chuyến bay chưa có ngày giờ hạ cánh !");

                    string time = CommonFunction.ConvertIntToTime(itemFlight.FluiLandedTime);
                    int Hour = Convert.ToInt32(time.Split(':')[0]);
                    int Minutes = Convert.ToInt32(time.Split(':')[1]);
                    flightCharge.Airline = itemFlight.FluiAl23letterCode;
                    flightCharge.FlightNo = itemFlight.FluiFlightNo;
                    flightCharge.FlightType = itemFlight.FluiType;
                    flightCharge.Etd = CommonFunction.ConverNumberToDate((int)itemFlight.FluiLandedDate).AddHours(Hour).AddMinutes(Minutes);
                    flightCharge.FlightDate = CommonFunction.ConverNumberToDate((int)itemFlight.FluiLandedDate).AddHours(Hour).AddMinutes(Minutes);

                    foreach (var item in queryGraiObjectGroupIsn)
                    {
                        var queryClass = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                         where g.GraiGroupType == "CLASS"
                                         where g.GraiGroupCode == "STORAGE"
                                         where g.GraiObjectGroupIsn == item
                                         where g.GraiObjectIsn == GraiObjectIsn
                                         where g.GraiValue != null
                                         select g.GraiValue;

                        var queryWeight = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                          where g.GraiGroupType == "WEIGHT"
                                          where g.GraiGroupCode == "RECEIVED"
                                          where g.GraiObjectGroupIsn == item
                                          where g.GraiObjectIsn == GraiObjectIsn
                                          select g.GraiNumericValue;

                        var queryPieces = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                          where g.GraiGroupType == "PIECES"
                                          where g.GraiGroupCode == "RECEIVED"
                                          where g.GraiObjectGroupIsn == item
                                          where g.GraiObjectIsn == GraiObjectIsn
                                          select g.GraiNumericValue;

                        long FlightIsn = long.Parse((from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                                     where g.GraiGroupType == "FLIGHT"
                                                     where g.GraiGroupCode == "ISN"
                                                     where g.GraiObjectGroupIsn == item
                                                     where g.GraiObjectIsn == GraiObjectIsn
                                                     select g.GraiValue).FirstOrDefault());
                        if (FlightIsn > 0)
                        {
                            if (FlightIsn == itemFlight.Id)
                            {
                                var itemGood = (from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                                where g.GraiObjectGroupIsn == item
                                                where g.GraiObjectIsn == GraiObjectIsn
                                                select new GoodInput
                                                {
                                                    Class = queryClass.FirstOrDefault() ?? "",
                                                    AcceptDate = ((DateTime)queryDLV.FirstOrDefault().CreateDate).ToString("yyyy-MM-dd"),
                                                    AcceptTime = CommonFunction.ConvertDateTimeToTimeHHMM((DateTime)queryDLV.FirstOrDefault().CreateDate),
                                                    LeaveDate = ((DateTime)queryDLV.FirstOrDefault().CreateDate).ToString("yyyy-MM-dd"),
                                                    LeaveTime = CommonFunction.ConvertDateTimeToTimeHHMM((DateTime)queryDLV.FirstOrDefault().CreateDate),
                                                    Weight = queryWeight.FirstOrDefault(),
                                                    CWeight = (decimal)queryLagi.LagiChargableWeight,
                                                    Pieces = (int)queryPieces.FirstOrDefault()
                                                }).FirstOrDefault();
                                if (itemGood.Weight != 0)
                                {
                                    lstGoodInput.Add(itemGood);
                                }
                            }
                        }
                    }
                    FlightWithGood flightWithGood = new()
                    {
                        flight = flightCharge,
                        lstGoodInput = lstGoodInput
                    };
                    list.Add(flightWithGood);
                }
            }
            else
            {
                //Trường hợp 1 lô có nhiều phiếu xuất kho
                foreach (var itemDLV in queryDLV)
                {
                    var queryFlui = (await _fluiRepository.GetListAsync(x => ((x.FluiAl23letterCode + x.FluiFlightNo) == itemDLV.AirlineCode)
                    && x.FluiLandedDate == itemDLV.AtaDate && x.FluiLandedTime == itemDLV.AtaTime)).FirstOrDefault();

                    List<GoodInput> lstGoodInput = new();
                    FlightCharge flightCharge = new();

                    if (queryFlui == null)
                        throw new UserFriendlyException("Không tìm thấy thông tin chuyến bay trên hệ thống !");
                    if (queryFlui.FluiLandedDate == 0 || queryFlui.FluiLandedTime == null)
                        throw new UserFriendlyException("Chuyến bay chưa có ngày giờ hạ cánh !");

                    string time = CommonFunction.ConvertIntToTime(queryFlui.FluiLandedTime);
                    int Hour = Convert.ToInt32(time.Split(':')[0]);
                    int Minutes = Convert.ToInt32(time.Split(':')[1]);

                    flightCharge.Airline = queryFlui.FluiAl23letterCode;
                    flightCharge.FlightNo = queryFlui.FluiFlightNo;
                    flightCharge.FlightType = queryFlui.FluiType;
                    flightCharge.Etd = CommonFunction.ConverNumberToDate((int)queryFlui.FluiLandedDate).AddHours(Hour).AddMinutes(Minutes);
                    flightCharge.FlightDate = CommonFunction.ConverNumberToDate((int)queryFlui.FluiLandedDate).AddHours(Hour).AddMinutes(Minutes);

                    foreach (var item in queryGraiObjectGroupIsn)
                    {
                        var queryClass = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                         where g.GraiGroupType == "CLASS"
                                         where g.GraiGroupCode == "STORAGE"
                                         where g.GraiObjectGroupIsn == item
                                         where g.GraiObjectIsn == GraiObjectIsn
                                         where g.GraiValue != null
                                         select g.GraiValue;

                        var queryWeight = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                          where g.GraiGroupType == "WEIGHT"
                                          where g.GraiGroupCode == "RECEIVED"
                                          where g.GraiObjectGroupIsn == item
                                          where g.GraiObjectIsn == GraiObjectIsn
                                          select g.GraiNumericValue;

                        var queryPieces = from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                          where g.GraiGroupType == "PIECES"
                                          where g.GraiGroupCode == "RECEIVED"
                                          where g.GraiObjectGroupIsn == item
                                          where g.GraiObjectIsn == GraiObjectIsn
                                          select g.GraiNumericValue;

                        long FlightIsn = long.Parse((from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                                     where g.GraiGroupType == "FLIGHT"
                                                     where g.GraiGroupCode == "ISN"
                                                     where g.GraiObjectGroupIsn == item
                                                     where g.GraiObjectIsn == GraiObjectIsn
                                                     select g.GraiValue).FirstOrDefault());
                        if (FlightIsn > 0)
                        {
                            if (queryFlui.Id == FlightIsn)
                            {
                                var itemGood = (from g in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                                where g.GraiObjectGroupIsn == item
                                                where g.GraiObjectIsn == GraiObjectIsn
                                                select new GoodInput
                                                {
                                                    Class = queryClass.FirstOrDefault() ?? "",
                                                    AcceptDate = ((DateTime)itemDLV.CreateDate).ToString("yyyy-MM-dd"),
                                                    AcceptTime = CommonFunction.ConvertDateTimeToTimeHHMM((DateTime)itemDLV.CreateDate),
                                                    LeaveDate = ((DateTime)itemDLV.CreateDate).ToString("yyyy-MM-dd"),
                                                    LeaveTime = CommonFunction.ConvertDateTimeToTimeHHMM((DateTime)itemDLV.CreateDate),
                                                    Weight = queryWeight.FirstOrDefault(),
                                                    CWeight = (decimal)queryLagi.LagiChargableWeight,
                                                    Pieces = (int)queryPieces.FirstOrDefault()
                                                }).FirstOrDefault();
                                lstGoodInput.Add(itemGood);
                            }
                        }
                    }

                    FlightWithGood flightWithGood = new()
                    {
                        flight = flightCharge,
                        lstGoodInput = lstGoodInput
                    };
                    list.Add(flightWithGood);
                }
            }
            return list;
        }

        public async Task<FlightCharge> GetFlightChargeAsync(long? lagiId = 0)
        {
            // Lấy danh sách flight id theo grai group
            var lstFlightId = await (from grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                        .Where(x => x.GraiObjectIsn == lagiId)
                        .Where(x => x.GraiGroupCode == "ISN")
                        .Where(x => x.GraiGroupType == "FLIGHT")
                                     select grai.GraiNumericValue).Distinct().ToListAsync();

            // Ở phiên bản hiện tại sẽ lấy chuyến bay đầu tiên tính từ lúc hạ cánh.
            // Khi tính tiền theo chuyến bay thì cần thêm điều kiện là chuyến bay ấy đã được xuất kho chưa.
            var queryFlui = _fluiRepository.Where(x => lstFlightId.Contains(x.Id)).OrderBy(x => x.FluiLandedDate).FirstOrDefault();
            if (queryFlui == null)
            {
                throw new UserFriendlyException("Chưa có thông tin chuyến bay !");
            }
            FlightCharge flightCharge = new();
            if (queryFlui != null)
            {
                flightCharge.Airline = queryFlui.FluiAl23letterCode;
                flightCharge.FlightNo = queryFlui.FluiFlightNo;
                flightCharge.FlightType = queryFlui.FluiType;
                //flightCharge.FlightDate = CommonFunction.ConverNumberToDate(queryFlui.FluiScheduleDate);
                if (queryFlui.FluiLandedDate == 0 || queryFlui.FluiLandedTime == null)
                {
                    throw new UserFriendlyException("Chuyến bay chưa có ngày giờ hạ cánh !");
                }
                else
                {
                    string time = CommonFunction.ConvertIntToTime(queryFlui.FluiLandedTime);
                    flightCharge.Etd = CommonFunction.ConverNumberToDate((int)queryFlui.FluiLandedDate).AddHours(Convert.ToInt32(time.Split(':')[0])).AddMinutes(Convert.ToInt32(time.Split(':')[1]));
                    flightCharge.FlightDate = CommonFunction.ConverNumberToDate((int)queryFlui.FluiLandedDate).AddHours(Convert.ToInt32(time.Split(':')[0])).AddMinutes(Convert.ToInt32(time.Split(':')[1]));
                    //string time = CommonFunction.ConvertIntToTime(queryFlui.FluiLandedTime);
                    //flightCharge.Etd = CommonFunction.ConverNumberToDate((int)queryFlui.FluiLandedDate).AddHours(Convert.ToInt32(time.Split(':')[0])).AddMinutes(Convert.ToInt32(time.Split(':')[1]));
                }
            }
            return flightCharge;
        }

        public virtual async Task<Flight> GetFlightAsync(long id, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var query = from lag in (await GetDbContextAsync()).Lagis
                        where lag.Id == id && lag.LagiLvgIn != " " && lag.LagiFlightNoIn != " "
                        select new Flight
                        {
                            Airline = lag.LagiLvgIn,
                            BookFlight = lag.LagiFlightNoIn,
                            BookFlightDate = lag.LagiFlightDateIn,
                            BookFlightTime = lag.LagiFlightTimeIn,
                            Ori = lag.LagiAwbOrigin,
                            Des = !String.IsNullOrWhiteSpace(lag.LagiAwbDest) ? lag.LagiAwbDest : "HAN",
                            LagiCustomerNoAirline = lag.LagiCustomerNoAirline,
                            Pcs = lag.LagiQuantityExpected,
                            GrossWeight = lag.LagiWeightExpected,
                            CargoTerminal = lag.LagiShedCode
                        };

            return query.PageBy(skipCount, maxResultCount).FirstOrDefault();
        }

        public async Task<PagedResult<Hawb>> GetPagedListUndeliveriedHawbByAsync(long vhclId, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var queryLagi = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiDateStatus4set == null && x.LagiDeleted == false)
                             join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn
                             join vhcl in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclImportExport == "IMPORT" && x.Id == vhclId) on vhld.VhldVehicleisn equals vhcl.Id
                             select lagi).Distinct();

            var queryGroup = from lagi in queryLagi
                             join grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos on lagi.Id equals grai.GraiObjectIsn into graiGroups
                             from graiGroup in graiGroups.DefaultIfEmpty()
                             where graiGroup.GraiGroupType == "CLASS" && graiGroup.GraiValue != "FKE"
                             select new
                             {
                                 LagiId = lagi.Id,
                                 lagi.LagiMawbNo,
                                 lagi.LagiMawbPrefix,
                                 lagi.LagiQuantityExpected,
                                 lagi.LagiWeightExpected,
                                 GraiId = graiGroup.Id,
                                 graiGroup.GraiObjectGroupIsn,
                                 lagi.LagiHawb
                             };

            var hawb = queryGroup.ToList().GroupBy(x => new { x.LagiId, x.LagiMawbNo, x.LagiMawbPrefix, x.LagiQuantityExpected, x.LagiWeightExpected, x.LagiHawb }).Select(y =>
              {
                  return new Hawb()
                  {
                      LagiId = y.Key.LagiId,
                      LagiMawbNo = y.Key.LagiMawbNo,
                      LagiMawbPrefix = y.Key.LagiMawbPrefix,
                      LagiQuantityExpected = y.Key.LagiQuantityExpected,
                      LagiWeightExpected = y.Key.LagiWeightExpected,
                      Group = y.GroupBy(z => z.GraiObjectGroupIsn).Count(),
                      LagiHawb = y.Key.LagiHawb
                  };
              });

            return hawb.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<List<Hawb>> GetListDeliveriedHawbByAsync(long vhclId, CancellationToken cancellationToken = default)
        {
            var queryLagi = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiDateStatus4set != null && x.LagiDeleted == false)
                             join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn
                             join vhcl in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclImportExport == "IMPORT" && x.Id == vhclId) on vhld.VhldVehicleisn equals vhcl.Id
                             select lagi).Distinct();

            var queryGroup = from lagi in queryLagi
                             join grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos on lagi.Id equals grai.GraiObjectIsn into graiGroups
                             from graiGroup in graiGroups.DefaultIfEmpty()
                             select new
                             {
                                 LagiId = lagi.Id,
                                 lagi.LagiMawbNo,
                                 lagi.LagiMawbPrefix,
                                 lagi.LagiQuantityExpected,
                                 lagi.LagiWeightExpected,
                                 GraiId = graiGroup.Id,
                                 graiGroup.GraiObjectGroupIsn
                             };

            var hawb = queryGroup.ToList().GroupBy(x => new { x.LagiId, x.LagiMawbNo, x.LagiMawbPrefix, x.LagiQuantityExpected, x.LagiWeightExpected }).Select(y =>
            {
                return new Hawb()
                {
                    LagiId = y.Key.LagiId,
                    LagiMawbNo = y.Key.LagiMawbNo,
                    LagiMawbPrefix = y.Key.LagiMawbPrefix,
                    LagiQuantityExpected = y.Key.LagiQuantityExpected,
                    LagiWeightExpected = y.Key.LagiWeightExpected,
                    Group = y.GroupBy(z => z.GraiObjectGroupIsn).Count()
                };
            });

            return hawb.ToList();
        }

        public async Task<PagedResult<Hawb>> GetPagedListHawbForSelect2Async(string term, long kundId, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var hawb = "";
            var mawbPrefix = "";
            var mawbNo = "";

            if (!string.IsNullOrWhiteSpace(term) && term.IndexOf('/') != -1)
                hawb = term.Substring(term.IndexOf('/') + 1);//Nếu có dấu / thì lấy phần sau đấu / là hawb

            if (!string.IsNullOrWhiteSpace(term) && term.IndexOf('-') != -1)
            {
                mawbPrefix = term.Substring(0, term.IndexOf('-'));//Nếu có dấu - thì lấy phần trước dấu - là mawb prefix
                if (term.IndexOf('/') != -1)
                    mawbNo = term.Substring(term.IndexOf('-') + 1, term.IndexOf('/') - term.IndexOf('-') - 1);//Nếu có dấu / thì lấy phần sau dấu - và trước dấu / là mawb no
                else
                    mawbNo = term.Substring(term.IndexOf('-') + 1);//Nếu không có dấu / thì lấy phần sau dấu - là mawb no
            }

            IQueryable<Hawb> query = null;
            if (!string.IsNullOrWhiteSpace(term) && term.IndexOf('-') == -1 && term.IndexOf('/') == -1)//Nếu không có dấu - và dấu / thì search theo cả mawb và hawb
            {
                mawbPrefix = term;
                hawb = term;
                query = from lagi in (await GetDbContextAsync()).Lagis
                        .WhereIf(kundId != 0, x => x.LagiCurrentAgent == kundId)
                        .WhereIf(!string.IsNullOrWhiteSpace(mawbPrefix) && !string.IsNullOrWhiteSpace(mawbPrefix), x => x.LagiMawbPrefix.Contains(mawbPrefix) || x.LagiHawb.Contains(hawb))
                        select new Hawb()
                        {
                            LagiMawbNo = lagi.LagiMawbNo,
                            LagiMawbPrefix = lagi.LagiMawbPrefix,
                            LagiHawb = lagi.LagiHawb,
                            LagiId = lagi.Id,
                        };
            }
            else
            {
                query = from lagi in (await GetDbContextAsync()).Lagis
                            .WhereIf(kundId != 0, x => x.LagiCurrentAgent == kundId)
                            .WhereIf(!string.IsNullOrWhiteSpace(mawbPrefix), x => x.LagiMawbPrefix.Contains(mawbPrefix))
                            .WhereIf(!string.IsNullOrWhiteSpace(mawbNo), x => x.LagiMawbNo.ToString().Contains(mawbNo))
                            .WhereIf(!string.IsNullOrWhiteSpace(hawb), x => x.LagiHawb.Contains(hawb))
                            .Where(x => x.LagiDeleted == false)
                        select new Hawb()
                        {
                            LagiMawbNo = lagi.LagiMawbNo,
                            LagiMawbPrefix = lagi.LagiMawbPrefix,
                            LagiHawb = lagi.LagiHawb,
                            LagiId = lagi.Id,
                        };
            }
            return query.PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<Hawb>> GetPagedListHawbForDatatableAsync(string[] mawbHawbs, string shipperName = null, string agentName = null, string airline = null
            , string flightNumber = null, string flightDateFrom = null, string flightDateTo = null, bool deleted = false, bool isIgnoreStatus4 = false, string isCleared = null
            , int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            if (!string.IsNullOrWhiteSpace(agentName) && agentName.Split("-").Length == 2)
            {
                agentName = agentName.Split("-")[1].Trim();
            }

            if (!string.IsNullOrWhiteSpace(shipperName) && shipperName.Split("-").Length == 2)
            {
                shipperName = shipperName.Split("-")[1].Trim();
            }

            //Chia mảng filter thành 2 phần là search theo mawb và hawb
            var mawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') != -1 && x.IndexOf('/') == -1);
            var hawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('/') != -1 && x.IndexOf('/') != -1);
            var onlyHawbNumbers = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') == -1 && x.IndexOf('/') == -1);
            var unknows = mawbHawbs.Where(x => x.IndexOf('?') != -1);

            //lấy lagi có cả mawb và hawb
            var lagiMawbHawb = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo != 0)
                                .WhereIf(isIgnoreStatus4, x => x.LagiDateStatus4set == null && x.LagiTimeStatus4set == null)
                                .WhereIf(isCleared == "true", x => x.LagiDateStatus3set != null && x.LagiTimeStatus3set != null)
                                .WhereIf(isCleared == "false", x => x.LagiDateStatus3set == null && x.LagiTimeStatus3set == null)
                                select new
                                {
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix
                                }).Distinct();

            //loại trừ những lagi có cả mawb và hawb
            var lagiOnlyMawb = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo == 0)
                                .Where(x => !lagiMawbHawb.Any(y => x.LagiMawbNo == y.LagiMawbNo && x.LagiMawbPrefix == y.LagiMawbPrefix))
                                .WhereIf(isIgnoreStatus4, x => x.LagiDateStatus4set == null && x.LagiTimeStatus4set == null)
                                .WhereIf(isCleared == "true", x => x.LagiDateStatus3set != null && x.LagiTimeStatus3set != null)
                                .WhereIf(isCleared == "false", x => x.LagiDateStatus3set == null && x.LagiTimeStatus3set == null)
                               select new
                               {
                                   lagi.Id
                               };

            var lagiKundQuery = from lagi in (await GetDbSetAsync()).Where(x => x.LagiMasterIdentNo != 0 || lagiOnlyMawb.Any(y => x.Id == y.Id))
                                .WhereIf(isIgnoreStatus4, x => x.LagiDateStatus4set == null && x.LagiTimeStatus4set == null)
                                .WhereIf(isCleared == "true", x => x.LagiDateStatus3set != null && x.LagiTimeStatus3set != null)
                                .WhereIf(isCleared == "false", x => x.LagiDateStatus3set == null && x.LagiTimeStatus3set == null)
                             .WhereIf(!deleted, x => x.LagiDeleted == false)
                                                            .Where(x =>
                                                              (mawbs.Count() == 0 && hawbs.Count() == 0 && onlyHawbNumbers.Count() == 0 && unknows.Count() == 0)
                                                              || (mawbs.Count() > 0 && mawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString()))
                                                              || (hawbs.Count() > 0 && hawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString() + '/' + x.LagiHawb))
                                                              || (onlyHawbNumbers.Count() > 0 && onlyHawbNumbers.Contains(x.LagiHawb))
                                                              || (unknows.Count() > 0 &&
                                                              (
                                                                unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains("?-?/" + x.LagiHawb)
                                                                || unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/?")
                                                              )
                                                              ))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(shipperName), x => x.LagiShipperName.ToUpper().Trim().Equals(shipperName.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(agentName), x => x.LagiNotifyName.ToUpper().Trim().Equals(agentName.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(airline), x => x.LagiLvgIn.ToUpper().Trim().Equals(airline.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightNumber), x => x.LagiFlightNoIn.Equals(flightNumber.Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                                join kund in (await GetDbContextAsync()).Kunds on lagi.LagiCurrentAgent.Value equals kund.Id into kundGroups
                                from kundGroup in kundGroups.DefaultIfEmpty()
                                select new Hawb
                                {
                                    LagiId = lagi.Id,
                                    LagiMawbNo = lagi.LagiMawbNo,
                                    LagiMawbPrefix = lagi.LagiMawbPrefix,
                                    LagiQuantityExpected = lagi.LagiQuantityExpected,
                                    LagiWeightExpected = lagi.LagiWeightExpected,
                                    KundId = kundGroup != null ? kundGroup.Id : 0,
                                    Kund3letterCode = kundGroup != null ? kundGroup.Kund3letterCode : "",
                                    LagiHawb = lagi.LagiHawb,
                                    LagiLvgIn = lagi.LagiLvgIn,
                                    LagiFlightNoIn = lagi.LagiFlightNoIn,
                                    LagiFlightDateIn = lagi.LagiFlightDateIn,
                                    LagiFlightTimeIn = lagi.LagiFlightTimeIn,
                                    LagiShedCode = lagi.LagiShedCode,
                                    LagiShedCodeAtDestination = lagi.LagiShedAtDestination,
                                    LagiExpandedCommodity = lagi.LagiExpandedCommodity,
                                    LagiTso = lagi.LagiTso,
                                    LagiAwbOrigin = lagi.LagiAwbOrigin,
                                    LagiAwbDest = lagi.LagiAwbDest,
                                    LagiQuantityReceived = lagi.LagiQuantityReceived,
                                    LagiWeightReceived = lagi.LagiWeightReceived,
                                    LagiChargableWeight = lagi.LagiChargableWeight,
                                    LagiDateStatus1Set = lagi.LagiDateStatus1set,
                                    LagiTimeStatus1Set = lagi.LagiTimeStatus1set,
                                    LagiDateStatus3Set = lagi.LagiDateStatus3set,
                                    LagiTimeStatus3Set = lagi.LagiTimeStatus3set,
                                    LagiDateStatus4Set = lagi.LagiDateStatus4set,
                                    LagiTimeStatus4Set = lagi.LagiTimeStatus4set,
                                    LagiDateStatus5Set = lagi.LagiDateStatus5set,
                                    LagiTimeStatus5Set = lagi.LagiTimeStatus5set,
                                    LagiDateStatus6Set = lagi.LagiDateStatus6set,
                                    LagiTimeStatus6Set = lagi.LagiTimeStatus6set,
                                    LagiDateStatus7Set = lagi.LagiDateStatus7set,
                                    LagiTimeStatus7Set = lagi.LagiTimeStatus7set,
                                    LagiDateStatus8Set = lagi.LagiDateStatus8set,
                                    LagiTimeStatus8Set = lagi.LagiTimeStatus8set,
                                    LagiMasterIndentNo = lagi.LagiMasterIdentNo,
                                    LagiCustomRefNo = lagi.LagiCustomRefNo,
                                };

            var lagisKund = lagiKundQuery.ToList();
            var flights = _fluiRepository.Select(x => new { x.FluiLandedDate, x.FluiLandedTime, x.FluiAl23letterCode, x.FluiFlightNo, x.FluiScheduleDate, x.FluiScheduleTime }).ToList();

            foreach (var lagiKund in lagisKund)
            {
                var flui = flights.Where(x => x.FluiAl23letterCode == lagiKund.LagiLvgIn && x.FluiFlightNo == lagiKund.LagiFlightNoIn && x.FluiScheduleDate == lagiKund.LagiFlightDateIn && x.FluiScheduleTime == lagiKund.LagiFlightTimeIn).FirstOrDefault();
                lagiKund.FluiLandedDate = flui?.FluiLandedDate;
                lagiKund.FluiLandedTime = flui?.FluiLandedTime;
            }

            var lagiTruckPickupQuery = from lagi in lagiKundQuery
                                       join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.LagiId equals vhld.VhldObjectisn into vhldGroups
                                       from vhldGroup in vhldGroups.DefaultIfEmpty()
                                       join vhcl in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclImportExport == "IMPORT") on vhldGroup.VhldVehicleisn equals vhcl.Id into vhclGroups
                                       from vhclGroup in vhclGroups.DefaultIfEmpty()
                                       select new TruckPickupHawb
                                       {
                                           LagiId = lagi.LagiId,
                                           VhclRegNo = vhclGroup.VehicRegNo,
                                           VhclLoadingLeftDate = vhclGroup.VhclLoadingLeftDate,
                                           VhclLoadingLeftTime = vhclGroup.VhclLoadingLeftTime,
                                           VhclLoadingArrivalDate = vhclGroup.VhclLoadingArrivalDate,
                                           VhclLoadingArrivalTime = vhclGroup.VhclLoadingArrivalTime,
                                           VhclCompletedDate = vhclGroup.VhclCompletedDate,
                                           VhclCompletedTime = vhclGroup.VhclCompletedTime,
                                           VhclTransitToFactoryDate = vhclGroup.VhclTransitToFactoryDate,
                                           VhclTransitToFactoryTime = vhclGroup.VhclTransitToFactoryTime,
                                           VhclArrivedFactoryDate = vhclGroup.VhclArrivedFactoryDate,
                                           VhclArrivedFactoryTime = vhclGroup.VhclArrivedFactoryTime,
                                           VhclUnloadingArrivalDate = vhclGroup.VhclUnloadingArrivalDate,
                                           VhclUnloadingArrivalTime = vhclGroup.VhclUnloadingArrivalTime
                                       };

            var fquery = from lagiKund in lagisKund
                         join lagiTruckPickup in lagiTruckPickupQuery.ToList() on lagiKund.LagiId equals lagiTruckPickup.LagiId
                         group lagiTruckPickup by lagiKund into lagiTruckPickupGroups
                         select new Hawb
                         {
                             LagiId = lagiTruckPickupGroups.Key.LagiId,
                             LagiMawbNo = lagiTruckPickupGroups.Key.LagiMawbNo,
                             LagiMawbPrefix = lagiTruckPickupGroups.Key.LagiMawbPrefix,
                             LagiQuantityExpected = lagiTruckPickupGroups.Key.LagiQuantityExpected,
                             LagiWeightExpected = lagiTruckPickupGroups.Key.LagiWeightExpected,
                             KundId = lagiTruckPickupGroups.Key.KundId,
                             Kund3letterCode = lagiTruckPickupGroups.Key.Kund3letterCode,
                             LagiHawb = lagiTruckPickupGroups.Key.LagiHawb,
                             LagiLvgIn = lagiTruckPickupGroups.Key.LagiLvgIn,
                             LagiFlightNoIn = lagiTruckPickupGroups.Key.LagiFlightNoIn,
                             LagiFlightDateIn = lagiTruckPickupGroups.Key.LagiFlightDateIn,
                             LagiFlightTimeIn = lagiTruckPickupGroups.Key.LagiFlightTimeIn,
                             LagiShedCode = lagiTruckPickupGroups.Key.LagiShedCode,
                             LagiShedCodeAtDestination = lagiTruckPickupGroups.Key.LagiShedCodeAtDestination,
                             FluiLandedDate = lagiTruckPickupGroups.Key.FluiLandedDate,
                             FluiLandedTime = lagiTruckPickupGroups.Key.FluiLandedTime,
                             VhclRegNo = string.Join(',', lagiTruckPickupGroups.Where(x => x.VhclRegNo != null).Select(x => x.VhclRegNo).Distinct()),
                             IsTransitToWarehouse = CheckTransitToWarehouse(lagiTruckPickupGroups),
                             IsArrivedWarehouse = CheckArrivedWarehouse(lagiTruckPickupGroups),
                             IsCompletedWarehouse = CheckCompletedWarehouse(lagiTruckPickupGroups),
                             IsTransitToFactory = CheckTransitToFactory(lagiTruckPickupGroups),
                             IsArrivedFactory = CheckArrivedFactory(lagiTruckPickupGroups),
                             NewestLoadingLeftDatetime = GetNewestLoadingLeftDatetime(lagiTruckPickupGroups),
                             NewestUnloadingArrivalDatetime = GetNewestUnloadingArrivalDatetime(lagiTruckPickupGroups),
                             NewestCompletedWarehouseDatetime = GetNewestCompletedWarehouseDatetime(lagiTruckPickupGroups),
                             NewestTransitToFactoryDatetime = GetNewestTransitToFactoryDatetime(lagiTruckPickupGroups),
                             NewestArrivedFactoryDatetime = GetNewestArrivedFactoryDatetime(lagiTruckPickupGroups),
                             LagiDateStatus1Set = lagiTruckPickupGroups.Key.LagiDateStatus1Set,
                             LagiTimeStatus1Set = lagiTruckPickupGroups.Key.LagiTimeStatus1Set,
                             LagiDateStatus3Set = lagiTruckPickupGroups.Key.LagiDateStatus3Set,
                             LagiTimeStatus3Set = lagiTruckPickupGroups.Key.LagiTimeStatus3Set,
                             LagiDateStatus4Set = lagiTruckPickupGroups.Key.LagiDateStatus4Set,
                             LagiTimeStatus4Set = lagiTruckPickupGroups.Key.LagiTimeStatus4Set,
                             LagiDateStatus5Set = lagiTruckPickupGroups.Key.LagiDateStatus5Set,
                             LagiTimeStatus5Set = lagiTruckPickupGroups.Key.LagiTimeStatus5Set,
                             LagiDateStatus6Set = lagiTruckPickupGroups.Key.LagiDateStatus6Set,
                             LagiTimeStatus6Set = lagiTruckPickupGroups.Key.LagiTimeStatus6Set,
                             LagiDateStatus7Set = lagiTruckPickupGroups.Key.LagiDateStatus7Set,
                             LagiTimeStatus7Set = lagiTruckPickupGroups.Key.LagiTimeStatus7Set,
                             LagiDateStatus8Set = lagiTruckPickupGroups.Key.LagiDateStatus8Set,
                             LagiTimeStatus8Set = lagiTruckPickupGroups.Key.LagiTimeStatus8Set,
                             LagiMasterIndentNo = lagiTruckPickupGroups.Key.LagiMasterIndentNo,
                             LagiAwbOrigin = lagiTruckPickupGroups.Key.LagiAwbOrigin,
                             LagiAwbDest = lagiTruckPickupGroups.Key.LagiAwbDest,
                             LagiQuantityReceived = lagiTruckPickupGroups.Key.LagiQuantityReceived,
                             LagiWeightReceived = lagiTruckPickupGroups.Key.LagiWeightReceived,
                             LagiChargableWeight = lagiTruckPickupGroups.Key.LagiChargableWeight,
                             NewestTruckStatusDatetime = GetNewestTruckStatusDateTime(lagiTruckPickupGroups),
                             LagiCustomRefNo = lagiTruckPickupGroups.Key.LagiCustomRefNo,
                         };

            fquery = fquery.OrderByDescending(x => x.NewestStatusDatetime);
            return fquery.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<Hawb> GetHawbByAsync(long hawbId, CancellationToken cancellationToken = default)
        {
            var lagiQuery = from lagi in (await GetDbSetAsync()).Where(x => x.Id == hawbId)
                            select new Hawb
                            {
                                LagiId = lagi.Id,
                                LagiMawbNo = lagi.LagiMawbNo,
                                LagiMawbPrefix = lagi.LagiMawbPrefix,
                                LagiQuantityExpected = lagi.LagiQuantityExpected,
                                LagiWeightExpected = lagi.LagiWeightExpected,
                                LagiHawb = lagi.LagiHawb,
                                LagiLvgIn = lagi.LagiLvgIn,
                                LagiFlightNoIn = lagi.LagiFlightNoIn,
                                LagiFlightDateIn = lagi.LagiFlightDateIn,
                                LagiFlightTimeIn = lagi.LagiFlightTimeIn,
                                LagiShedCode = lagi.LagiShedCode,
                                LagiShedCodeAtDestination = lagi.LagiShedAtDestination,
                                LagiExpandedCommodity = lagi.LagiExpandedCommodity,
                                LagiTso = lagi.LagiTso,
                                LagiAwbOrigin = lagi.LagiAwbOrigin,
                                LagiAwbDest = lagi.LagiAwbDest,
                                LagiQuantityReceived = lagi.LagiQuantityReceived,
                                LagiWeightReceived = lagi.LagiWeightReceived,
                                LagiChargableWeight = lagi.LagiChargableWeight,
                                LagiDateStatus1Set = lagi.LagiDateStatus1set,
                                LagiTimeStatus1Set = lagi.LagiTimeStatus1set,
                                LagiDateStatus3Set = lagi.LagiDateStatus3set,
                                LagiTimeStatus3Set = lagi.LagiTimeStatus3set,
                                LagiDateStatus4Set = lagi.LagiDateStatus4set,
                                LagiTimeStatus4Set = lagi.LagiTimeStatus4set,
                                LagiDateStatus5Set = lagi.LagiDateStatus5set,
                                LagiTimeStatus5Set = lagi.LagiTimeStatus5set,
                                LagiDateStatus6Set = lagi.LagiDateStatus6set,
                                LagiTimeStatus6Set = lagi.LagiTimeStatus6set,
                                LagiDateStatus7Set = lagi.LagiDateStatus7set,
                                LagiTimeStatus7Set = lagi.LagiTimeStatus7set,
                                LagiDateStatus8Set = lagi.LagiDateStatus8set,
                                LagiTimeStatus8Set = lagi.LagiTimeStatus8set,
                                LagiMasterIndentNo = lagi.LagiMasterIdentNo,
                            };

            var lagiTruckPickupQuery = from lagi in lagiQuery
                                       join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.LagiId equals vhld.VhldObjectisn into vhldGroups
                                       from vhldGroup in vhldGroups.DefaultIfEmpty()
                                       join vhcl in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclImportExport == "IMPORT") on vhldGroup.VhldVehicleisn equals vhcl.Id into vhclGroups
                                       from vhclGroup in vhclGroups.DefaultIfEmpty()
                                       select new TruckPickupHawb
                                       {
                                           LagiId = lagi.LagiId,
                                           VhclRegNo = vhclGroup.VehicRegNo,
                                           VhclLoadingLeftDate = vhclGroup.VhclLoadingLeftDate,
                                           VhclLoadingLeftTime = vhclGroup.VhclLoadingLeftTime,
                                           VhclLoadingArrivalDate = vhclGroup.VhclLoadingArrivalDate,
                                           VhclLoadingArrivalTime = vhclGroup.VhclLoadingArrivalTime,
                                           VhclCompletedDate = vhclGroup.VhclCompletedDate,
                                           VhclCompletedTime = vhclGroup.VhclCompletedTime,
                                           VhclTransitToFactoryDate = vhclGroup.VhclTransitToFactoryDate,
                                           VhclTransitToFactoryTime = vhclGroup.VhclTransitToFactoryTime,
                                           VhclArrivedFactoryDate = vhclGroup.VhclArrivedFactoryDate,
                                           VhclArrivedFactoryTime = vhclGroup.VhclArrivedFactoryTime,
                                           VhclUnloadingArrivalDate = vhclGroup.VhclUnloadingArrivalDate,
                                           VhclUnloadingArrivalTime = vhclGroup.VhclUnloadingArrivalTime,
                                       };

            var fquery = from lagi in lagiQuery.ToList()
                         join lagiTruckPickup in lagiTruckPickupQuery.ToList() on lagi.LagiId equals lagiTruckPickup.LagiId
                         group lagiTruckPickup by lagi into lagiTruckPickupGroups
                         select new Hawb
                         {
                             LagiId = lagiTruckPickupGroups.Key.LagiId,
                             LagiMawbNo = lagiTruckPickupGroups.Key.LagiMawbNo,
                             LagiMawbPrefix = lagiTruckPickupGroups.Key.LagiMawbPrefix,
                             LagiQuantityExpected = lagiTruckPickupGroups.Key.LagiQuantityExpected,
                             LagiWeightExpected = lagiTruckPickupGroups.Key.LagiWeightExpected,
                             KundId = lagiTruckPickupGroups.Key.KundId,
                             Kund3letterCode = lagiTruckPickupGroups.Key.Kund3letterCode,
                             LagiHawb = lagiTruckPickupGroups.Key.LagiHawb,
                             LagiLvgIn = lagiTruckPickupGroups.Key.LagiLvgIn,
                             LagiFlightNoIn = lagiTruckPickupGroups.Key.LagiFlightNoIn,
                             LagiFlightDateIn = lagiTruckPickupGroups.Key.LagiFlightDateIn,
                             LagiFlightTimeIn = lagiTruckPickupGroups.Key.LagiFlightTimeIn,
                             LagiShedCode = lagiTruckPickupGroups.Key.LagiShedCode,
                             LagiShedCodeAtDestination = lagiTruckPickupGroups.Key.LagiShedCodeAtDestination,
                             FluiLandedDate = lagiTruckPickupGroups.Key.FluiLandedDate,
                             FluiLandedTime = lagiTruckPickupGroups.Key.FluiLandedTime,
                             VhclRegNo = string.Join(',', lagiTruckPickupGroups.Where(x => x.VhclRegNo != null).Select(x => x.VhclRegNo).Distinct()),
                             IsTransitToWarehouse = CheckTransitToWarehouse(lagiTruckPickupGroups),
                             IsArrivedWarehouse = CheckArrivedWarehouse(lagiTruckPickupGroups),
                             IsCompletedWarehouse = CheckCompletedWarehouse(lagiTruckPickupGroups),
                             IsTransitToFactory = CheckTransitToFactory(lagiTruckPickupGroups),
                             IsArrivedFactory = CheckArrivedFactory(lagiTruckPickupGroups),
                             NewestLoadingLeftDatetime = GetNewestLoadingLeftDatetime(lagiTruckPickupGroups),
                             NewestUnloadingArrivalDatetime = GetNewestUnloadingArrivalDatetime(lagiTruckPickupGroups),
                             NewestCompletedWarehouseDatetime = GetNewestCompletedWarehouseDatetime(lagiTruckPickupGroups),
                             NewestTransitToFactoryDatetime = GetNewestTransitToFactoryDatetime(lagiTruckPickupGroups),
                             NewestArrivedFactoryDatetime = GetNewestArrivedFactoryDatetime(lagiTruckPickupGroups),
                             LagiDateStatus1Set = lagiTruckPickupGroups.Key.LagiDateStatus1Set,
                             LagiTimeStatus1Set = lagiTruckPickupGroups.Key.LagiTimeStatus1Set,
                             LagiDateStatus3Set = lagiTruckPickupGroups.Key.LagiDateStatus3Set,
                             LagiTimeStatus3Set = lagiTruckPickupGroups.Key.LagiTimeStatus3Set,
                             LagiDateStatus4Set = lagiTruckPickupGroups.Key.LagiDateStatus4Set,
                             LagiTimeStatus4Set = lagiTruckPickupGroups.Key.LagiTimeStatus4Set,
                             LagiDateStatus5Set = lagiTruckPickupGroups.Key.LagiDateStatus5Set,
                             LagiTimeStatus5Set = lagiTruckPickupGroups.Key.LagiTimeStatus5Set,
                             LagiDateStatus6Set = lagiTruckPickupGroups.Key.LagiDateStatus6Set,
                             LagiTimeStatus6Set = lagiTruckPickupGroups.Key.LagiTimeStatus6Set,
                             LagiDateStatus7Set = lagiTruckPickupGroups.Key.LagiDateStatus7Set,
                             LagiTimeStatus7Set = lagiTruckPickupGroups.Key.LagiTimeStatus7Set,
                             LagiDateStatus8Set = lagiTruckPickupGroups.Key.LagiDateStatus8Set,
                             LagiTimeStatus8Set = lagiTruckPickupGroups.Key.LagiTimeStatus8Set,
                             LagiMasterIndentNo = lagiTruckPickupGroups.Key.LagiMasterIndentNo,
                             LagiAwbOrigin = lagiTruckPickupGroups.Key.LagiAwbOrigin,
                             LagiAwbDest = lagiTruckPickupGroups.Key.LagiAwbDest,
                             LagiQuantityReceived = lagiTruckPickupGroups.Key.LagiQuantityReceived,
                             LagiWeightReceived = lagiTruckPickupGroups.Key.LagiWeightReceived,
                             LagiChargableWeight = lagiTruckPickupGroups.Key.LagiChargableWeight,
                             NewestTruckStatusDatetime = GetNewestTruckStatusDateTime(lagiTruckPickupGroups),
                         };

            return fquery.FirstOrDefault();
        }

        public bool CheckTransitToWarehouse(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)
        {
            return lagiTruckPickupGroups.Any(x => x.VhclLoadingLeftDate != null);
        }

        public bool CheckArrivedWarehouse(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)
        {
            return lagiTruckPickupGroups.All(x => x.VhclUnloadingArrivalDate != null);
        }

        public bool CheckCompletedWarehouse(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)
        {
            return lagiTruckPickupGroups.All(x => x.VhclCompletedDate != null);
        }

        public bool CheckTransitToFactory(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)
        {
            return lagiTruckPickupGroups.Any(x => x.VhclTransitToFactoryDate != null);
        }

        public bool CheckArrivedFactory(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)
        {
            return lagiTruckPickupGroups.All(x => x.VhclArrivedFactoryDate != null);
        }

        public DateTime? GetNewestLoadingLeftDatetime(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)//Đang trung chuyển đến kho - TRANSIT TO WAREHOUSE
        {
            var newest = lagiTruckPickupGroups.Select(x => { return x.VhclLoadingLeftDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclLoadingLeftDate.Value, x.VhclLoadingLeftTime) : DateTime.MinValue; }).Max();
            return newest == DateTime.MinValue ? null : newest;
        }

        public DateTime? GetNewestUnloadingArrivalDatetime(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)//Đã đến kho - ARRIVED WAREHOUSE
        {
            var newest = lagiTruckPickupGroups.Select(x => { return x.VhclUnloadingArrivalDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclUnloadingArrivalDate.Value, x.VhclUnloadingArrivalTime) : DateTime.MinValue; }).Max();
            return newest == DateTime.MinValue ? null : newest;
        }

        public DateTime? GetNewestCompletedWarehouseDatetime(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)//Đang lưu kho - COMPLETED WAREHOUSE
        {
            var newest = lagiTruckPickupGroups.Select(x => { return x.VhclCompletedDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclCompletedDate.Value, x.VhclCompletedTime) : DateTime.MinValue; }).Max();
            return newest == DateTime.MinValue ? null : newest;
        }

        public DateTime? GetNewestTransitToFactoryDatetime(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)//Đang trung chuyển đến địa điểm trả hang - TRANSIT TO FACTORY
        {
            var newest = lagiTruckPickupGroups.Select(x => { return x.VhclTransitToFactoryDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclTransitToFactoryDate.Value, x.VhclTransitToFactoryTime) : DateTime.MinValue; }).Max();
            return newest == DateTime.MinValue ? null : newest;
        }

        public DateTime? GetNewestArrivedFactoryDatetime(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)//Đã đến địa điểm trả hàng - ARRIVED FACTORY
        {
            var newest = lagiTruckPickupGroups.Select(x => { return x.VhclArrivedFactoryDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclArrivedFactoryDate.Value, x.VhclArrivedFactoryTime) : DateTime.MinValue; }).Max();
            return newest == DateTime.MinValue ? null : newest;
        }

        private DateTime GetNewestTruckStatusDateTime(IGrouping<Hawb, TruckPickupHawb> lagiTruckPickupGroups)
        {
            var arrivedFactoryDateTime = lagiTruckPickupGroups.Select(x => { return x.VhclArrivedFactoryDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclArrivedFactoryDate.Value, x.VhclArrivedFactoryTime) : DateTime.MinValue; });
            var completedDateTime = lagiTruckPickupGroups.Select(x => { return x.VhclCompletedDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclCompletedDate.Value, x.VhclCompletedTime) : DateTime.MinValue; });
            var loadingArrivalDateTime = lagiTruckPickupGroups.Select(x => { return x.VhclLoadingArrivalDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclLoadingArrivalDate.Value, x.VhclLoadingArrivalTime) : DateTime.MinValue; });
            var loadingLeftDateTime = lagiTruckPickupGroups.Select(x => { return x.VhclLoadingLeftDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclLoadingLeftDate.Value, x.VhclLoadingLeftTime) : DateTime.MinValue; });
            var transitToFactoryDateTime = lagiTruckPickupGroups.Select(x => { return x.VhclTransitToFactoryDate.HasValue ? CommonFunction.CombineDateAndTime(x.VhclTransitToFactoryDate.Value, x.VhclTransitToFactoryTime) : DateTime.MinValue; });

            var combineList = new List<DateTime>();
            combineList.AddRange(arrivedFactoryDateTime);
            combineList.AddRange(completedDateTime);
            combineList.AddRange(loadingArrivalDateTime);
            combineList.AddRange(loadingLeftDateTime);
            combineList.AddRange(transitToFactoryDateTime);
            return combineList.Max(); //Lấy trạng thái xe mới nhất
        }

        public async Task<PagedResult<Flight>> GetPagedListFlightByHawbForDatatableAsync(long hawbId, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var hawbQuery = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiDeleted == false).Where(x => x.Id == hawbId)
                            select lagi;
            var hawb = await hawbQuery.FirstOrDefaultAsync();

            var flightQuery = from grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                        .Where(x => x.GraiObjectIsn == hawbId)
                        .Where(x => x.GraiGroupCode == "ISN")
                        .Where(x => x.GraiGroupType == "FLIGHT")
                              select grai;

            var piecesQuery = from grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                              .Where(x => x.GraiObjectIsn == hawbId)
                              .Where(x => x.GraiGroupCode == "RECEIVED")
                              .Where(x => x.GraiGroupType == "PIECES")
                              select grai;

            var grossWeightQuery = from grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                              .Where(x => x.GraiObjectIsn == hawbId)
                              .Where(x => x.GraiGroupCode == "RECEIVED")
                              .Where(x => x.GraiGroupType == "WEIGHT")
                                   select grai;

            var piecesAndGwByFlightQuery = from flightGrai in flightQuery
                                           join piecesGrai in piecesQuery on new { flightGrai.GraiObjectIsn, flightGrai.GraiObjectGroupIsn } equals new { piecesGrai.GraiObjectIsn, piecesGrai.GraiObjectGroupIsn }
                                           join grossWeightGrai in grossWeightQuery on new { flightGrai.GraiObjectIsn, flightGrai.GraiObjectGroupIsn } equals new { grossWeightGrai.GraiObjectIsn, grossWeightGrai.GraiObjectGroupIsn }
                                           select new
                                           {
                                               FlightId = flightGrai.GraiNumericValue,
                                               Pieces = piecesGrai.GraiNumericValue,
                                               GrossWeight = grossWeightGrai.GraiNumericValue
                                           };

            var piecesAndGwByFlight = await piecesAndGwByFlightQuery.ToListAsync();

            var totalPiecesAndGwByFlight = piecesAndGwByFlight.GroupBy(x => x.FlightId).Select(x => new { FlightId = x.Key, TotalPieces = x.Select(y => y.Pieces).Sum(), TotalGw = x.Select(y => y.GrossWeight).Sum() });

            var flights = await flightQuery.ToListAsync();
            var flightIds = await flightQuery.Select(x => x.GraiNumericValue).Distinct().ToListAsync();
            var results = _fluiRepository.Where(x => flightIds.Contains(x.Id))
                .Select(x => new Flight()
                {
                    Id = x.Id,
                    Airline = x.FluiAl23letterCode,
                    BookFlight = x.FluiFlightNo,
                    BookFlightDate = x.FluiScheduleDate,
                    BookFlightTime = x.FluiScheduleTime,
                    Ori = x.FluiLoadingLocation,
                    FluiType = x.FluiType,
                    FluiActualDate = x.FluiLandedDate ?? 0,
                    FluiActualTime = x.FluiLandedTime,
                    CargoTerminal = hawb != null ? hawb.LagiShedCode : "",
                    Des = hawb != null && !string.IsNullOrWhiteSpace(hawb.LagiAwbDest) ? hawb.LagiAwbDest : "HAN",
                }).PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);

            var items = results.Queryable.ToList().Select(x =>
            {
                x.GrossWeight = totalPiecesAndGwByFlight.Where(y => y.FlightId == x.Id).Select(y => y.TotalGw).FirstOrDefault();
                x.Pcs = Decimal.ToInt32(totalPiecesAndGwByFlight.Where(y => y.FlightId == x.Id).Select(y => y.TotalPieces).FirstOrDefault());
                return x;
            }).AsQueryable();
            results.Queryable = items;

            return results;
        }

        public async Task<PagedResult<HawbOla>> GetPagedListHawbForSelect2OlaAsync(string term, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var hawb = "";
            var mawbPrefix = "";
            var mawbNo = "";

            if (!string.IsNullOrWhiteSpace(term) && term.IndexOf('/') != -1)
                hawb = term.Substring(term.IndexOf('/') + 1);//Nếu có dấu / thì lấy phần sau đấu / là hawb

            if (!string.IsNullOrWhiteSpace(term) && term.IndexOf('-') != -1)
            {
                mawbPrefix = term.Substring(0, term.IndexOf('-'));//Nếu có dấu - thì lấy phần trước dấu - là mawb prefix
                if (term.IndexOf('/') != -1)
                    mawbNo = term.Substring(term.IndexOf('-') + 1, term.IndexOf('/') - term.IndexOf('-') - 1);//Nếu có dấu / thì lấy phần sau dấu - và trước dấu / là mawb no
                else
                    mawbNo = term.Substring(term.IndexOf('-') + 1);//Nếu không có dấu / thì lấy phần sau dấu - là mawb no
            }

            IQueryable<HawbOla> query = null;
            if (!string.IsNullOrWhiteSpace(term) && term.IndexOf('-') == -1 && term.IndexOf('/') == -1)//Nếu không có dấu - và dấu / thì search theo cả mawb và hawb
            {
                mawbPrefix = term;
                hawb = term;
                query = from lagi in (await GetDbContextAsync()).Lagis
                        .Where(x => x.LagiDateStatus2set == null || x.LagiDateStatus3set == null || x.LagiDateStatus4set == null)
                        .WhereIf(!string.IsNullOrWhiteSpace(mawbPrefix) && !string.IsNullOrWhiteSpace(mawbPrefix), x => x.LagiMawbPrefix.Contains(mawbPrefix) || x.LagiHawb.Contains(hawb))
                        .Where(x => x.LagiMawbNo != 11111111 && x.LagiConsigneeNumber != 781843)
                        select new HawbOla()
                        {
                            LagiMawbNo = lagi.LagiMawbNo,
                            LagiMawbPrefix = lagi.LagiMawbPrefix,
                            LagiHawb = lagi.LagiHawb,
                            LagiId = lagi.Id,
                        };
            }
            else
            {
                query = from lagi in (await GetDbContextAsync()).Lagis
                            .Where(x => x.LagiDateStatus2set == null || x.LagiDateStatus3set == null || x.LagiDateStatus4set == null)
                            .WhereIf(!string.IsNullOrWhiteSpace(mawbPrefix), x => x.LagiMawbPrefix.Contains(mawbPrefix))
                            .WhereIf(!string.IsNullOrWhiteSpace(mawbNo), x => x.LagiMawbNo.ToString().Contains(mawbNo))
                            .WhereIf(!string.IsNullOrWhiteSpace(hawb), x => x.LagiHawb.Contains(hawb))
                            .Where(x => x.LagiDeleted == false)
                            .Where(x => x.LagiMawbNo != 11111111 && x.LagiConsigneeNumber != 781843)
                        select new HawbOla()
                        {
                            LagiMawbNo = lagi.LagiMawbNo,
                            LagiMawbPrefix = lagi.LagiMawbPrefix,
                            LagiHawb = lagi.LagiHawb,
                            LagiId = lagi.Id,
                        };
            }
            return query.PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<HawbOla>> GetPagedListHawbOlaForDatatableAsync(string status, string vehicleRegNo, string[] mawbHawbs, string shipperName = null, string agentName = null, string airline = null, string flightNumber = null, string flightDateFrom = null, string flightDateTo = null, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            if (!string.IsNullOrWhiteSpace(agentName) && agentName.Split("-").Length == 2)
            {
                agentName = agentName.Split("-")[1].Trim();
            }

            if (!string.IsNullOrWhiteSpace(shipperName) && shipperName.Split("-").Length == 2)
            {
                shipperName = shipperName.Split("-")[1].Trim();
            }

            //Chia mảng filter thành 2 phần là search theo mawb và hawb
            var mawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') != -1 && x.IndexOf('/') == -1);
            var hawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('/') != -1 && x.IndexOf('/') != -1);
            var onlyHawbNumbers = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') == -1 && x.IndexOf('/') == -1);
            var unknows = mawbHawbs.Where(x => x.IndexOf('?') != -1);

            ////lấy lagi có cả mawb và hawb
            //var lagiMawbHawb = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo != 0)
            //                    select new
            //                    {
            //                        lagi.LagiMawbNo,
            //                        lagi.LagiMawbPrefix
            //                    }).Distinct();

            ////loại trừ những lagi có cả mawb và hawb
            //var lagiOnlyMawb = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo == 0)
            //                    .Where(x => !lagiMawbHawb.Any(y => x.LagiMawbNo == y.LagiMawbNo && x.LagiMawbPrefix == y.LagiMawbPrefix))
            //                   select new
            //                   {
            //                       lagi.Id
            //                   };

            //var lagiKundQuery = from lagi in (await GetDbSetAsync()).Where(x => x.LagiMasterIdentNo != 0 || lagiOnlyMawb.Any(y => x.Id == y.Id))
            //                    .Where(x => x.LagiDateStatus2set == null && x.LagiDateStatus4set == null)
            var lagiKundQuery = from lagi in (await GetDbSetAsync())//.Where(x => lagiOnlyMawb.Any(y => x.Id == y.Id))
                             .Where(x => x.LagiDateStatus2set == null)
                             .Where(x => x.LagiDeleted == false)
                                                            .Where(x =>
                                                              (mawbs.Count() == 0 && hawbs.Count() == 0 && onlyHawbNumbers.Count() == 0 && unknows.Count() == 0)
                                                              || (mawbs.Count() > 0 && mawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString()))
                                                              || (hawbs.Count() > 0 && hawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString() + '/' + x.LagiHawb))
                                                              || (onlyHawbNumbers.Count() > 0 && onlyHawbNumbers.Contains(x.LagiHawb))
                                                              || (unknows.Count() > 0 &&
                                                              (
                                                                unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains("?-?/" + x.LagiHawb)
                                                                || unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/?")
                                                              )
                                                              ))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(agentName), x => x.LagiNotifyName.ToUpper().Trim().Equals(agentName.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(airline), x => x.LagiLvgIn.ToUpper().Trim().Equals(airline.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightNumber), x => x.LagiFlightNoIn.Equals(flightNumber.Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                                select new HawbOla
                                {
                                    LagiId = lagi.Id,
                                    LagiMawbNo = lagi.LagiMawbNo,
                                    LagiMawbPrefix = lagi.LagiMawbPrefix,
                                    LagiQuantityExpected = lagi.LagiQuantityExpected,
                                    LagiWeightExpected = lagi.LagiWeightExpected,
                                    LagiHawb = lagi.LagiHawb,
                                    LagiLvgIn = lagi.LagiLvgIn,
                                    LagiFlightNoIn = lagi.LagiFlightNoIn,
                                    LagiFlightDateIn = lagi.LagiFlightDateIn,
                                    LagiFlightTimeIn = lagi.LagiFlightTimeIn,
                                    LagiQuantityReceived = lagi.LagiQuantityReceived,
                                    LagiWeightReceived = lagi.LagiWeightReceived,
                                    LagiChargableWeight = lagi.LagiChargableWeight,
                                    ConsigneeId = lagi.LagiConsigneeNumber,
                                    LagiShedCode = lagi.LagiShedCode
                                };

            var lagisKund = lagiKundQuery.ToList();
            var flights = _fluiRepository.Select(x => new { x.FluiLandedDate, x.FluiLandedTime, x.FluiAl23letterCode, x.FluiFlightNo, x.FluiScheduleDate, x.FluiScheduleTime }).ToList();

            foreach (var lagiKund in lagisKund)
            {
                var flui = flights.Where(x => x.FluiAl23letterCode == lagiKund.LagiLvgIn && x.FluiFlightNo == lagiKund.LagiFlightNoIn && x.FluiScheduleDate == lagiKund.LagiFlightDateIn && x.FluiScheduleTime == lagiKund.LagiFlightTimeIn).FirstOrDefault();
                lagiKund.FluiLandedDate = flui?.FluiLandedDate;
                lagiKund.FluiLandedTime = flui?.FluiLandedTime;
            }

            var lagiTruckPickupQuery = from lagi in lagiKundQuery
                                       join vhld in (await GetDbContextAsync()).OlaVhldVehicleDetails
                                       .Where(x => x.VhldImportExport == "IMPORT" && x.VhldRecordtype == "ECUS" && x.VhldDeleted == false)
                                       on lagi.LagiId equals vhld.VhldObjectisn
                                       into vhldGroups
                                       from vhldGroup in vhldGroups.DefaultIfEmpty()
                                       join vhcl in (await GetDbContextAsync()).OlaVehiclesRegistrations
                                       .Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "ECUS")
                                       .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), x => x.VehicRegNo == vehicleRegNo)
                                       on vhldGroup.VhldVehicleisn equals vhcl.Id into vhclGroups
                                       from vhclGroup in vhclGroups.DefaultIfEmpty()
                                       select new TruckHawbOla
                                       {
                                           LagiId = lagi.LagiId,
                                           VhclRegNo = vhclGroup.VehicRegNo,
                                           VhclLinkedIsn = vhclGroup.VhclLinkedIsn,
                                           VhclMessageIsn = vhclGroup.VhclMessageIsn,
                                           VhclRacNumber = vhclGroup.VhclRacNumber,
                                           PiecesOnTruck = vhldGroup.VhldLoadedPieces,
                                           WeightOnTruck = vhldGroup.VhldLoadedWeight,
                                           VhclRegId = vhclGroup.Id,
                                           VhldId = vhldGroup.Id,
                                           ConsigneeId = lagi.ConsigneeId
                                       };

            var fquery = from lagiKund in lagisKund
                         join lagiTruckPickup in lagiTruckPickupQuery on lagiKund.LagiId equals lagiTruckPickup.LagiId
                         //group lagiTruckPickup by lagiKund into lagiTruckPickupGroups
                         select new HawbOla
                         {
                             LagiId = lagiTruckPickup.LagiId,
                             LagiMawbNo = lagiKund.LagiMawbNo,
                             LagiMawbPrefix = lagiKund.LagiMawbPrefix,
                             LagiQuantityExpected = lagiKund.LagiQuantityExpected,
                             LagiWeightExpected = lagiKund.LagiWeightExpected,
                             LagiHawb = lagiKund.LagiHawb,
                             LagiLvgIn = lagiKund.LagiLvgIn,
                             LagiFlightNoIn = lagiKund.LagiFlightNoIn,
                             LagiFlightDateIn = lagiKund.LagiFlightDateIn,
                             LagiFlightTimeIn = lagiKund.LagiFlightTimeIn,
                             LagiShedCode = lagiKund.LagiShedCode,
                             FluiLandedDate = lagiKund.FluiLandedDate,
                             FluiLandedTime = lagiKund.FluiLandedTime,
                             VhclRegNo = lagiTruckPickup.VhclRegNo,
                             LagiQuantityReceived = lagiKund.LagiQuantityReceived,
                             LagiWeightReceived = lagiKund.LagiWeightReceived,
                             LagiChargableWeight = lagiKund.LagiChargableWeight,
                             VhclLinkedIsn = lagiTruckPickup.VhclLinkedIsn == null ? 0 : lagiTruckPickup.VhclLinkedIsn,
                             VhclMessageIsn = lagiTruckPickup.VhclMessageIsn == null ? 0 : lagiTruckPickup.VhclMessageIsn,
                             VhclRacNumber = lagiTruckPickup.VhclRacNumber,
                             PiecesOnTruck = lagiTruckPickup.PiecesOnTruck == null ? 0 : lagiTruckPickup.PiecesOnTruck,
                             WeightOnTruck = lagiTruckPickup.WeightOnTruck == null ? 0 : lagiTruckPickup.WeightOnTruck,
                             VehicleRegId = lagiTruckPickup.VhclRegId == null ? 0 : lagiTruckPickup.VhclRegId,
                             VehicleDetailId = lagiTruckPickup.VhldId == null ? 0 : lagiTruckPickup.VhldId,
                             ConsigneeId = lagiTruckPickup.ConsigneeId == null ? 0 : lagiTruckPickup.ConsigneeId
                         };
            fquery = fquery.WhereIf(!string.IsNullOrEmpty(vehicleRegNo), x => x.VhclRegNo != null && x.VhclRegNo.Contains(vehicleRegNo))
                           .WhereIf(status == "0", x => x.VhclRacNumber == null || x.VhclRacNumber.Trim() == "")
                           .WhereIf(status == "1", x => x.VhclRacNumber != null && x.VhclRacNumber.Trim() != "")
                           .Where(x => x.LagiMawbNo != 11111111 && x.ConsigneeId != 781843); ;
            //.Where(x=>x.PiecesOnTruck< x.LagiQuantityExpected);

            fquery = fquery.OrderByDescending(x => x.FluiLandedDate);
            return fquery.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<HawbAssignTruck>> GetListHawbAssignTruckAsync(List<string> listHawb = null, string flightNumber = null, string cargoTerminal = null, string prefix = null, long? serialNo = 0, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var hawbTruckAssignQuery = (from lagi in dbContext.Lagis.Where(x => x.LagiDeleted == false && !string.IsNullOrWhiteSpace(x.LagiHawb) && (x.LagiDateStatus2set == null
                                        && x.LagiConsigneeNumber != 781843))
                                       // "SAMSUNG DISPLAY VIETNAM"
                                       //.WhereIf(!string.IsNullOrWhiteSpace(hawb), x => x.LagiHawb.Contains(hawb))
                                       .WhereIf(listHawb != null && listHawb.Count > 1, x => listHawb.Contains(x.LagiHawb))
                                       .WhereIf(listHawb != null && listHawb.Count == 1, x => x.LagiHawb.ToUpper().Contains(listHawb[0].ToUpper()))
                                       .WhereIf(!string.IsNullOrWhiteSpace(prefix), x => x.LagiMawbPrefix.Contains(prefix))
                                       //.WhereIf(!string.IsNullOrWhiteSpace(flightNumber), x => flightNumber.Contains(x.LagiFlightNoIn) || flightNumber.Contains(x.LagiLvgIn))
                                       .WhereIf(cargoTerminal != "0", x => x.LagiShedCode == cargoTerminal)
                                       .WhereIf(serialNo != null, x => x.LagiMawbNo.ToString().Contains(serialNo.ToString()))
                                        join vhld in dbContext.OlaVhldVehicleDetails
                                        .Where(x => x.VhldImportExport == "IMPORT" && x.VhldRecordtype == "ECUS" && x.VhldDeleted == false)
                                        on lagi.Id equals vhld.VhldObjectisn into vhldGroups
                                        from vhldGroup in vhldGroups.DefaultIfEmpty()
                                        join vhcl in dbContext.OlaVehiclesRegistrations
                                        .Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "ECUS" && x.VhclMessageIsn == 0)
                                        on vhldGroup.VhldVehicleisn equals vhcl.Id into vhclGroups
                                        from vhclGroup in vhclGroups.DefaultIfEmpty()
                                            //where !(from lagiId in dbContext.OlaVhldVehicleDetails
                                            //        join vehicleReg in dbContext.OlaVehiclesRegistrations on lagiId.VhldVehicleisn equals vehicleReg.Id
                                            //        where lagiId.VhldDeleted == false && lagiId.VhldRecordtype == "ECUS"
                                            //        && lagiId.VhldImportExport == "IMPORT" && vehicleReg.VhclMessageIsn != 0
                                            //        select lagiId.VhldObjectisn)
                                            //.Contains(lagi.Id)
                                        select new HawbAssignTruck
                                        {
                                            LagiId = lagi.Id,
                                            LagiMawbPrefix = lagi.LagiMawbPrefix,
                                            LagiMawbNo = lagi.LagiMawbNo,
                                            LagiHawb = lagi.LagiHawb,
                                            LagiQuantityExpected = lagi.LagiQuantityExpected,
                                            LagiWeightExpected = lagi.LagiWeightExpected,
                                            LagiQuantityReceived = lagi.LagiQuantityReceived,
                                            LagiWeightReceived = lagi.LagiWeightReceived,
                                            //VhclRegNo = vhclGroup.VehicRegNo,
                                            VhclLinkedIsn = vhclGroup.VhclLinkedIsn,
                                            VhclRacNumber = vhclGroup.VhclRacNumber,
                                            LagiFlightNoIn = lagi.LagiFlightNoIn,
                                            LagiLvgIn = lagi.LagiLvgIn,
                                            CargoTerminal = lagi.LagiShedCode,
                                            PiecesAvailable = lagi.LagiQuantityExpected - (from detail in dbContext.OlaVhldVehicleDetails where detail.VhldRecordtype == "ECUS" && detail.VhldImportExport == "IMPORT" && detail.VhldObjectisn == lagi.Id && detail.VhldDeleted == false select detail.VhldLoadedPieces).Sum(),
                                            FlightNo = lagi.LagiLvgIn + lagi.LagiFlightNoIn
                                        }).Distinct();

            hawbTruckAssignQuery = hawbTruckAssignQuery.OrderByDescending(x => x.LagiId).Where(x => x.PiecesAvailable > 0)
            .WhereIf(!string.IsNullOrWhiteSpace(flightNumber), x => x.FlightNo.ToUpper().Contains(flightNumber.ToUpper()))
            .Where(x => x.LagiMawbNo != 11111111);
            return hawbTruckAssignQuery.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        private async Task<IQueryable<HawbGroupV1>> GetQueryListGroupHawbV1(
            long? lagiId = 0,
            string prefix = null,
            long? serialNo = 0,
            string hawb = null,
            string flight = null,
            long? agent = null,
            long? consigneeId = 0,
            string cargoTerminal = null,
            string awb = null,
            string flightDateFrom = null, string flightDateTo = null)
        {
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            var mawb = "";
            if (!string.IsNullOrWhiteSpace(awb) && awb.Length > 3 && int.TryParse(awb[3..], out int mawbNo))
            {
                mawb = awb[..3] + mawbNo.ToString();
            }

            return (from lagi in (await GetDbSetAsync()).Where(x => x.LagiDeleted == false)
                              .WhereIf(!string.IsNullOrWhiteSpace(awb) && string.IsNullOrWhiteSpace(mawb), e => e.LagiHawb.Contains(awb)
                                                                           || e.LagiMawbPrefix.Contains(awb)
                                                                           || e.LagiMawbNo.ToString().Contains(awb)
                                                                           || (e.LagiMawbPrefix + e.LagiMawbNo.ToString()).Contains(awb))
                              .WhereIf(!string.IsNullOrWhiteSpace(mawb), e => e.LagiHawb.Contains(awb)
                                                                           || e.LagiMawbNo.ToString().Contains(awb)
                                                                           || (e.LagiMawbPrefix + e.LagiMawbNo.ToString()).Contains(mawb))
                              .WhereIf(lagiId.HasValue, e => e.Id == lagiId)
                              .WhereIf(consigneeId > 0, e => e.LagiConsigneeNumber == consigneeId)
                              .WhereIf(serialNo.HasValue, e => e.LagiMawbNo == serialNo)
                              .WhereIf(!string.IsNullOrWhiteSpace(hawb), e => e.LagiHawb.Contains(hawb.Trim().ToUpper()))
                              .WhereIf(agent.HasValue, e => e.LagiNotifyNumber == agent)
                              .WhereIf(!string.IsNullOrWhiteSpace(prefix), e => e.LagiMawbPrefix.Contains(prefix.Trim()))
                              .WhereIf(!string.IsNullOrWhiteSpace(flight), e => (e.LagiLvgIn + e.LagiFlightNoIn).Contains(flight.ToUpper()))
                              .WhereIf(!string.IsNullOrWhiteSpace(cargoTerminal), e => e.LagiShedCode == cargoTerminal)
                              .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                              .OrderByDescending(x => x.LagiFlightDateIn).ThenByDescending(x => x.Id)
                    select new HawbGroupV1
                    {
                        LagiId = lagi.Id,
                        MawbPrefix = lagi.LagiMawbPrefix,
                        MawbSerial = lagi.LagiMawbNo,
                        Hawb = lagi.LagiHawb,
                        LagiShedCode = lagi.LagiShedCode,
                        LagiLvgIn = lagi.LagiLvgIn,
                        LagiFlightNoIn = lagi.LagiFlightNoIn,
                        Pieces = lagi.LagiQuantityReceived,
                        Weight = lagi.LagiWeightReceived,
                        CargoTerminal = lagi.LagiShedCode,
                        FlightDateIn = lagi.LagiFlightDateIn,
                        AgentName = lagi.LagiNotifyName,
                        ConsigneeName = lagi.LagiConsigneeName,
                    });
        }

        public async Task<List<HawbGroupV1>> GetListGroupHawbV1Async(
        long? lagiId = 0,
        string prefix = null,
        long? serialNo = 0,
        string hawb = null,
        string flight = null,
        long? agent = null,
        long? consigneeId = 0,
        string cargoTerminal = null,
        string awb = null,
        string flightDateFrom = null, string flightDateTo = null,
        int maxResultCount = int.MaxValue,
        int skipCount = 0,
        CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListGroupHawbV2(lagiId, prefix, serialNo, hawb, flight, agent, consigneeId, cargoTerminal, awb, flightDateFrom, flightDateTo);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountListGroupHawbV1Async(
        long? lagiId = 0,
        string prefix = null,
        long? serialNo = 0,
        string hawb = null,
        string flight = null,
        long? agent = null,
        long? consigneeId = 0,
        string cargoTerminal = null,
        string awb = null,
        string flightDateFrom = null, string flightDateTo = null,
        CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListGroupHawbV2(lagiId, prefix, serialNo, hawb, flight, agent, consigneeId, cargoTerminal, awb, flightDateFrom, flightDateTo);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> RemoveManyHawb(long vehicleRegId = 0, string listObjectIsn = null)
        {
            int result = 0;
            var dbContext = await GetDbContextAsync();
            if (vehicleRegId > 0)
            {
                string[] listObjectIsnArr = listObjectIsn.Split(',');
                var queryVehicleDetail = (from dt in (await GetDbContextAsync()).VhldVehicleDetails where dt.VhldVehicleisn == vehicleRegId && dt.VhldDeleted == false && listObjectIsnArr.Contains(dt.VhldObjectisn.ToString()) select dt).ToList();
                var queryHistory = (from dt in (await GetDbContextAsync()).VehicleExploitHistories where dt.VehVehicleIsn == vehicleRegId && dt.IsDeleted == false && listObjectIsnArr.Contains(dt.VehObjectIsn.ToString()) select dt).ToList();
                if (queryVehicleDetail != null && queryHistory != null)
                {
                    //dbContext.VhldVehicleDetails.RemoveRange(queryVehicleDetail);
                    queryVehicleDetail.ForEach(a => a.VhldDeleted = true);
                    //dbContext.VehicleExploitHistories.RemoveRange(queryHistory);
                    queryHistory.ForEach(a => a.IsDeleted = true);
                    dbContext.SaveChanges();
                    result = 1;
                }
            }

            return result;
        }

        public async Task<long> UpdateLocationManyHawb(long vehicleRegId = 0, string listObjectIsn = null, long locationId = 0)
        {
            int result = 0;
            var dbContext = await GetDbContextAsync();
            if (vehicleRegId > 0)
            {
                string[] listObjectID = listObjectIsn.Split(',');
                foreach (var item in listObjectID)
                {
                    var ObjectIsn = long.Parse(item);
                    var query = (from dt in (await GetDbContextAsync()).VhldVehicleDetails where dt.VhldVehicleisn == vehicleRegId && dt.VhldObjectisn == ObjectIsn && dt.VhldDeleted == false select dt).ToList();
                    query.ForEach(a => a.VhldPhysicalLocationIsn = locationId);
                    dbContext.SaveChanges();
                }
                result = 1;
            }
            return result;
        }

        public async Task<PagedResult<CheckLoadUnload>> GetListCheckLoadUnloadAsync(long? vehicleDetailId, string truckNumber, string cargoTerminal, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var listHawb = (from lagi in dbContext.Lagis.Where(x => x.LagiDeleted == false)
                                       .WhereIf(cargoTerminal != "0" && !string.IsNullOrEmpty(cargoTerminal), x => x.LagiShedCode == cargoTerminal)
                            join vhld in dbContext.VhldVehicleDetails.Where(x => x.VhldDeleted == false)
                            .Where(x => x.VhldImportExport == "IMPORT" && x.VhldRecordtype == "TRANSIT" && x.VhldDeleted == false && x.VhldUnloading == true && x.VhldLoadedPieces != x.VhldUnloadingPieces)
                            .WhereIf(vehicleDetailId.HasValue, x => x.Id == vehicleDetailId)
                            on lagi.Id equals vhld.VhldObjectisn
                            join vhcl in dbContext.VehiclesRegistrations
                            .Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "TRANSIT" && x.VhclLoadingLeftDate != null)
                            .WhereIf(!string.IsNullOrWhiteSpace(truckNumber), x => x.VehicRegNo == truckNumber)
                            on vhld.VhldVehicleisn equals vhcl.Id
                            select new CheckLoadUnload
                            {
                                LagiId = lagi.Id,
                                VehicleDetailId = vhld.Id,
                                VehicleRegId = vhcl.Id,
                                LagiMawbPrefix = lagi.LagiMawbPrefix,
                                LagiMawbNo = lagi.LagiMawbNo,
                                LagiHawb = lagi.LagiHawb,
                                PiecesLoaded = vhld.VhldLoadedPieces == null ? 0 : vhld.VhldLoadedPieces,
                                PiecesUnload = vhld.VhldUnloadingPieces == null ? 0 : vhld.VhldUnloadingPieces,
                                VhclRegNo = vhcl.VehicRegNo,
                                TransitDate = vhcl.VhclLoadingLeftDate,
                                CargoTerminal = lagi.LagiShedCode,
                                Irregular = false,
                            }).OrderByDescending(x => x.TransitDate);

            return listHawb.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<HawbClassUpdate>> GetPagedListHawbInClassUpdateAsync(string[] mawbHawbs, string agentName = null, string flightDateFrom = null, string flightDateTo = null,
            DateTime? uploadDateFrom = null, DateTime? uploadDateTo = null, bool? isDone = null, string classParam = null, List<string> listHawb = null,
            string weightLossWarning = null, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            if (!string.IsNullOrWhiteSpace(agentName) && agentName.Split("-").Length == 2)
            {
                agentName = agentName.Split("-")[1].Trim();
            }
            string[] classFake = { "FKE", "FAKE" };

            var lagiQuery = from lagi in (await GetDbSetAsync()).Where(x => x.LagiMasterIdentNo != 0 && x.LagiDeleted == false)
                                                           .WhereIf(listHawb != null, x => listHawb.Contains(x.LagiHawb))
                                                           .WhereIf(isDone.HasValue, x => x.LagiCalculation == isDone.Value)
                                                           .WhereIf(!string.IsNullOrWhiteSpace(agentName), x => x.LagiNotifyName.ToUpper().Trim().Equals(agentName.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                                                           .WhereIf("YES".Equals(weightLossWarning), x => x.LagiRecalcFreightChrgs == true)
                            join grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                .Where(x => x.GraiGroupType == "CLASS" && x.GraiGroupCode == "STORAGE" && !classFake.Contains(x.GraiValue) && !string.IsNullOrWhiteSpace(x.GraiValue))
                                .WhereIf(uploadDateFrom.HasValue && uploadDateTo.HasValue, x => x.GraiCreatedAt >= uploadDateFrom && x.GraiCreatedAt <= uploadDateTo)
                                .WhereIf(classParam != null, x => x.GraiValue == classParam)
                            on lagi.Id equals grai.GraiObjectIsn
                            select new HawbClassUpdate
                            {
                                LagiId = lagi.Id,
                                LagiMawbNo = lagi.LagiMawbNo,
                                LagiMawbPrefix = lagi.LagiMawbPrefix,
                                LagiHawb = lagi.LagiHawb,
                                LagiLvgIn = lagi.LagiLvgIn,
                                LagiFlightNoIn = lagi.LagiFlightNoIn,
                                LagiFlightDateIn = lagi.LagiFlightDateIn,
                                LagiFlightTimeIn = lagi.LagiFlightTimeIn,
                                ClassUpdate = grai.GraiValue,
                                GraiGroupId = grai.Id,
                                GraiGroupNo = grai.GraiObjectGroupIsn,
                                UploadDate = grai.GraiCreatedAt,
                                LagiHawbBill = lagi.LagiHawbBill,
                                Status = lagi.LagiCalculation,
                                WeightLossWarning = lagi.LagiRecalcFreightChrgs
                            };
            return lagiQuery.AsQueryable().Distinct().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<ListShipmentLocations>> GetListShipmentLocationsAsync(List<string> listHawb = null, int flightDateIntFrom = 0, int flightDateIntTo = 0,
            DateTime? flightDateFrom = null, DateTime? flightDateTo = null, string flightNo = null, string flightLvg = null, string groupNo = null, DateTime? arrivalALSBDate = null, DateTime? deliveryDate = null, bool? cdStatus = null, string deliveryGate = null, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var query2 = (
                    from r in (await GetDbContextAsync()).ImportEbookDownloads
                        .WhereIf(listHawb != null, x => listHawb.Contains(x.Hawb)) // list hawb
                        .WhereIf(!flightNo.IsNullOrWhiteSpace() && !flightLvg.IsNullOrWhiteSpace(), x => x.Flt.Equals(flightLvg + flightNo)) // chuyến bay
                        .WhereIf(flightDateFrom.HasValue && flightDateTo.HasValue, x => x.FlightdateAtaNoibai >= flightDateFrom.Value && x.FlightdateAtaNoibai <= flightDateTo.Value) // ngày bay
                        .WhereIf(arrivalALSBDate.HasValue, x => x.AlsbDate.HasValue
                             && x.AlsbDate.Value.Day == arrivalALSBDate.Value.Day
                             && x.AlsbDate.Value.Month == arrivalALSBDate.Value.Month
                             && x.AlsbDate.Value.Year == arrivalALSBDate.Value.Year) // ngày đến ALSB
                        .WhereIf(deliveryDate.HasValue, x => x.SevDeliverydate.HasValue
                             && x.SevDeliverydate.Value.Day == deliveryDate.Value.Day
                             && x.SevDeliverydate.Value.Month == deliveryDate.Value.Month
                             && x.SevDeliverydate.Value.Year == deliveryDate.Value.Year) // Ngày giao
                        .WhereIf(!deliveryGate.IsNullOrWhiteSpace(), x => x.SevGrade.Equals(deliveryGate)) // Gate
                        .WhereIf(cdStatus.HasValue && cdStatus == true, x => x.CdNsx != null) // đã thông quan
                        .WhereIf(cdStatus.HasValue && cdStatus == false, x => x.CdNsx == null) // chưa thông quan

                    join l in (await GetDbContextAsync()).Lagis
                        .Where(x => x.LagiDeleted == false)
                        on r.LagiId equals l.Id

                    select new ListShipmentLocations
                    {
                        LagiId = r.LagiId,
                        LagiMawb = r.Mawb,
                        LagiHawb = r.Hawb,
                        Forwarder = r.FwdCode,
                        LagiFlightNo = r.Flt,
                        LagiFlightDateIn = l.LagiFlightDateIn,
                        LagiFlightTimeIn = l.LagiFlightTimeIn,
                        FlightDate = r.FlightdateEtaNoibai,
                        FlightTime = r.FlighttimeEtaNoibai,
                        Remark = r.Remark,
                        CDNumber = r.CdNsx,
                        Group = r.HawbLocationGroupNo,
                        Kund3letterCode = r.FwdCode,
                        VehicleRegNo = r.AlsbTruck,
                        Pcs = (int?)r.HawbPcs,
                        Gw = r.HawbGw,
                        ArriveWarehouseDate = r.AlsbDate,
                        ArriveWarehouseTime = r.AlsbTime,
                        Location = r.HawbLocation,
                        TruckDeliveryNo = r.SevDeliveryTruckno,
                        DeliveryDate = r.SevDeliverydate,
                        DeliveryTime = r.SevDeliverytime,
                        Grade = r.SevGrade,
                        Warehouse = r.SevWarehouse,
                        PIC = r.SdbnReceiver,
                        FinishDate = r.SevArrivaldate,
                        FinishTime = r.SevArrivaltime
                    }
                );

            return query2
                    .OrderByDescending(x => x.FlightDate.HasValue)
                    .ThenByDescending(x => x.FlightDate)
                    .ThenByDescending(x => x.LagiMawb != null)
                    .ThenByDescending(x => x.LagiMawb)
                    .ThenByDescending(x => x.LagiHawb != null)
                    .ThenByDescending(x => x.LagiHawb)
                    .AsQueryable()
                    .PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<PagedResult<HawbMonitoring>> GetHawbMonitoringAsync(string statusCD, string vehicleRegNo, string[] mawbHawbs, string cargoTerminal = null, string status4 = null, string grade = null, string classParam = null, string agentName = null, string airline = null, string flightNumber = null, string flightDateFrom = null, string flightDateTo = null, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            string[] classFake = { "FKE", "FAKE" };
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            if (!string.IsNullOrWhiteSpace(agentName) && agentName.Split("-").Length == 2)
            {
                agentName = agentName.Split("-")[1].Trim();
            }

            //Chia mảng filter thành 2 phần là search theo mawb và hawb
            var mawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') != -1 && x.IndexOf('/') == -1);
            var hawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('/') != -1 && x.IndexOf('/') != -1);
            var onlyHawbNumbers = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') == -1 && x.IndexOf('/') == -1);
            var unknows = mawbHawbs.Where(x => x.IndexOf('?') != -1);

            //lấy lagi có cả mawb và hawb
            var lagiMawbHawb = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo != 0)
                                select new
                                {
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix
                                }).Distinct();

            //loại trừ những lagi có cả mawb và hawb
            var lagiOnlyMawb = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo == 0)
                                .Where(x => !lagiMawbHawb.Any(y => x.LagiMawbNo == y.LagiMawbNo && x.LagiMawbPrefix == y.LagiMawbPrefix))
                               select new
                               {
                                   lagi.Id
                               };

            var lagiKundQuery = from lagi in (await GetDbSetAsync()).Where(x => x.LagiMasterIdentNo != 0 || lagiOnlyMawb.Any(y => x.Id == y.Id))
                                .Where(x => x.LagiDeleted == false)
                                                            .Where(x =>
                                                              (mawbs.Count() == 0 && hawbs.Count() == 0 && onlyHawbNumbers.Count() == 0 && unknows.Count() == 0)
                                                              || (mawbs.Count() > 0 && mawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString()))
                                                              || (hawbs.Count() > 0 && hawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString() + '/' + x.LagiHawb))
                                                              || (onlyHawbNumbers.Count() > 0 && onlyHawbNumbers.Contains(x.LagiHawb))
                                                              || (unknows.Count() > 0 &&
                                                              (
                                                                unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains("?-?/" + x.LagiHawb)
                                                                || unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/?")
                                                              )
                                                              ))
                                                           .WhereIf(status4 == "1", x => x.LagiDateStatus4set != null && x.LagiTimeStatus4set != null)
                                                           .WhereIf(status4 == "0", x => x.LagiDateStatus4set == null && x.LagiTimeStatus4set == null)
                                                           .WhereIf(statusCD == "1", x => x.LagiDateStatus3set != null && x.LagiCustomRefNo != null)
                                                           .WhereIf(statusCD == "0", x => x.LagiDateStatus3set == null && x.LagiCustomRefNo == null)
                                                           .WhereIf(!string.IsNullOrWhiteSpace(agentName), x => x.LagiNotifyName.ToUpper().Trim().Equals(agentName.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(airline), x => x.LagiLvgIn.ToUpper().Trim().Equals(airline.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(cargoTerminal), x => x.LagiShedCode.ToUpper().Trim().Equals(cargoTerminal.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightNumber), x => x.LagiFlightNoIn.Equals(flightNumber.Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                                select new
                                {
                                    lagi.Id,
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix,
                                    lagi.LagiQuantityExpected,
                                    lagi.LagiWeightExpected,
                                    lagi.LagiHawb,
                                    lagi.LagiLvgIn,
                                    lagi.LagiFlightNoIn,
                                    lagi.LagiFlightDateIn,
                                    lagi.LagiFlightTimeIn,
                                    lagi.LagiShipmentRemarks,
                                    lagi.LagiCustomRefNo,
                                    lagi.LagiDateStatus3set,
                                    lagi.LagiTimeStatus3set,
                                    lagi.LagiMasterIdentNo,
                                    lagi.LagiDateStatus4set
                                };

            var lagiTruckPickupQuery = (from lagi in lagiKundQuery
                                        join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn into vhldGroups
                                        from vhldGroup in vhldGroups.DefaultIfEmpty()
                                        join vhcl in (await GetDbContextAsync()).VehiclesRegistrations.Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "TRANSIT" && x.VhclUnloadingArrivalDate != null) on vhldGroup.VhldVehicleisn equals vhcl.Id into vhclGroups
                                        from vhclGroup in vhclGroups.DefaultIfEmpty()
                                        join vhclDLV in (await GetDbContextAsync()).VehiclesRegistrations
                                        .Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "DELIVERY")
                                        on vhldGroup.VhldVehicleisn equals vhclDLV.Id into vhclGroupsDLV
                                        from vhclGroupDLV in vhclGroupsDLV.DefaultIfEmpty()
                                        join location in (await GetDbContextAsync()).LocsLocations.Where(x => !x.LocsDeleted.Value) on lagi.Id equals location.LocsObjectIsn into locationGroups
                                        from locationGroup in locationGroups.DefaultIfEmpty()
                                        join locationName in (await GetDbContextAsync()).SslpPhysicalLocations on locationGroup.LocsPhysicalIsn equals locationName.Id into locationNameGroups
                                        from locationNameGroup in locationNameGroups.DefaultIfEmpty()
                                        join grai in (await GetDbContextAsync()).GraiGroupAdditionalInfos
                                        .Where(x => x.GraiGroupType == "CLASS" && x.GraiGroupCode == "STORAGE" && !classFake.Contains(x.GraiValue) && !string.IsNullOrWhiteSpace(x.GraiValue))
                                        on lagi.Id equals grai.GraiObjectIsn into Groups
                                        from graiClass in Groups.DefaultIfEmpty()
                                        join irr in (await GetDbContextAsync()).HawbIrrs on lagi.Id equals irr.HawbId into irrGroups
                                        from irrGroup in irrGroups.DefaultIfEmpty()
                                        orderby vhclGroup.VhclUnloadingArrivalDate descending
                                        select new
                                        {
                                            lagi.Id,
                                            vhclGroup.VehicRegNo,
                                            vhclGroup.VhclUnloadingArrivalDate,
                                            vhclGroup.VhclUnloadingArrivalTime,
                                            vhclGroupDLV.VhclLoadingArrivalDate,
                                            vhclGroupDLV.VhclLoadingArrivalTime,
                                            vhclGroupDLV.VhclUnloadingDoor,
                                            locationNameGroup.SslpRackRow,
                                            graiClass.GraiValue,
                                            irrGroup.Place,
                                            VehicRegNoDLV = vhclGroupDLV.VehicRegNo
                                        }).Distinct();

            var query = (from lagiKund in lagiKundQuery.ToList()
                         join lagiTruckPickup in lagiTruckPickupQuery on lagiKund.Id equals lagiTruckPickup.Id
                         group lagiTruckPickup by lagiKund into lagiTruckPickupGroups
                         select new HawbMonitoring
                         {
                             LagiId = lagiTruckPickupGroups.Key.Id,
                             LagiMawbNo = lagiTruckPickupGroups.Key.LagiMawbNo,
                             LagiMawbPrefix = lagiTruckPickupGroups.Key.LagiMawbPrefix,
                             LagiQuantityExpected = lagiTruckPickupGroups.Key.LagiQuantityExpected,
                             LagiWeightExpected = lagiTruckPickupGroups.Key.LagiWeightExpected,
                             LagiHawb = lagiTruckPickupGroups.Key.LagiHawb,
                             LagiLvgIn = lagiTruckPickupGroups.Key.LagiLvgIn,
                             LagiFlightNoIn = lagiTruckPickupGroups.Key.LagiFlightNoIn,
                             LagiFlightDateIn = lagiTruckPickupGroups.Key.LagiFlightDateIn,
                             LagiFlightTimeIn = lagiTruckPickupGroups.Key.LagiFlightTimeIn,
                             VehicleRegNo = string.Join(',', lagiTruckPickupGroups.Where(x => x.VehicRegNo != null).Select(x => x.VehicRegNo).Distinct()),
                             TruckDeliveryNo = string.Join(',', lagiTruckPickupGroups.Where(x => x.VehicRegNoDLV != null).Select(x => x.VehicRegNoDLV).Distinct()),
                             ArriveWarehouseDate = lagiTruckPickupGroups.Select(x => x.VhclUnloadingArrivalDate).FirstOrDefault(),
                             ArriveWarehouseTime = lagiTruckPickupGroups.Select(x => x.VhclUnloadingArrivalTime).FirstOrDefault(),
                             GoodsClass = string.Join(',', lagiTruckPickupGroups.Select(x => x.GraiValue).Distinct()),
                             Location = string.Join(',', lagiTruckPickupGroups.Where(x => x.SslpRackRow != null).Select(x => x.SslpRackRow).Distinct()),
                             Remark = lagiTruckPickupGroups.Key.LagiShipmentRemarks,
                             Irr = string.Join(',', lagiTruckPickupGroups.Where(x => !string.IsNullOrWhiteSpace(x.Place)).Select(x => x.Place).Distinct()),
                             CDNumber = lagiTruckPickupGroups.Key.LagiCustomRefNo,
                             LagiDateStatus3Set = lagiTruckPickupGroups.Key.LagiDateStatus3set,
                             LagiTimeStatus3Set = lagiTruckPickupGroups.Key.LagiTimeStatus3set,
                             LagiMasterIndentNo = lagiTruckPickupGroups.Key.LagiMasterIdentNo,
                             Grade = string.Join(',', lagiTruckPickupGroups.Where(x => x.VhclUnloadingDoor != null).Select(x => x.VhclUnloadingDoor).Distinct()),
                             DeliveryDate = lagiTruckPickupGroups.Where(x => x.VhclLoadingArrivalDate != null).Select(x => x.VhclLoadingArrivalDate).FirstOrDefault(),
                             DeliveryTime = lagiTruckPickupGroups.Where(x => x.VhclLoadingArrivalTime != null).Select(x => x.VhclLoadingArrivalTime).FirstOrDefault(),
                         })
                        .WhereIf(!string.IsNullOrWhiteSpace(grade), x => x.Grade == grade)
                        .WhereIf(!string.IsNullOrWhiteSpace(classParam), x => x.GoodsClass.Contains(classParam.ToUpper()));
            return query.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<List<TruckOla>> GetListTruckOlaAsync(
            string truckNumber = null,
            long? ecusId = 0,
            string olaNumber = null,
            string dateFrom = null, string dateTo = null,
            string status = null,
            int dateType = 0,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListTruckOla(truckNumber, ecusId, olaNumber, dateFrom, dateTo, status, dateType);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        private async Task<IQueryable<TruckOla>> GetQueryListTruckOla(
            string truckNumber = null,
            long? ecusId = 0,
            string olaNumber = null,
            string dateFrom = null,
            string dateTo = null,
            string status = null,
            int dateType = 0)
        {
            DateTime startDate = DateTime.Now.Date.AddDays(-1000);
            DateTime endDate = DateTime.Now.Date.AddDays(1).AddTicks(-1);
            if (!string.IsNullOrWhiteSpace(dateFrom))
            {
                if (DateTime.TryParseExact(dateFrom, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startDate = date.Date;
                }
            }
            if (!string.IsNullOrWhiteSpace(dateTo))
            {
                if (DateTime.TryParseExact(dateTo, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    endDate = date.Date.AddDays(1).AddTicks(-1);
                }
            }
            return (from vehicleReg in (await GetDbContextAsync()).OlaVehiclesRegistrations
                               .Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "ECUS")
                               .WhereIf(!string.IsNullOrWhiteSpace(truckNumber), x => x.VehicRegNo == truckNumber)
                               .WhereIf(ecusId.HasValue, e => e.VhclMessageIsn == ecusId)
                               .WhereIf(!string.IsNullOrWhiteSpace(olaNumber), e => e.VhclRacNumber.Contains(olaNumber))
                               //.Where(x => x.VhclLoadingArrivalDate >= startDate && x.VhclLoadingArrivalDate <= endDate)
                               .WhereIf(status == "0", x => x.VhclRacNumber == null || x.VhclRacNumber.Trim() == "")
                               .WhereIf(status == "1", x => x.VhclRacNumber != null && x.VhclRacNumber.Trim() != "")
                               //.WhereIf(!string.IsNullOrWhiteSpace(dateFrom) && !string.IsNullOrWhiteSpace(dateTo), x => x.VhclLoadingArrivalDate >= startDate && x.VhclLoadingArrivalDate <= endDate)
                               .WhereIf(dateType == 0 && !string.IsNullOrWhiteSpace(dateFrom) && !string.IsNullOrWhiteSpace(dateTo), x => x.VhclLoadingLeftDate >= startDate && x.VhclLoadingLeftDate <= endDate)
                               .WhereIf(dateType == 1 && !string.IsNullOrWhiteSpace(dateFrom) && !string.IsNullOrWhiteSpace(dateTo), x => x.VhclLoadingArrivalDate >= startDate && x.VhclLoadingArrivalDate <= endDate)

                    select new TruckOla
                    {
                        VehicleRegId = vehicleReg.Id,
                        VhclRegNo = vehicleReg.VehicRegNo,
                        VhclLinkedIsn = vehicleReg.VhclLinkedIsn,
                        VhclMessageIsn = vehicleReg.VhclMessageIsn,
                        VhclRacNumber = vehicleReg.VhclRacNumber,
                        VhclMasterIsn = vehicleReg.VhclMasterIsn,
                        OlaDate = vehicleReg.VhclLoadingArrivalDate,
                        TruckCreateDate = vehicleReg.VhclLoadingLeftDate,
                        Remark = vehicleReg.HysRemark,
                        HysStatus = vehicleReg.HysStatus ?? 0,
                        CargoTerminal = vehicleReg.VhclLoadingWarehouse,
                        HysUrl = vehicleReg.HysFileUrl,
                        NctsVcdlFileUrl = vehicleReg.NctsVcdlFileUrl,
                        HysNo = vehicleReg.HysNo
                    }).OrderBy(x => x.OlaDate);
        }

        public async Task<long> GetCountListTruckOlaAsync(
        string truckNumber = null,
        long? ecusId = 0,
        string olaNumber = null,
        string dateFrom = null,
        string dateTo = null,
        string status = null,
        int dateType = 0,
        CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListTruckOla(truckNumber, ecusId, olaNumber, dateFrom, dateTo, status);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        private async Task<IQueryable<HawbOla>> GetQueryListHawbInTruckOla(long? vehicleRegId = 0)
        {
            var query = (from vehicleReg in (await GetDbContextAsync()).OlaVehiclesRegistrations.Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "ECUS" && x.Id == vehicleRegId)
                         join vhld in (await GetDbContextAsync()).OlaVhldVehicleDetails.Where(x => x.VhldDeleted == false) on vehicleReg.Id equals vhld.VhldVehicleisn
                         join lagi in (await GetDbContextAsync()).Lagis on vhld.VhldObjectisn equals lagi.Id
                         select new HawbOla
                         {
                             LagiId = lagi.Id,
                             VehicleDetailId = vhld.Id,
                             VehicleRegId = vehicleReg.Id,
                             VhclRegNo = vehicleReg.VehicRegNo,
                             LagiHawb = lagi.LagiHawb,
                             VhclMessageIsn = vehicleReg.VhclMessageIsn,
                             VhclRacNumber = vehicleReg.VhclRacNumber,
                             LagiMawbNo = lagi.LagiMawbNo,
                             LagiMawbPrefix = lagi.LagiMawbPrefix,
                             LagiQuantityExpected = lagi.LagiQuantityExpected,
                             LagiWeightExpected = lagi.LagiWeightExpected,
                             LagiLvgIn = lagi.LagiLvgIn,
                             LagiFlightNoIn = lagi.LagiFlightNoIn,
                             LagiFlightDateIn = lagi.LagiFlightDateIn,
                             LagiFlightTimeIn = lagi.LagiFlightTimeIn,
                             PiecesOnTruck = vhld.VhldLoadedPieces,
                             WeightOnTruck = vhld.VhldLoadedWeight
                         });

            return query;
        }

        public async Task<List<HawbOla>> GetListHawbOnTruckOlaAsync(long? vehicleRegId = 0, int maxResultCount = int.MaxValue,
        int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListHawbInTruckOla(vehicleRegId);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountListHawbOnTruckOlaAsync(
        long? vehicleRegId = 0,
        CancellationToken cancellationToken = default)
        {
            var query = await GetQueryListHawbInTruckOla(vehicleRegId);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<PagedResult<OperationCenterData>> GetOperationCenterAsync(bool complete, string statusCD, string[] mawbHawbs, string cargoTerminal = null, string agentName = null, string awbStatus = null, string flightDateFrom = null, string flightDateTo = null, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            //Chia mảng filter thành 2 phần là search theo mawb và hawb
            var mawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') != -1 && x.IndexOf('/') == -1);
            var hawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('/') != -1 && x.IndexOf('/') != -1);
            var onlyHawbNumbers = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') == -1 && x.IndexOf('/') == -1);
            var unknows = mawbHawbs.Where(x => x.IndexOf('?') != -1);

            //lấy lagi có cả mawb và hawb
            var lagiMawbHawb = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo != 0)
                                select new
                                {
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix
                                }).Distinct();

            //loại trừ những lagi có cả mawb và hawb
            var lagiOnlyMawb = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo == 0)
                                .Where(x => !lagiMawbHawb.Any(y => x.LagiMawbNo == y.LagiMawbNo && x.LagiMawbPrefix == y.LagiMawbPrefix))
                               select new
                               {
                                   lagi.Id
                               };
            DateTime FromDate = DateTime.Now.AddDays(-7).Date;
            var lagiKundQuery = from lagi in (await GetDbSetAsync()).Where(x => x.LagiMasterIdentNo != 0 || lagiOnlyMawb.Any(y => x.Id == y.Id))
                                .Where(x => x.LagiDeleted == false)
                                .WhereIf(complete, x => x.LagiDateStatus4set != null && x.LagiDateStatus4set.Value.Date > FromDate)
                                .WhereIf(complete == false, x => x.LagiDateStatus4set == null)
                                                            .Where(x =>
                                                              (mawbs.Count() == 0 && hawbs.Count() == 0 && onlyHawbNumbers.Count() == 0 && unknows.Count() == 0)
                                                              || (mawbs.Count() > 0 && mawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString()))
                                                              || (hawbs.Count() > 0 && hawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString() + '/' + x.LagiHawb))
                                                              || (onlyHawbNumbers.Count() > 0 && onlyHawbNumbers.Contains(x.LagiHawb))
                                                              || (unknows.Count() > 0 &&
                                                              (
                                                                unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains("?-?/" + x.LagiHawb)
                                                                || unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/?")
                                                              )
                                                              ))
                                                           .WhereIf(statusCD == "1", x => x.LagiDateStatus3set != null && x.LagiCustomRefNo != null)
                                                           .WhereIf(statusCD == "0", x => x.LagiDateStatus3set == null && x.LagiCustomRefNo == null)
                                                           .WhereIf(!string.IsNullOrWhiteSpace(agentName), x => agentName.ToUpper().Trim().Contains(x.LagiNotifyName.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(cargoTerminal), x => x.LagiShedCode.ToUpper().Trim().Equals(cargoTerminal.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                                select new
                                {
                                    lagi.Id,
                                    lagi.LagiMasterIdentNo,
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix,
                                    lagi.LagiQuantityExpected,
                                    lagi.LagiWeightExpected,
                                    lagi.LagiHawb,
                                    lagi.LagiLvgIn,
                                    lagi.LagiFlightNoIn,
                                    lagi.LagiFlightDateIn,
                                    lagi.LagiFlightTimeIn,
                                    lagi.LagiShipmentRemarks,
                                    lagi.LagiDateStatus4set,
                                    lagi.LagiNotifyName,
                                    lagi.LagiShedCode,
                                };

            var lagiTruckPickupQuery = (from lagi in lagiKundQuery
                                        join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn into vhldGroups
                                        from vhldGroup in vhldGroups.DefaultIfEmpty()
                                        join vhclDLV in (await GetDbContextAsync()).VehiclesRegistrations
                                        .Where(x => x.VhclImportExport == "DELIVERY_PLAN")
                                        on vhldGroup.VhldVehicleisn equals vhclDLV.Id into vhclGroupsDLV
                                        from vhclGroupDLV in vhclGroupsDLV.DefaultIfEmpty()
                                        join irr in (await GetDbContextAsync()).HawbIrrs on lagi.Id equals irr.HawbId into irrGroups
                                        from irrGroup in irrGroups.DefaultIfEmpty()
                                        select new
                                        {
                                            lagi.Id,
                                            lagi.LagiNotifyName,
                                            vhclGroupDLV.VhclLoadingArrivalDate,
                                            vhclGroupDLV.VhclLoadingArrivalTime,
                                            vhclGroupDLV.VhclUnloadingDoor,
                                            irrGroup.Place,
                                            VehicRegNoDLV = vhclGroupDLV.VehicRegNo,
                                            vhclGroupDLV.VhclUnloadingArrivalDate,
                                            vhclGroupDLV.VhclUnloadingArrivalTime,
                                        }).Distinct();

            var query = (from lagiKund in lagiKundQuery.ToList()
                         join lagiTruckPickup in lagiTruckPickupQuery on lagiKund.Id equals lagiTruckPickup.Id
                         group lagiTruckPickup by lagiKund into lagiTruckPickupGroups
                         select new OperationCenterData
                         {
                             LagiId = lagiTruckPickupGroups.Key.Id,
                             LagiMasterIndentNo = lagiTruckPickupGroups.Key.LagiMasterIdentNo,
                             LagiMawbNo = lagiTruckPickupGroups.Key.LagiMawbNo,
                             LagiMawbPrefix = lagiTruckPickupGroups.Key.LagiMawbPrefix,
                             LagiQuantityExpected = lagiTruckPickupGroups.Key.LagiQuantityExpected,
                             LagiWeightExpected = lagiTruckPickupGroups.Key.LagiWeightExpected,
                             LagiHawb = lagiTruckPickupGroups.Key.LagiHawb,
                             LagiLvgIn = lagiTruckPickupGroups.Key.LagiLvgIn,
                             LagiFlightNoIn = lagiTruckPickupGroups.Key.LagiFlightNoIn,
                             LagiFlightDateIn = lagiTruckPickupGroups.Key.LagiFlightDateIn,
                             LagiFlightTimeIn = lagiTruckPickupGroups.Key.LagiFlightTimeIn,
                             TruckDeliveryNo = string.Join(',', lagiTruckPickupGroups.Where(x => x.VehicRegNoDLV != null).Select(x => x.VehicRegNoDLV).Distinct()),
                             Remark = lagiTruckPickupGroups.Key.LagiShipmentRemarks,
                             Irr = string.Join(',', lagiTruckPickupGroups.Where(x => !string.IsNullOrWhiteSpace(x.Place)).Select(x => x.Place).Distinct()),
                             DeliveryDate = lagiTruckPickupGroups.Where(x => x.VhclLoadingArrivalDate != null).Select(x => x.VhclLoadingArrivalDate).FirstOrDefault(),
                             DeliveryTime = lagiTruckPickupGroups.Where(x => x.VhclLoadingArrivalTime != null).Select(x => x.VhclLoadingArrivalTime).FirstOrDefault(),
                             Agent = lagiTruckPickupGroups.Key.LagiNotifyName,
                             CargoterminalId = lagiTruckPickupGroups.Key.LagiShedCode,
                             VhclUnloadingArrivalDate = lagiTruckPickupGroups.Where(x => x.VhclUnloadingArrivalDate != null).Select(x => x.VhclUnloadingArrivalDate).FirstOrDefault(),
                             VhclUnloadingArrivalTime = lagiTruckPickupGroups.Where(x => x.VhclUnloadingArrivalTime != null).Select(x => x.VhclUnloadingArrivalTime).FirstOrDefault(),
                         });
            return query.AsQueryable().PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<List<OperationCenterUncompleteData>> GetOperationCenterUncompleteAsync(string status, string statusCD, string[] mawbHawbs, string cargoTerminal = null, string agentName = null, string flightDateFrom = null, string flightDateTo = null)
        {
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            //Chia mảng filter thành 2 phần là search theo mawb và hawb
            var mawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') != -1 && x.IndexOf('/') == -1);
            var hawbs = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('/') != -1 && x.IndexOf('/') != -1);
            var onlyHawbNumbers = mawbHawbs.Where(x => x.IndexOf('?') == -1 && x.IndexOf('-') == -1 && x.IndexOf('/') == -1);
            var unknows = mawbHawbs.Where(x => x.IndexOf('?') != -1);

            //lấy lagi có cả mawb và hawb
            var lagiMawbHawb = (from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo != 0)
                                select new
                                {
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix
                                }).Distinct();

            //loại trừ những lagi có cả mawb và hawb
            var lagiOnlyMawb = from lagi in (await GetDbContextAsync()).Lagis.Where(x => x.LagiMasterIdentNo == 0)
                                .Where(x => !lagiMawbHawb.Any(y => x.LagiMawbNo == y.LagiMawbNo && x.LagiMawbPrefix == y.LagiMawbPrefix))
                               select new
                               {
                                   lagi.Id
                               };
            DateTime FromDate = DateTime.Now.AddDays(-7).Date;
            var lagiKundQuery = from lagi in (await GetDbSetAsync()).Where(x => x.LagiMasterIdentNo != 0 || lagiOnlyMawb.Any(y => x.Id == y.Id))
                                .Where(x => x.LagiDeleted == false && x.LagiDateStatus4set == null)
                                .WhereIf(status == "ARRIVAL" || status == "DELIVERYPLAN", x => x.LagiDateStatus0set == null)
                                .WhereIf(status == "LOADINGONTRUCK", x => x.LagiDateStatus0set != null && x.LagiDateStatus1set == null)
                                .WhereIf(status == "DELIVERING", x => x.LagiDateStatus1set != null)
                                                            .Where(x =>
                                                              (mawbs.Count() == 0 && hawbs.Count() == 0 && onlyHawbNumbers.Count() == 0 && unknows.Count() == 0)
                                                              || (mawbs.Count() > 0 && mawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString()))
                                                              || (hawbs.Count() > 0 && hawbs.Contains(x.LagiMawbPrefix + '-' + x.LagiMawbNo.ToString() + '/' + x.LagiHawb))
                                                              || (onlyHawbNumbers.Count() > 0 && onlyHawbNumbers.Contains(x.LagiHawb))
                                                              || (unknows.Count() > 0 &&
                                                              (
                                                                unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/" + x.LagiHawb)
                                                                || unknows.Contains(x.LagiMawbPrefix + "-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains("?-?/" + x.LagiHawb)
                                                                || unknows.Contains("?-" + x.LagiMawbNo.ToString() + "/?")
                                                                || unknows.Contains(x.LagiMawbPrefix + "-?/?")
                                                              )
                                                              ))
                                                           .WhereIf(statusCD == "1", x => x.LagiDateStatus3set != null && x.LagiCustomRefNo != null)
                                                           .WhereIf(statusCD == "0", x => x.LagiDateStatus3set == null && x.LagiCustomRefNo == null)
                                                           .WhereIf(!string.IsNullOrWhiteSpace(agentName), x => agentName.ToUpper().Trim().Contains(x.LagiNotifyName.ToUpper().Trim()) && !string.IsNullOrEmpty(x.LagiNotifyName.Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(cargoTerminal), x => x.LagiShedCode.ToUpper().Trim().Equals(cargoTerminal.ToUpper().Trim()))
                                                           .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                                select new
                                {
                                    lagi.Id,
                                    lagi.LagiMasterIdentNo,
                                    lagi.LagiMawbNo,
                                    lagi.LagiMawbPrefix,
                                    lagi.LagiQuantityExpected,
                                    lagi.LagiWeightExpected,
                                    lagi.LagiHawb,
                                    lagi.LagiLvgIn,
                                    lagi.LagiFlightNoIn,
                                    lagi.LagiFlightDateIn,
                                    lagi.LagiFlightTimeIn,
                                    lagi.LagiShipmentRemarks,
                                    lagi.LagiDateStatus4set,
                                    lagi.LagiNotifyName,
                                    lagi.LagiShedCode,
                                    lagi.LagiDateStatus3set
                                };

            var lagiTruckPickupQuery = (from lagi in lagiKundQuery
                                        join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn into vhldGroups
                                        from vhldGroup in vhldGroups.DefaultIfEmpty()
                                        join vhclDLV in (await GetDbContextAsync()).VehiclesRegistrations
                                        .Where(x => x.VhclImportExport == "DELIVERY_PLAN")
                                        on vhldGroup.VhldVehicleisn equals vhclDLV.Id into vhclGroupsDLV
                                        from vhclGroupDLV in vhclGroupsDLV.DefaultIfEmpty()
                                        join vhclDLV1 in (await GetDbContextAsync()).VehiclesRegistrations
                                       .Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "TRANSIT" && x.VhclLoadingLeftDate != null)
                                        on vhldGroup.VhldVehicleisn equals vhclDLV1.Id into vhclGroupsDLV1
                                        from vhclGroupDLV1 in vhclGroupsDLV1.DefaultIfEmpty()

                                        join irr in (await GetDbContextAsync()).HawbIrrs on lagi.Id equals irr.HawbId into irrGroups
                                        from irrGroup in irrGroups.DefaultIfEmpty()
                                        select new
                                        {
                                            lagi.Id,
                                            lagi.LagiNotifyName,
                                            vhclGroupDLV.VhclUnloadingEtaDate,
                                            vhclGroupDLV.VhclUnloadingEtaTime,
                                            vhclGroupDLV.VhclUnloadingDoor,
                                            irrGroup.Place,
                                            VehicRegNo = vhclGroupDLV1.VehicRegNo,
                                            vhclGroupDLV.VhclUnloadingArrivalDate,
                                            vhclGroupDLV.VhclUnloadingArrivalTime,
                                            VhclLoadingArrivalDateTS = vhclGroupDLV1.VhclLoadingArrivalDate,
                                            VhclLoadingArrivalTimeTS = vhclGroupDLV1.VhclLoadingArrivalTime
                                        }).Distinct();

            var query = (from lagiKund in lagiKundQuery.ToList()
                         join lagiTruckPickup in lagiTruckPickupQuery on lagiKund.Id equals lagiTruckPickup.Id
                         group lagiTruckPickup by lagiKund into lagiTruckPickupGroups
                         select new OperationCenterUncompleteData
                         {
                             LagiId = lagiTruckPickupGroups.Key.Id,
                             LagiMasterIndentNo = lagiTruckPickupGroups.Key.LagiMasterIdentNo,
                             LagiMawbNo = lagiTruckPickupGroups.Key.LagiMawbNo,
                             LagiMawbPrefix = lagiTruckPickupGroups.Key.LagiMawbPrefix,
                             LagiQuantityExpected = lagiTruckPickupGroups.Key.LagiQuantityExpected,
                             LagiWeightExpected = lagiTruckPickupGroups.Key.LagiWeightExpected,
                             LagiHawb = lagiTruckPickupGroups.Key.LagiHawb,
                             LagiLvgIn = lagiTruckPickupGroups.Key.LagiLvgIn,
                             LagiFlightNoIn = lagiTruckPickupGroups.Key.LagiFlightNoIn,
                             LagiFlightDateIn = lagiTruckPickupGroups.Key.LagiFlightDateIn,
                             LagiFlightTimeIn = lagiTruckPickupGroups.Key.LagiFlightTimeIn,
                             TruckNo = string.Join(',', lagiTruckPickupGroups.Where(x => x.VehicRegNo != null).Select(x => x.VehicRegNo).Distinct()),
                             Remark = lagiTruckPickupGroups.Key.LagiShipmentRemarks,
                             Irr = string.Join(',', lagiTruckPickupGroups.Where(x => !string.IsNullOrWhiteSpace(x.Place)).Select(x => x.Place).Distinct()),
                             //DeliveryDate = lagiTruckPickupGroups.Where(x => x.VhclUnloadingEtaDate != null).Select(x => x.VhclUnloadingEtaDate).FirstOrDefault(),
                             //DeliveryTime = lagiTruckPickupGroups.Where(x => x.VhclUnloadingEtaTime != null).Select(x => x.VhclUnloadingEtaTime).FirstOrDefault(),
                             Agent = lagiTruckPickupGroups.Key.LagiNotifyName,
                             CargoterminalId = lagiTruckPickupGroups.Key.LagiShedCode,
                             VhclUnloadingArrivalDate = lagiTruckPickupGroups.Where(x => x.VhclUnloadingEtaDate != null).Select(x => x.VhclUnloadingEtaDate).FirstOrDefault(),
                             VhclUnloadingArrivalTime = lagiTruckPickupGroups.Where(x => x.VhclUnloadingEtaTime != null).Select(x => x.VhclUnloadingEtaTime).FirstOrDefault(),
                             CDStatus = lagiTruckPickupGroups.Key.LagiDateStatus3set == null ? "No" : "Yes",
                             VhclLoadingArrivalDateTime = string.IsNullOrWhiteSpace(lagiTruckPickupGroups.Where(x => x.VhclLoadingArrivalDateTS != null).Select(x => x.VhclLoadingArrivalDateTS).FirstOrDefault().ToString()) ? " " : (string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)lagiTruckPickupGroups.Where(x => x.VhclLoadingArrivalDateTS != null).Select(x => x.VhclLoadingArrivalDateTS).FirstOrDefault(), lagiTruckPickupGroups.Where(x => x.VhclLoadingArrivalDateTS != null).Select(x => x.VhclLoadingArrivalTimeTS).FirstOrDefault()))),
                         });
            return query.ToList();
        }

        public async Task<List<OperationCenterSummaryList>> GetOperationCenterSummaryAsync()
        {
            var lagiTemp = (from lagi in (await GetDbSetAsync()).Where(x => x.LagiMasterIdentNo != 0 && x.LagiDeleted == false)
                            select new
                            {
                                lagi.Id,
                                lagi.LagiQuantityExpected,
                                lagi.LagiWeightExpected,
                                lagi.LagiDateStatus0set,
                                lagi.LagiDateStatus1set,
                                lagi.LagiDateStatus4set,
                                lagi.LagiLvgIn,
                                lagi.LagiFlightNoIn,
                                lagi.LagiFlightDateIn,
                                lagi.LagiFlightTimeIn,
                            });

            var lagiTruckPickupQuery = (from lagi in lagiTemp
                                        join vhld in (await GetDbContextAsync()).VhldVehicleDetails.Where(x => x.VhldDeleted == false) on lagi.Id equals vhld.VhldObjectisn into vhldGroups
                                        from vhldGroup in vhldGroups.DefaultIfEmpty()
                                        join vhclDLV in (await GetDbContextAsync()).VehiclesRegistrations
                                        .Where(x => x.VhclImportExport == "DELIVERY_PLAN")
                                        on vhldGroup.VhldVehicleisn equals vhclDLV.Id into vhclGroupsDLV
                                        from vhclGroupDLV in vhclGroupsDLV.DefaultIfEmpty()

                                        select new
                                        {
                                            lagi.Id,
                                            vhclGroupDLV.VhclUnloadingArrivalDate,
                                            vhclGroupDLV.VhclUnloadingArrivalTime,
                                            vhclGroupDLV.VhclUnloadingEtaDate,
                                            vhclGroupDLV.VhclUnloadingEtaTime,
                                        }).Distinct();
            var plan = (from lagi in lagiTemp.ToList()
                        join lagiTruckPickup in lagiTruckPickupQuery on lagi.Id equals lagiTruckPickup.Id
                        group lagiTruckPickup by lagi into lagiTruckPickupGroups
                        select new OperationCenterSummaryList
                        {
                            LagiId = lagiTruckPickupGroups.Key.Id,
                            LagiQuantityExpected = lagiTruckPickupGroups.Key.LagiQuantityExpected,
                            LagiWeightExpected = lagiTruckPickupGroups.Key.LagiWeightExpected,
                            LagiDateStatus0set = lagiTruckPickupGroups.Key.LagiDateStatus0set,
                            LagiDateStatus1set = lagiTruckPickupGroups.Key.LagiDateStatus1set,
                            LagiDateStatus4set = lagiTruckPickupGroups.Key.LagiDateStatus4set,
                            VhclUnloadingArrivalDate = lagiTruckPickupGroups.Where(x => x.VhclUnloadingEtaDate != null).Select(x => x.VhclUnloadingEtaDate).FirstOrDefault(),
                            VhclUnloadingArrivalTime = lagiTruckPickupGroups.Where(x => x.VhclUnloadingEtaTime != null).Select(x => x.VhclUnloadingEtaTime).FirstOrDefault(),
                            LagiLvgIn = lagiTruckPickupGroups.Key.LagiLvgIn,
                            LagiFlightNoIn = lagiTruckPickupGroups.Key.LagiFlightNoIn,
                            LagiFlightDateIn = lagiTruckPickupGroups.Key.LagiFlightDateIn,
                            LagiFlightTimeIn = lagiTruckPickupGroups.Key.LagiFlightTimeIn,
                        });
            return plan.ToList();
        }

        protected async Task<IQueryable<AwbCustomsDeclaration>> ApplyFilterListAwbCustomsDeclaration(
            List<string> listCd = null,
            List<string> listHawb = null,
            string cdType = null,
            string cdSource = null,
            string agentName = null,
            int flightDateNumberFrom = 0,
            int flightDateNumberTo = 0,
            bool deleted = false,
            bool isIgnoreStatus4 = false,
            string isCleared = null,
            string approvedDateFrom = null,
            string approvedDateTo = null)
        {
            DateTime startDate = DateTime.Now.Date.AddDays(-1000);
            DateTime endDate = DateTime.Now.Date.AddDays(1).AddTicks(-1);
            if (!string.IsNullOrWhiteSpace(approvedDateFrom))
            {
                if (DateTime.TryParseExact(approvedDateFrom, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startDate = date.Date;
                }
            }
            if (!string.IsNullOrWhiteSpace(approvedDateTo))
            {
                if (DateTime.TryParseExact(approvedDateTo, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    endDate = date.Date.AddDays(1).AddTicks(-1);
                }
            }
            var query = (from lagi in (await GetDbSetAsync())
                                    .Where(x => x.LagiMasterIdentNo != 0)
                                    .WhereIf(listHawb != null, x => listHawb.Contains(x.LagiHawb))

                                    .WhereIf(flightDateNumberFrom != 0 && flightDateNumberTo != 0, x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                                    .WhereIf(!agentName.IsNullOrWhiteSpace(), x => x.LagiNotifyName.Contains(agentName))
                                    .WhereIf(isCleared == "true", x => x.LagiDateStatus3set != null && x.LagiTimeStatus3set != null)
                                    .WhereIf(isCleared == "false", x => x.LagiDateStatus3set == null && x.LagiTimeStatus3set == null)
                         join cd in (await GetDbContextAsync()).CustomsDeclarations
                             .WhereIf(!cdType.IsNullOrWhiteSpace(), x => x.CdType.Equals(cdType))
                             .WhereIf(listCd != null, x => listCd.Contains(x.CdNsx))
                             on lagi.Id equals cd.CdLagiId into ljcd
                         from cd in ljcd.DefaultIfEmpty()
                         select new AwbCustomsDeclaration
                         {
                             LagiId = lagi.Id,
                             CdId = cd.Id,
                             LagiMawbPrefix = lagi.LagiMawbPrefix,
                             LagiMawbNo = lagi.LagiMawbNo,
                             LagiHawb = lagi.LagiHawb,
                             LagiLvgIn = lagi.LagiLvgIn,
                             LagiFlightNoIn = lagi.LagiFlightNoIn,
                             LagiFlightDateIn = lagi.LagiFlightDateIn,
                             LagiQuantityExpected = lagi.LagiQuantityExpected,
                             LagiQuantityReceived = lagi.LagiQuantityReceived,
                             LagiWeightExpected = lagi.LagiWeightExpected,
                             LagiWeightReceived = lagi.LagiWeightReceived,
                             LagiDateStatus3Set = lagi.LagiDateStatus3set,
                             LagiCustomRefNo = lagi.LagiCustomRefNo,
                             LagiNotifyName = lagi.LagiNotifyName,
                             CdInvoiceNo = cd.CdInvoiceNo,
                             CdNsx = cd.CdNsx,
                             CdType = cd.CdType,
                             CdCustomsBranch = cd.CdCustomsBranch,
                             CdGw = cd.CdGw,
                             CdPcs = cd.CdPcs,
                             CdApproveDate = cd.CdApproveDate,
                             CdSource = cd.CdSource
                         });
            query = query.OrderByDescending(x => x.LagiFlightDateIn)
                  .Where(x => x.CdApproveDate >= startDate && x.CdApproveDate <= endDate)
                  .WhereIf(!cdSource.IsNullOrWhiteSpace(), x => x.CdSource.ToUpper().Equals(cdSource.ToUpper()));
            return query;
        }

        public async Task<List<AwbCustomsDeclaration>> GetListAwbCustomsDeclarationAsync(List<string> listCd = null, List<string> listHawb = null, string cdType = null, string cdSource = null, string agentName = null, int flightDateNumberFrom = 0, int flightDateNumberTo = 0, bool deleted = false, bool isIgnoreStatus4 = false, string isCleared = null, string approvedDateFrom = null, string approvedDateTo = null, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var query = await ApplyFilterListAwbCustomsDeclaration(listCd, listHawb, cdType, cdSource, agentName, flightDateNumberFrom, flightDateNumberTo, deleted, isIgnoreStatus4, isCleared, approvedDateFrom, approvedDateTo);
            var data = await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            return data;
        }

        public async Task<long> GetCountAwbCustomsDeclarationAsync(List<string> listCd = null, List<string> listHawb = null, string cdType = null, string cdSource = null, string agentName = null, int flightDateNumberFrom = 0, int flightDateNumberTo = 0, bool deleted = false, bool isIgnoreStatus4 = false, string isCleared = null, string approvedDateFrom = null, string approvedDateTo = null, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            var query = await ApplyFilterListAwbCustomsDeclaration(listCd, listHawb, cdType, cdSource, agentName, flightDateNumberFrom, flightDateNumberTo, deleted, isIgnoreStatus4, isCleared, approvedDateFrom, approvedDateTo);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Retrieves a list of HawbGroupV1 objects based on the provided parameters.
        /// </summary>
        /// <param name="lagiId">The ID of the Lagi.</param>
        /// <param name="prefix">The prefix.</param>
        /// <param name="serialNo">The serial number.</param>
        /// <param name="hawb">The HAWB number.</param>
        /// <param name="awb">The AWB number.</param>
        /// <param name="maxResultCount">The maximum number of results to return.</param>
        /// <param name="skipCount">The number of results to skip.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A list of HawbGroupV1 objects.</returns>
        public async Task<List<HawbGroupV1>> FindNotDeliveredAwbAsync(
            long? lagiId = 0,
            string prefix = null,
            long? serialNo = 0,
            string hawb = null,
            string awb = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryFindNotDeliveredAwb(lagiId, prefix, serialNo, hawb, awb);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }
        /// <summary>
        /// Retrieves the count of a group of Hawb records based on the specified criteria.
        /// </summary>
        /// <param name="lagiId">The ID of the Lagi record.</param>
        /// <param name="prefix">The prefix of the Hawb records.</param>
        /// <param name="serialNo">The serial number of the Hawb records.</param>
        /// <param name="hawb">The Hawb number.</param>
        /// <param name="awb">The Awb number.</param>s
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The count of the group of Hawb records.</returns>
        public async Task<long> GetCountFindNotDeliveredAwbAsync(
            long? lagiId = 0,
            string prefix = null,
            long? serialNo = 0,
            string hawb = null,
            string awb = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryFindNotDeliveredAwb(lagiId, prefix, serialNo, hawb, awb);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }
        /// <summary>
        /// Retrieves a queryable collection of HawbGroupV1 objects based on the specified criteria (HAWB or MAWB contain param awb).
        /// </summary>
        /// <param name="lagiId">The ID of the Lagi entity.</param>
        /// <param name="prefix">The Mawb prefix.</param>
        /// <param name="serialNo">The Mawb serial number.</param>
        /// <param name="hawb">The Hawb value.</param>
        /// <param name="awb">The Awb value.</param>
        /// <returns>A queryable collection of HawbGroupV1 objects.</returns>
        private async Task<IQueryable<HawbGroupV1>> GetQueryFindNotDeliveredAwb(
            long? lagiId = 0,
            string prefix = null,
            long? serialNo = 0,
            string hawb = null,
            string awb = null)
        {
            var query = (from lagi in (await GetDbSetAsync()).Where(x => x.LagiDeleted == false && x.LagiDateStatus4set == null)
                              .WhereIf(!string.IsNullOrWhiteSpace(awb), e => awb.Contains(e.LagiHawb)
                                                                           || awb.Contains(e.LagiMawbPrefix + e.LagiMawbNo.ToString()))
                              .WhereIf(lagiId.HasValue, e => e.Id == lagiId)
                              .WhereIf(!string.IsNullOrWhiteSpace(prefix), e => e.LagiMawbPrefix.Contains(prefix.Trim()))
                              .WhereIf(serialNo.HasValue, e => e.LagiMawbNo == serialNo)
                              .WhereIf(!string.IsNullOrWhiteSpace(hawb), e => e.LagiHawb.Contains(hawb.Trim().ToUpper()))
                           .OrderByDescending(x => x.LagiFlightDateIn).ThenByDescending(x => x.Id)
                         select new HawbGroupV1
                         {
                             LagiId = lagi.Id,
                             MawbPrefix = lagi.LagiMawbPrefix,
                             MawbSerial = lagi.LagiMawbNo,
                             Hawb = lagi.LagiHawb,
                             LagiShedCode = lagi.LagiShedCode,
                             LagiLvgIn = lagi.LagiLvgIn,
                             LagiFlightNoIn = lagi.LagiFlightNoIn,
                             Pieces = lagi.LagiQuantityReceived,
                             Weight = lagi.LagiWeightReceived,
                             CargoTerminal = lagi.LagiShedCode,
                             FlightDateIn = lagi.LagiFlightDateIn,
                             AgentName = lagi.LagiNotifyName
                         });
            return query;
        }

        public async Task<List<TruckOla>> GetListOlaExportAsync(
            string truckNumber = null,
            long? ecusId = 0,
            string olaNumber = null,
            string dateFrom = null, string dateTo = null,
            string status = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryOlaExport(truckNumber, ecusId, olaNumber, dateFrom, dateTo, status);
            return await query.ToListAsync(cancellationToken);
        }
        private async Task<IQueryable<TruckOla>> GetQueryOlaExport(
           string truckNumber = null,
           long? ecusId = 0,
           string olaNumber = null,
           string dateFrom = null,
           string dateTo = null,
           string status = null)
        {
            DateTime startDate = DateTime.Now.Date.AddDays(-1000);
            DateTime endDate = DateTime.Now.Date.AddDays(1).AddTicks(-1);
            if (!string.IsNullOrWhiteSpace(dateFrom))
            {
                if (DateTime.TryParseExact(dateFrom, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startDate = date.Date;
                }
            }
            if (!string.IsNullOrWhiteSpace(dateTo))
            {
                if (DateTime.TryParseExact(dateTo, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    endDate = date.Date.AddDays(1).AddTicks(-1);
                }
            }
            return (from vhld in (await GetDbContextAsync()).OlaVhldVehicleDetails.Where(x => x.VhldDeleted == false)
                    join
                    vehicleReg in (await GetDbContextAsync()).OlaVehiclesRegistrations
                          .Where(x => x.VhclImportExport == "IMPORT" && x.VhclTruckType == "ECUS")
                          .WhereIf(!string.IsNullOrWhiteSpace(truckNumber), x => x.VehicRegNo == truckNumber)
                          .WhereIf(ecusId.HasValue, e => e.VhclMessageIsn == ecusId)
                          .WhereIf(!string.IsNullOrWhiteSpace(olaNumber), e => e.VhclRacNumber.Contains(olaNumber))
                          .Where(x => x.VhclLoadingArrivalDate >= startDate && x.VhclLoadingArrivalDate <= endDate)
                          .WhereIf(status == "0", x => x.VhclRacNumber == null || x.VhclRacNumber.Trim() == "")
                          .WhereIf(status == "1", x => x.VhclRacNumber != null && x.VhclRacNumber.Trim() != "")
                    on vhld.VhldVehicleisn equals vehicleReg.Id
                    join lagi in (await GetDbContextAsync()).Lagis
                    on vhld.VhldObjectisn equals lagi.Id
                    //join cd in (await GetDbContextAsync()).CustomsDeclarations
                    //on vhld.VhldObjectisn equals cd.CdLagiId
                    select new TruckOla
                    {
                        VehicleRegId = vehicleReg.Id,
                        VhclRegNo = vehicleReg.VehicRegNo,
                        VhclLinkedIsn = vehicleReg.VhclLinkedIsn,
                        VhclMessageIsn = vehicleReg.VhclMessageIsn,
                        VhclRacNumber = vehicleReg.VhclRacNumber,
                        OlaDate = vehicleReg.VhclLoadingArrivalDate,
                        Hawb = lagi.LagiHawb,
                        Hys = vhld.VhldUcrNo.ToString(),
                        //CDNumber = cd.CdNsx,
                        //CDCount = 1,
                        Pcs = (int)vhld.VhldLoadedPieces,
                        Gw = (decimal)vhld.VhldLoadedWeight,
                        ObjectIsn = vhld.VhldObjectisn
                    }).OrderBy(x => x.OlaDate);
        }
        private async Task<IQueryable<HawbGroupV1>> GetQueryListGroupHawbV2(
           long? lagiId = 0,
           string prefix = null,
           long? serialNo = 0,
           string hawb = null,
           string flight = null,
           long? agent = null,
           long? consigneeId = 0,
           string cargoTerminal = null,
           string awb = null,
           string flightDateFrom = null, string flightDateTo = null)
        {
            int flightDateNumberFrom = 0;
            if (!string.IsNullOrWhiteSpace(flightDateFrom))
            {
                flightDateNumberFrom = CommonFunction.ConverDateStringToNumber(flightDateFrom);
            }

            int flightDateNumberTo = 0;
            if (!string.IsNullOrWhiteSpace(flightDateTo))
            {
                flightDateNumberTo = CommonFunction.ConverDateStringToNumber(flightDateTo);
            }

            var mawb = "";
            if (!string.IsNullOrWhiteSpace(awb) && awb.Length > 3 && int.TryParse(awb[3..], out int mawbNo))
            {
                mawb = awb[..3] + mawbNo.ToString();
            }

            var dbContext = await GetDbContextAsync();
            var result = (from apud in dbContext.AwbPlanUploadDetails
                          join lagi in dbContext.Lagis
                               .Where(x => x.LagiDeleted == false)
                               .WhereIf(!string.IsNullOrWhiteSpace(awb) && string.IsNullOrWhiteSpace(mawb), e => e.LagiHawb.Contains(awb)
                                                                            || e.LagiMawbPrefix.Contains(awb)
                                                                            || e.LagiMawbNo.ToString().Contains(awb)
                                                                            || (e.LagiMawbPrefix + e.LagiMawbNo.ToString()).Contains(awb))
                               .WhereIf(!string.IsNullOrWhiteSpace(mawb), e => e.LagiHawb.Contains(awb)
                                                                            || e.LagiMawbNo.ToString().Contains(awb)
                                                                            || (e.LagiMawbPrefix + e.LagiMawbNo.ToString()).Contains(mawb))
                               .WhereIf(lagiId.HasValue, e => e.Id == lagiId)
                               .WhereIf(consigneeId > 0, e => e.LagiConsigneeNumber == consigneeId)
                               .WhereIf(serialNo.HasValue, e => e.LagiMawbNo == serialNo)
                               .WhereIf(!string.IsNullOrWhiteSpace(hawb), e => e.LagiHawb.Contains(hawb.Trim().ToUpper()))
                               .WhereIf(agent.HasValue, e => e.LagiNotifyNumber == agent)
                               .WhereIf(!string.IsNullOrWhiteSpace(prefix), e => e.LagiMawbPrefix.Contains(prefix.Trim()))
                               .WhereIf(!string.IsNullOrWhiteSpace(flight), e => (e.LagiLvgIn + e.LagiFlightNoIn).Contains(flight.ToUpper()))
                               .WhereIf(!string.IsNullOrWhiteSpace(cargoTerminal), e => e.LagiShedCode == cargoTerminal)
                               .WhereIf(!string.IsNullOrWhiteSpace(flightDateFrom) && !string.IsNullOrWhiteSpace(flightDateTo), x => x.LagiFlightDateIn != 0 && x.LagiFlightDateIn >= flightDateNumberFrom && x.LagiFlightDateIn <= flightDateNumberTo)
                          on apud.ApudSync equals lagi.Id
                          where
                                apud.ApudDeleted == false
                                && apud.ApudPlanType == "PICKUP"
                                && !string.IsNullOrWhiteSpace(lagi.LagiHawb)
                                && (
                                     lagi.LagiQuantityReceived >
                                    (
                                        (from vhld in dbContext.VhldVehicleDetails
                                         where vhld.VhldRecordtype == "TRANSIT"
                                               && vhld.VhldObjectisn == lagi.Id
                                               && vhld.VhldDeleted == false
                                         group vhld by new { vhld.VhldObjectisn } into g
                                         select g.Sum(v => v.VhldLoadedPieces)
                                    ).FirstOrDefault() ?? 0
                                ))

                          select new HawbGroupV1
                          {
                              LagiId = lagi.Id,
                              MawbPrefix = lagi.LagiMawbPrefix,
                              MawbSerial = lagi.LagiMawbNo,
                              Hawb = lagi.LagiHawb,
                              LagiShedCode = lagi.LagiShedCode,
                              LagiLvgIn = lagi.LagiLvgIn,
                              LagiFlightNoIn = lagi.LagiFlightNoIn,
                              Pieces = lagi.LagiQuantityExpected,
                              Weight = lagi.LagiWeightExpected,
                              CargoTerminal = lagi.LagiShedCode,
                              FlightDateIn = lagi.LagiFlightDateIn,
                              AgentName = lagi.LagiNotifyName,
                              ConsigneeName = lagi.LagiConsigneeName,
                              ApudFlightEtaDate = apud.ApudFlightEtaDate,
                              ApudFlightEtaTime = apud.ApudFlightEtaTime,
                              ApudListId = apud.ApudListId,
                              ApudNo = apud.ApudNo

                          }).OrderBy(x => x.ApudListId).ThenBy(x => x.ApudNo).ThenBy(x => x.ApudFlightEtaDate).ThenBy(x => x.ApudFlightEtaTime);

            return result;
        }
    }
}