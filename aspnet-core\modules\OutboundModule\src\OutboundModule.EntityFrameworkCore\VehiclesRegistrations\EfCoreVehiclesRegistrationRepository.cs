using MasterDataModule.VhslVehicleSealInfos;
using Microsoft.EntityFrameworkCore;
using Oracle.ManagedDataAccess.Client;
using OutboundModule.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Z.EntityFramework.Plus;

namespace OutboundModule.VehiclesRegistrations
{
    public class EfCoreVehiclesRegistrationRepository : EfCoreRepository<OutboundModuleDbContext, VehiclesRegistration, long>, IVehiclesRegistrationRepository
    {
        public EfCoreVehiclesRegistrationRepository(IDbContextProvider<OutboundModuleDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<List<Warehouse.ReceivingPlan.VehiclesRegistration>> GetListVehiclesRegistrationAsync(DateTime? date, long? customerId, long? truckId, string assigneeId,
            string sorting,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var tenantCondition = CurrentTenant.Id.HasValue ? "vhcl.tenant_id = :current_tenant" : "vhcl.tenant_id IS NULL";

            var query = @"
                      SELECT
                            vhcl.id                           ""VhclRegisId"",
                            vhcl_loading_arrival_date         ""VhclLoadingArrivalDate"",
                            vhcl_loading_arrival_time         ""VhclLoadingArrivalTime"",
                            vehic_reg_no                      ""VehicRegNo"",
                            vhcl_company                      ""VhclCompany"",
                            vhcl_truck_type                   ""VhclTruckType"",
                            vhcl_loading_at_door_date         ""VhclLoadingAtDoorDate"",
                            vhcl_loading_at_door_time         ""VhclLoadingAtDoorTime"",
                            vhcl_loading_vehicle_closed_date  ""VhclLoadingVehicleClosedDate"",
                            vhcl_loading_vehicle_closed_time  ""VhclLoadingVehicleClosedTime"",
                            vhcl_loading_left_date            ""VhclLoadingLeftDate"",
                            vhcl_loading_left_time            ""VhclLoadingLeftTime"",
                            vhcl_unloading_eta_date           ""VhclUnloadingEtaDate"",
                            vhcl_unloading_eta_time           ""VhclUnloadingEtaTime"",
                            vhcl_unloading_arrival_date       ""VhclUnloadingArrivalDate"",
                            vhcl_unloading_arrival_time       ""VhclUnloadingArrivalTime"",
                            vhcl_unloading_activation_date    ""VhclUnloadingActivationDate"",
                            vhcl_unloading_activation_time    ""VhclUnloadingActivationTime"",
                            vhcl_completed_date               ""VhclCompletedDate"",
                            vhcl_completed_time               ""VhclCompletedTime"",
                            vhcl_assignee_name                ""VhclAssigneeName"",
                            vhcl_vehicle_complete             ""VhclVehicleComplete"",
                            SUM(labs_quantity_del)            ""LabsQuantityDel"",
                            SUM(labs_quantity_booked)         ""LabsQuantityBooked"",
                            COUNT(*)
                            OVER()                            ""TotalCount""
                        FROM
                            aomsvehiclesregistration  vhcl
                            LEFT JOIN aomsvhldvehicledetail     vhld ON vhcl.id = vhld.vhld_vehicleisn
                            LEFT JOIN aomslabs labs ON vhld.vhld_objectisn = labs.id
                        GROUP BY
                            vhcl.id,
                            vhcl.tenant_id,
                            vhcl_loading_arrival_date,
                            vhcl_loading_arrival_time,
                            vehic_reg_no,
                            vhcl_master_isn,
                            vhcl_company,
                            vhcl_provider_customer_isn,
                            vhcl_loading_at_door_date,
                            vhcl_loading_at_door_time,
                            vhcl_loading_vehicle_closed_date,
                            vhcl_loading_vehicle_closed_time,
                            vhcl_loading_left_date,
                            vhcl_loading_left_time,
                            vhcl_unloading_eta_date,
                            vhcl_unloading_eta_time,
                            vhcl_unloading_arrival_date,
                            vhcl_unloading_arrival_time,
                            vhcl_unloading_activation_date,
                            vhcl_unloading_activation_time,
                            vhcl_completed_date,
                            vhcl_completed_time,
                            vhcl_assignee_name,
                            vhcl_assignee_id,
                            vhcl_vehicle_complete,
                            vhcl_truck_type
                        HAVING(" + tenantCondition + @"
                                 AND((vhcl_truck_type = 'AT DOOR'
                                         AND trunc(vhcl_unloading_arrival_date) = to_date(:vhcl_unloading_arrival_date, 'YYYY-MM-DD'))
                                       OR(vhcl_truck_type = 'PICKUP'
                                            AND trunc(vhcl_loading_arrival_date) = to_date(:vhcl_loading_arrival_date, 'YYYY-MM-DD'))) )";
            var parameters = new List<OracleParameter>();
            parameters.Add(new OracleParameter(":current_tenant", CurrentTenant.Id?.ToByteArray()));
            parameters.Add(new OracleParameter(":vhcl_unloading_arrival_date", date.HasValue ? date.Value.ToString("yyyy-MM-dd") : DateTime.Now.ToString("yyyy-MM-dd")));
            parameters.Add(new OracleParameter(":vhcl_loading_arrival_date", date.HasValue ? date.Value.ToString("yyyy-MM-dd") : DateTime.Now.ToString("yyyy-MM-dd")));

            if (customerId.HasValue)
            {
                query = query + " AND vhcl_provider_customer_isn = :vhclProviderCustomerIsn";
                parameters.Add(new OracleParameter(":vhclProviderCustomerIsn", customerId));
            }

            if (truckId.HasValue)
            {
                query = query + " AND vhcl_master_isn = :vhclMasterIsn";
                parameters.Add(new OracleParameter(":vhclMasterIsn", truckId));
            }

            if (!string.IsNullOrWhiteSpace(assigneeId))
            {
                query = query + " AND vhcl_assignee_id = :vhclAssigneeName";
                parameters.Add(new OracleParameter(":vhclAssigneeName", assigneeId));
            }

            var results = await (await GetDbContextAsync()).RP_VehiclesRegistrations.FromSqlRaw(query, parameters.ToArray()).OrderBy(x => x.VhclVehicleComplete).ThenByDescending(x => x.VhclLoadingArrivalDate).ThenByDescending(x => x.VhclLoadingArrivalTime).PageBy(skipCount, maxResultCount).Future().ToListAsync(GetCancellationToken(cancellationToken));
            return results;
        }

        public async Task<Warehouse.ReceivePlanDetails.VehiclesRegistration> GetVehiclesRegistrationByIdAsync(long vhclRegisId)
        {
            var query = @"
                       SELECT
                            vr.vehic_reg_no                      ""VehicRegNo"",
                            vr.vhcl_remarks                      ""VhclRemarks"",
                            vr.vhcl_loading_door                 ""VhclLoadingDoor"",
                            vr.vhcl_assignee_name                ""VhclAssigneeName"",
                            vr.vhcl_driver_name                  ""VhclDriverName"",
                            vr.vhcl_loading_arrival_date         ""VhclLoadingArrivalDate"",
                            vr.vhcl_loading_arrival_time         ""VhclLoadingArrivalTime"",
                            vr.vhcl_loading_at_door_date         ""VhclLoadingAtDoorDate"",
                            vr.vhcl_loading_at_door_time         ""VhclLoadingAtDoorTime"",
                            vr.vhcl_loading_vehicle_closed_date  ""VhclLoadingVehicleClosedDate"",
                            vr.vhcl_loading_vehicle_closed_time  ""VhclLoadingVehicleClosedTime"",
                            vr.vhcl_loading_left_date            ""VhclLoadingLeftDate"",
                            vr.vhcl_loading_left_time            ""VhclLoadingLeftTime"",
                            vr.vhcl_unloading_eta_date           ""VhclUnloadingEtaDate"",
                            vr.vhcl_unloading_eta_time           ""VhclUnloadingEtaTime"",
                            vr.vhcl_unloading_arrival_date       ""VhclUnloadingArrivalDate"",
                            vr.vhcl_unloading_arrival_time       ""VhclUnloadingArrivalTime"",
                            vr.vhcl_unloading_activation_date    ""VhclUnloadingActivationDate"",
                            vr.vhcl_unloading_activation_time    ""VhclUnloadingActivationTime"",
                            vr.vhcl_completed_date               ""VhclCompletedDate"",
                            vr.vhcl_completed_time               ""VhclCompletedTime"",
                            vsi.vhsl_seal_number                 ""VhslSealNumber"",
                            vhcl.vehicle_load_weight             ""VehicleLoadWeight"",
                            door.door_number                     ""DoorNumber"",
                            sslp.sslp_rack_location              ""SslpRackLocation""
                        FROM
                            aomsvehiclesregistration  vr
                            LEFT JOIN aomsvhslvehiclesealinfo   vsi ON vr.vhcl_master_isn = vsi.vhsl_vehicle_reg_isn
                            LEFT JOIN aomsvehicle vhcl ON vr.vhcl_master_isn = vhcl.id
                            LEFT JOIN aomsdoorcfs door ON vr.vhcl_unloading_door_used = door.id
                            LEFT JOIN aomssslpphysicallocations sslp ON vr.vhcl_unloading_slot_isn = sslp.id
                        WHERE
                            vr.id = :vhclId";
            var parameters = new List<OracleParameter>();
            parameters.Add(new OracleParameter(":vhclId", vhclRegisId));
            var result = (await GetDbContextAsync()).RPD_VehiclesRegistrations.FromSqlRaw(query, parameters.ToArray()).Future().FirstOrDefault();
            return result;
        }

        public async Task<List<Warehouse.ReceivePlanDetails.Waybill>> GetListWayBillByVehiclesRegistrationIdAsync(long vhclRegisId,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = @"
                        SELECT
                            hawb.hawb_house_number       ""HawbHouseNumber"",
                            mawb.labs_mawb_prefix        ""LabsMawbPrefix"",
                            mawb.labs_mawb_serial_no     ""LabsMawbSerialNo"",
                            mawb.labs_quantity_del       ""LabsQuantityDel"",
                            mawb.labs_quantity_booked    ""LabsQuantityBooked"",
                            mawb.labs_weight_del         ""LabsWeightDel"",
                            mawb.labs_weight_booked      ""LabsWeightBooked"",
                            COUNT(*)
                            OVER()                       ""TotalCount""
                        FROM
                            (
                                SELECT
                                    vr.id vrid,
                                    labs.labs_mawb_prefix,
                                    labs.labs_mawb_serial_no,
                                    labs.labs_quantity_del,
                                    labs.labs_quantity_booked,
                                    labs.labs_weight_del,
                                    labs.labs_weight_booked
                                FROM
                                    aomsvehiclesregistration  vr
                                    LEFT JOIN aomsvhldvehicledetail     vd ON vr.id = vd.vhld_vehicleisn
                                    LEFT JOIN aomslabs                  labs ON vd.vhld_objectisn = labs.id
                                WHERE
                                        vd.vhld_deleted = 0
                                    AND labs.labs_deleted = 0
                            )  mawb
                            FULL OUTER JOIN(
                                SELECT
                                    vr.id vrid,
                                    hhwbd.hawb_house_number
                                FROM
                                    aomsvehiclesregistration vr
                                    LEFT JOIN aomsvhdhvehicledetailhawb vdh ON vr.id = vdh.vhdh_vehicleisn
                                    LEFT JOIN aomshawbhousewaybilldetails hhwbd ON vdh.vhdh_hawb_isn = hhwbd.id
                            )  hawb ON mawb.vrid = hawb.vrid
                        WHERE
                            mawb.vrid = :vhclRegisId
                        ";
            var parameters = new List<OracleParameter>();
            parameters.Add(new OracleParameter(":vhclRegisId", vhclRegisId));
            var results = await (await GetDbContextAsync()).RPD_Waybills.FromSqlRaw(query, parameters.ToArray()).PageBy(skipCount, maxResultCount).Future().ToListAsync();
            return results;
        }

        public async Task<List<ReceivingPlan>> GetListReceivingPlanAsync(
            string keyword = null,
            long? Id = null,
            long? customerId = null,
            string assigneeId = null,
            string etaDate = null,
            string status = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(etaDate))
            {
                if (DateTime.TryParseExact(etaDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startOfDay = date.Date;

                    endOfDay = date.Date.AddDays(1).AddTicks(-1);
                }
            }

            var query = from vhcl in (await GetDbSetAsync()).Where(x => x.VhclUnloadingEtaDate != null && x.VhclMasterIsn == 0)
                                .WhereIf(!string.IsNullOrWhiteSpace(etaDate), x => x.VhclUnloadingEtaDate >= startOfDay && x.VhclUnloadingEtaDate <= endOfDay)
                                .WhereIf(!string.IsNullOrWhiteSpace(assigneeId), x => x.VhclAssigneeId.Equals(assigneeId))
                                .WhereIf(customerId.HasValue, x => x.VhclProviderCustomerIsn == customerId)
                                .WhereIf(Id.HasValue, x => x.Id == Id)
                                .WhereIf(!string.IsNullOrWhiteSpace(keyword), x => x.VhclCompany.ToUpper().Contains(keyword.ToUpper()) || x.VhclAssigneeName.ToUpper().Contains(keyword.ToUpper()) || x.Id.ToString() == keyword)
                                .WhereIf(!string.IsNullOrWhiteSpace(status) && status.Equals("Completed"),
                                                                          x => x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate != null && (x.VhclUnloadingArrivalTime != null || !x.VhclUnloadingArrivalTime.Equals("0"))
                                                                            && x.VhclVehicleComplete == true)
                                .WhereIf(!string.IsNullOrWhiteSpace(status) && status.Equals("Receiving"),
                                                                          x => x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate != null && (x.VhclUnloadingArrivalTime != null || !x.VhclUnloadingArrivalTime.Equals("0"))
                                                                            && x.VhclVehicleComplete == false)
                                .WhereIf(!string.IsNullOrWhiteSpace(status) && status.Equals("Approved"),
                                                                          x => x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate == null && (x.VhclUnloadingArrivalTime == null || x.VhclUnloadingArrivalTime.Equals("0")))
                                .WhereIf(!string.IsNullOrWhiteSpace(status) && status.Equals("Pending"),
                                                                          x => x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate == null && x.VhclUnloadingActivationTime.Equals("0"))
                        select new ReceivingPlan
                        {
                            Id = vhcl.Id,
                            VhclUnloadingEtaDate = vhcl.VhclUnloadingEtaDate,
                            VhclUnloadingEtaTime = vhcl.VhclUnloadingEtaTime,
                            VhclCompany = vhcl.VhclCompany,
                            VhclAssigneeName = vhcl.VhclAssigneeName,
                            VhclSupervisorName = vhcl.VhclSupervisorName,
                            Status = string.Empty,
                            VhclUnloadingActivationDate = vhcl.VhclUnloadingActivationDate,
                            VhclUnloadingActivationTime = vhcl.VhclUnloadingActivationTime,
                            VhclUnloadingArrivalDate = vhcl.VhclUnloadingArrivalDate,
                            VhclUnloadingArrivalTime = vhcl.VhclUnloadingArrivalTime,
                            VhclVehicleComplete = vhcl.VhclVehicleComplete,
                            VhclAssigneeId = vhcl.VhclAssigneeId,
                            VhclProviderCustomerIsn = vhcl.VhclProviderCustomerIsn,
                            VhclSupervisorId = vhcl.VhclSupervisorId
                        };

            var dataList = query.Select(vhcl => new ReceivingPlan
            {
                Id = vhcl.Id,
                VhclUnloadingEtaDate = vhcl.VhclUnloadingEtaDate,
                VhclUnloadingEtaTime = vhcl.VhclUnloadingEtaTime,
                VhclCompany = vhcl.VhclCompany,
                VhclAssigneeName = vhcl.VhclAssigneeName,
                VhclSupervisorName = vhcl.VhclSupervisorName,
                Status = string.Empty,
                VhclUnloadingActivationDate = vhcl.VhclUnloadingActivationDate,
                VhclUnloadingActivationTime = vhcl.VhclUnloadingActivationTime,
                VhclUnloadingArrivalDate = vhcl.VhclUnloadingArrivalDate,
                VhclUnloadingArrivalTime = vhcl.VhclUnloadingArrivalTime,
                VhclVehicleComplete = vhcl.VhclVehicleComplete,
                VhclAssigneeId = vhcl.VhclAssigneeId,
                VhclProviderCustomerIsn = vhcl.VhclProviderCustomerIsn,
                VhclSupervisorId = vhcl.VhclSupervisorId,
                TotalCount = query.Count()
            });

            var datas = await dataList.OrderByDescending(x => x.VhclUnloadingEtaDate).ThenByDescending(x => x.VhclUnloadingEtaTime)
                                      .PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);

            foreach (var item in datas)
            {
                if (item.VhclUnloadingEtaDate != null && !item.VhclUnloadingEtaTime.Equals("0"))
                {
                    if (item.VhclUnloadingActivationDate != null && !item.VhclUnloadingActivationTime.Equals("0"))
                    {
                        if (item.VhclUnloadingArrivalDate != null && (item.VhclUnloadingArrivalTime != null || !item.VhclUnloadingArrivalTime.Equals("0")))
                        {
                            if (item.VhclVehicleComplete.HasValue && item.VhclVehicleComplete == true) item.Status = "Completed";

                            if (item.VhclVehicleComplete.HasValue && item.VhclVehicleComplete == false) item.Status = "Receiving";
                        }
                        else
                        {
                            item.Status = "Approved";
                        }
                    }
                    else
                    {
                        item.Status = "Pending";
                    }
                }
            }

            return datas;
        }

        public async Task<List<ReceivingPlan>> GetListReceiveShipmentAsync(
            string keyword = null,
            long? Id = null,
            long? customerId = null,
            string assigneeId = null,
            string etaDate = null,
            string status = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(etaDate))
            {
                if (DateTime.TryParseExact(etaDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startOfDay = date.Date;

                    endOfDay = date.Date.AddDays(1).AddTicks(-1);
                }
            }

            var query = from vhcl in (await GetDbSetAsync()).Where(x => x.VhclUnloadingEtaDate != null)
                                .WhereIf(!string.IsNullOrWhiteSpace(etaDate), x => x.VhclUnloadingEtaDate >= startOfDay && x.VhclUnloadingEtaDate <= endOfDay)
                                .WhereIf(!string.IsNullOrWhiteSpace(assigneeId), x => x.VhclAssigneeId.Equals(assigneeId))
                                .WhereIf(customerId.HasValue, x => x.VhclProviderCustomerIsn == customerId)
                                .WhereIf(!string.IsNullOrWhiteSpace(keyword), x => x.VhclCompany.ToUpper().Contains(keyword.ToUpper()) || x.VhclAssigneeName.ToUpper().Contains(keyword.ToUpper()) || x.Id.ToString() == keyword)
                                .WhereIf(Id.HasValue, x => x.Id == Id)
                                .WhereIf(string.IsNullOrWhiteSpace(status), x => (x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate != null && (x.VhclUnloadingArrivalTime != null || !x.VhclUnloadingArrivalTime.Equals("0"))
                                                                            && x.VhclVehicleComplete == true) || (x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate != null && (x.VhclUnloadingArrivalTime != null || !x.VhclUnloadingArrivalTime.Equals("0"))
                                                                            && x.VhclVehicleComplete == false) || (x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate == null && (x.VhclUnloadingArrivalTime == null || x.VhclUnloadingArrivalTime.Equals("0"))
                                                                            ))
                                .WhereIf(!string.IsNullOrWhiteSpace(status) && status.Equals("Completed"),
                                                                          x => x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate != null && (x.VhclUnloadingArrivalTime != null || !x.VhclUnloadingArrivalTime.Equals("0"))
                                                                            && x.VhclVehicleComplete == true)
                                .WhereIf(!string.IsNullOrWhiteSpace(status) && status.Equals("Receiving"),
                                                                          x => x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate != null && (x.VhclUnloadingArrivalTime != null || !x.VhclUnloadingArrivalTime.Equals("0"))
                                                                            && x.VhclVehicleComplete == false)
                                .WhereIf(!string.IsNullOrWhiteSpace(status) && status.Equals("Approved"),
                                                                          x => x.VhclUnloadingEtaDate != null && !x.VhclUnloadingEtaTime.Equals("0")
                                                                            && x.VhclUnloadingActivationDate != null && !x.VhclUnloadingActivationTime.Equals("0")
                                                                            && x.VhclUnloadingArrivalDate == null && (x.VhclUnloadingArrivalTime == null || x.VhclUnloadingArrivalTime.Equals("0")))

                        select new ReceivingPlan
                        {
                            Id = vhcl.Id,
                            VhclUnloadingEtaDate = vhcl.VhclUnloadingEtaDate,
                            VhclUnloadingEtaTime = vhcl.VhclUnloadingEtaTime,
                            VhclCompany = vhcl.VhclCompany,
                            VhclAssigneeName = vhcl.VhclAssigneeName,
                            VhclSupervisorName = vhcl.VhclSupervisorName,
                            Status = string.Empty,
                            VhclUnloadingActivationDate = vhcl.VhclUnloadingActivationDate,
                            VhclUnloadingActivationTime = vhcl.VhclUnloadingActivationTime,
                            VhclUnloadingArrivalDate = vhcl.VhclUnloadingArrivalDate,
                            VhclUnloadingArrivalTime = vhcl.VhclUnloadingArrivalTime,
                            VhclVehicleComplete = vhcl.VhclVehicleComplete,
                            VhclAssigneeId = vhcl.VhclAssigneeId,
                            VhclProviderCustomerIsn = vhcl.VhclProviderCustomerIsn,
                            VhclSupervisorId = vhcl.VhclSupervisorId
                        };

            var dataList = query.Select(vhcl => new ReceivingPlan
            {
                Id = vhcl.Id,
                VhclUnloadingEtaDate = vhcl.VhclUnloadingEtaDate,
                VhclUnloadingEtaTime = vhcl.VhclUnloadingEtaTime,
                VhclCompany = vhcl.VhclCompany,
                VhclAssigneeName = vhcl.VhclAssigneeName,
                VhclSupervisorName = vhcl.VhclSupervisorName,
                Status = string.Empty,
                VhclUnloadingActivationDate = vhcl.VhclUnloadingActivationDate,
                VhclUnloadingActivationTime = vhcl.VhclUnloadingActivationTime,
                VhclUnloadingArrivalDate = vhcl.VhclUnloadingArrivalDate,
                VhclUnloadingArrivalTime = vhcl.VhclUnloadingArrivalTime,
                VhclVehicleComplete = vhcl.VhclVehicleComplete,
                VhclAssigneeId = vhcl.VhclAssigneeId,
                VhclProviderCustomerIsn = vhcl.VhclProviderCustomerIsn,
                VhclSupervisorId = vhcl.VhclSupervisorId,
                TotalCount = query.Count()
            });

            var datas = await dataList.OrderByDescending(x => x.VhclUnloadingEtaDate).ThenByDescending(x => x.VhclUnloadingEtaTime)
                                      .PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);

            foreach (var item in datas)
            {
                if (item.VhclUnloadingEtaDate != null && !item.VhclUnloadingEtaTime.Equals("0"))
                {
                    if (item.VhclUnloadingActivationDate != null && !item.VhclUnloadingActivationTime.Equals("0"))
                    {
                        if (item.VhclUnloadingArrivalDate != null && (item.VhclUnloadingArrivalTime != null || !item.VhclUnloadingArrivalTime.Equals("0")))
                        {
                            if (item.VhclVehicleComplete.HasValue && item.VhclVehicleComplete == true) item.Status = "Completed";

                            if (item.VhclVehicleComplete.HasValue && item.VhclVehicleComplete == false) item.Status = "Receiving";
                        }
                        else
                        {
                            item.Status = "Approved";
                        }
                    }
                    else
                    {
                        item.Status = "Pending";
                    }
                }
            }

            return datas;
        }

        public async Task<NotificationRegistration> GetVehicleRegNotification(long vehicleRegId = 0, string wareHouseName = "")
        {
            NotificationRegistration vehiclesRegistrationStatus = new();
            VehiclesRegistration VehicleReg = (from vr in (await GetDbSetAsync()) where vr.Id == vehicleRegId select vr).FirstOrDefault();
            if (VehicleReg != null)
            {
                //ErtsRemoteTransitShed wareHouse = (from r in (await GetDbContextAsync()).SertsRemoteTransitSheds where r.Id.ToString() == VehicleReg.VhclUnloadingWarehouse select r).FirstOrDefault();
                VhslVehicleSealInfo sealInfo = (from r in (await GetDbContextAsync()).VhslVehicleSealInfos where r.VhslVehicleRegIsn == vehicleRegId select r).FirstOrDefault();
                //string wareHouseName = "";
                string sealNumber = "";
                //if (wareHouse != null)
                //{
                //    wareHouseName = "Ga " + wareHouse.ErtsShedCode + ", ";
                //}
                if (sealInfo != null)
                {
                    if (!string.IsNullOrEmpty(sealInfo.VhslSealNumber))
                    {
                        sealNumber = sealInfo.VhslSealNumber + ", ";
                    }
                }
                var dt = (from r in (await GetDbContextAsync()).VhldVehicleDetail where r.VhldVehicleisn == vehicleRegId && r.VhldQtyreceived != 0 select r).ToList();
                string sum = "";
                if (dt != null)
                {
                    sum += "Mawb " + dt.GroupBy(g => (g.VhldAwbprefix + g.VhldAwbserial.ToString())).Select(g => g.First()).Count() + ", ";
                    sum += "Pcs " + dt.Sum(x => x.VhldQtyreceived).ToString() + ", ";
                    sum += "Weight " + dt.Sum(x => x.VhldWeightReceived).ToString() + " Kg, ";
                }
                vehiclesRegistrationStatus.Extra = VehicleReg.VhclReleasedBy;
                vehiclesRegistrationStatus.Title = VehicleReg.VehicRegNo + " - " + ((DateTime)VehicleReg.VhclUnloadingEtaDate).ToString("dd/MM/yyyy HH:mm");
                vehiclesRegistrationStatus.Body = "Lái xe " + VehicleReg.VhclDriverName + ", " + wareHouseName + sum + sealNumber + (string.IsNullOrEmpty(VehicleReg.VhclRemarks) ? "" : VehicleReg.VhclRemarks);
            }

            return vehiclesRegistrationStatus;
        }

        public async Task<List<TruckLoadingInfo>> GetListTruckLoadingAsync(
            string truckNo = null,
            string wareHouse = null,
            string unloadingWarehouse = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string status = null,
            List<string> lstCargoTerminal = null,
            string keyword = null)
        {
            var query = (from truck in (await GetDbSetAsync()).Where(x => x.VhclImportExport == "EXPORT" && x.VhclTruckType == "TRANSIT")
                            .WhereIf(!string.IsNullOrWhiteSpace(truckNo), x => x.VehicRegNo.Contains(truckNo))
                            .WhereIf(startDate.HasValue, x => x.VhclLoadingArrivalDate >= startDate)
                            .WhereIf(endDate.HasValue, x => x.VhclLoadingArrivalDate <= endDate)
                            .WhereIf(!string.IsNullOrWhiteSpace(unloadingWarehouse), e => e.VhclUnloadingWarehouse.Contains(unloadingWarehouse))
                            .WhereIf(lstCargoTerminal?.Count > 0, e => lstCargoTerminal.Contains(e.VhclUnloadingWarehouse))
                            .WhereIf(!string.IsNullOrWhiteSpace(keyword), x => x.VehicRegNo.Contains(keyword))
                         join warehouse in (await GetDbContextAsync()).WareHouses
                            .WhereIf(!string.IsNullOrWhiteSpace(wareHouse), y => y.WarehouseName.Contains(wareHouse))
                         on truck.VhclLoadingWarehouse equals warehouse.Id.ToString()
                         join vhld in (await GetDbContextAsync()).VhldVehicleDetail.Where(x => x.VhldDeleted == false)
                             on truck.Id equals vhld.VhldVehicleisn into vhld
                         from truckDetail in vhld.DefaultIfEmpty()

                         // lấy cân kiện ở mawb
                         join pallet in (await GetDbContextAsync()).DoDnnPalletMgrs
                             on truckDetail.VhldGroupIsn equals pallet.PltGroupPalletNo into ljpallet
                         from pallet in ljpallet.DefaultIfEmpty()
                         join dopo in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                             on pallet.PltDoNo equals dopo.DomgrDono into ljdopo
                         from dopo in ljdopo.DefaultIfEmpty()
                         join lab in (await GetDbContextAsync()).Labs.Where(x => x.LabsDeleted == false)
                             on dopo.MawbId equals lab.Id into ljlab
                         from lab in ljlab.DefaultIfEmpty()

                         orderby truck.VhclLoadingArrivalDate descending
                         select new TruckLoadingInfo
                         {
                             Id = truck.Id,
                             TruckNo = truck.VehicRegNo,
                             UnloadingWarehouse = truck.VhclUnloadingWarehouse,
                             LoadingVehicleClosed = truck.VhclLoadingVehicleClosed,
                             LoadingLeftDate = truck.VhclLoadingLeftDate,
                             LoadingArrivalDate = truck.VhclLoadingArrivalDate,
                             WarehouseName = warehouse.WarehouseName,
                             Prefix = truckDetail.VhldAwbprefix,
                             Serial = truckDetail.VhldAwbserial,
                             Objectisn = truckDetail.VhldGroupIsn,
                             Pcs = truckDetail.VhldQtyexpected,
                             GW = truckDetail.VhldWeightExpected,
                             LabsId = lab.Id,
                             PcsMawb = lab.LabsQuantityDel,
                             GWMawb = lab.LabsWeightDel,
                             VehicleClosedDate= truck.VhclLoadingVehicleClosedDate,
                             VehicleClosedTime= truck.VhclLoadingVehicleClosedTime,
                         }).ToList();
            return (from m in query
                    group m by m.Id into g
                    select new TruckLoadingInfo
                    {
                        Id = g.Key,
                        TruckNo = g.FirstOrDefault().TruckNo,
                        TotalMawb = g.Where(x => !string.IsNullOrWhiteSpace(x.Prefix)).Select(x => new { x.Prefix, x.Serial }).Distinct().Count(),
                        TotalPcs = g.Select(x => new { x.Objectisn, x.Pcs }).Distinct().Sum(x => x.Pcs ?? 0),
                        //TotalGW = g.Select(x => new { x.Objectisn, x.GW }).Distinct().Sum(x => x.GW ?? 0),
                        TotalGW = g.Where(x => x.GWMawb.HasValue).Select(x => new { x.LabsId, x.GWMawb }).Distinct().Sum(x => x.GWMawb ?? 0),
                        WarehouseName = g.FirstOrDefault().WarehouseName,
                        UnloadingWarehouse = g.FirstOrDefault().UnloadingWarehouse,
                        LoadingLeftDate = g.FirstOrDefault().LoadingLeftDate,
                        LoadingVehicleClosed = g.FirstOrDefault().LoadingVehicleClosed,
                        LoadingArrivalDate = g.FirstOrDefault().LoadingArrivalDate,
                        VehicleClosedDate= g.FirstOrDefault().VehicleClosedDate,
                        VehicleClosedTime= g.FirstOrDefault().VehicleClosedTime
                    })
                    .WhereIf("Ready to load".Equals(status), x => x.TotalPcs == 0 && !x.LoadingVehicleClosed)
                    .WhereIf("Loading".Equals(status), x => x.TotalPcs > 0 && !x.LoadingVehicleClosed && x.LoadingLeftDate == null)
                    .WhereIf("Closed".Equals(status), x => x.TotalPcs > 0 && x.LoadingVehicleClosed && x.LoadingLeftDate == null)
                    .WhereIf("In Transit".Equals(status), x => x.TotalPcs > 0 && x.LoadingVehicleClosed && x.LoadingLeftDate != null)
                    .ToList();
        }

        public async Task<List<ListCheckDoc>> GetListTruckForCheckDocAsync(
            string truckNo = null,
            string wareHouse = null,
            string unloadingWarehouse = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            DateTime? closeStartDate = null,
            DateTime? closeEndDate = null,
            DateTime? transitStartDate = null,
            DateTime? transitEndDate = null,
            DateTime? arrivedStartDate = null,
            DateTime? arrivedEndDate = null,
            DateTime? completedStartDate = null,
            DateTime? completedEndDate = null,
            string status = null,
            List<string> lstCargoTerminal = null)
        {
            var query = (from truck in (await GetDbSetAsync()).Where(x => x.VhclImportExport == "EXPORT" && x.VhclTruckType == "TRANSIT")
                            .WhereIf(!string.IsNullOrWhiteSpace(truckNo), x => x.VehicRegNo.Contains(truckNo))
                            .WhereIf(startDate.HasValue && endDate.HasValue, x => x.VhclLoadingArrivalDate >= startDate && x.VhclLoadingArrivalDate <= endDate)
                            .WhereIf(closeStartDate.HasValue && closeEndDate.HasValue, x => x.VhclLoadingVehicleClosedDate >= closeStartDate && x.VhclLoadingVehicleClosedDate <= closeEndDate)
                            .WhereIf(transitStartDate.HasValue && transitEndDate.HasValue, x => x.VhclLoadingLeftDate >= transitStartDate && x.VhclLoadingLeftDate <= transitEndDate)
                            .WhereIf(arrivedStartDate.HasValue && arrivedEndDate.HasValue, x => x.VhclUnloadingArrivalDate >= arrivedStartDate && x.VhclUnloadingArrivalDate <= arrivedEndDate)
                            .WhereIf(completedStartDate.HasValue && completedEndDate.HasValue, x => x.VhclCompletedDate >= completedStartDate && x.VhclCompletedDate <= completedEndDate)
                            .WhereIf(!string.IsNullOrWhiteSpace(unloadingWarehouse), e => e.VhclUnloadingWarehouse.Contains(unloadingWarehouse))
                            .WhereIf(lstCargoTerminal?.Count > 0, e => lstCargoTerminal.Contains(e.VhclUnloadingWarehouse))
                         join warehouse in (await GetDbContextAsync()).WareHouses
                            .WhereIf(!string.IsNullOrWhiteSpace(wareHouse), y => y.WarehouseName.Contains(wareHouse))
                         on truck.VhclLoadingWarehouse equals warehouse.Id.ToString()
                         join vhld in (await GetDbContextAsync()).VhldVehicleDetail.Where(x => x.VhldDeleted == false)
                             on truck.Id equals vhld.VhldVehicleisn into ljvhld
                         from vhld in ljvhld.DefaultIfEmpty()

                             // không get ở vhld mà get ở dodnn
                         join pallet in (await GetDbContextAsync()).DoDnnPalletMgrs
                             on vhld.VhldGroupIsn equals pallet.PltGroupPalletNo into ljpallet
                         from pallet in ljpallet.DefaultIfEmpty()
                         join dopo in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                             on pallet.PltDoNo equals dopo.DomgrDono into ljdopo
                         from dopo in ljdopo.DefaultIfEmpty()
                         join lab in (await GetDbContextAsync()).Labs.Where(x => x.LabsDeleted == false)
                             on dopo.MawbId equals lab.Id into ljlab
                         from lab in ljlab.DefaultIfEmpty()

                         orderby truck.VhclLoadingArrivalDate descending
                         select new ListCheckDoc
                         {
                             Id = truck.Id,
                             EcusId = truck.VhclMessageIsn,
                             VehicleId = truck.VhclMasterIsn,
                             TruckNo = truck.VehicRegNo,
                             UnloadingWarehouse = truck.VhclUnloadingWarehouse,
                             LoadingVehicleClosed = truck.VhclLoadingVehicleClosed,
                             LoadingLeftDate = truck.VhclLoadingLeftDate,
                             LoadingArrivalDate = truck.VhclLoadingArrivalDate,
                             WarehouseName = warehouse.WarehouseName,
                             Objectisn = vhld.VhldGroupIsn,
                             /*Prefix = vhld.VhldAwbprefix,
                             Serial = vhld.VhldAwbserial,
                             Pcs = vhld.VhldQtyreceived,
                             GW = vhld.VhldWeightReceived,*/
                             LabsId = lab.Id,
                             Prefix = lab.LabsMawbPrefix,
                             Serial = lab.LabsMawbSerialNo,
                             Pcs = pallet.PltReceivedPieces ?? 0,
                             GW = pallet.PltReceivedWeight ?? 0,
                             PcsMawb = lab.LabsQuantityDel,
                             GWMawb = lab.LabsWeightDel,
                             VhclLoadingVehicleClosedDate = truck.VhclLoadingVehicleClosedDate,
                             VhclLoadingVehicleClosedTime = truck.VhclLoadingVehicleClosedTime,
                             VhclLoadingLeftDate = truck.VhclLoadingLeftDate,
                             VhclLoadingLeftTime = truck.VhclLoadingLeftTime,
                             VhclCompletedDate = truck.VhclCompletedDate,
                             VhclCompletedTime = truck.VhclCompletedTime,
                             VhclUnloadingArrivalDate = truck.VhclUnloadingArrivalDate,
                             VhclUnloadingArrivalTime = truck.VhclUnloadingArrivalTime,
                             VhclUnloadingActivationDate = truck.VhclUnloadingActivationDate,
                             VhclUnloadingActivationTime = truck.VhclUnloadingActivationTime,
                             VhclVehicleComplete = truck.VhclVehicleComplete,
                         }).ToList();
            return (from m in query
                    group m by m.Id into g
                    select new ListCheckDoc
                    {
                        Id = g.Key,
                        EcusId = g.FirstOrDefault().EcusId,
                        VehicleId = g.FirstOrDefault().VehicleId,
                        TruckNo = g.FirstOrDefault().TruckNo,
                        TotalMawb = g.Where(x => !string.IsNullOrWhiteSpace(x.Prefix)).Select(x => new { x.Prefix, x.Serial }).Distinct().Count(),
                        TotalPcs = g.Where(x => x.Pcs.HasValue).Select(x => new { x.Objectisn, x.Pcs }).Distinct().Sum(x => x.Pcs ?? 0),
                        TotalGW = g.Where(x => x.GWMawb.HasValue).Select(x => new { x.LabsId, x.GWMawb }).Distinct().Sum(x => x.GWMawb ?? 0),
                        WarehouseName = g.FirstOrDefault().WarehouseName,
                        UnloadingWarehouse = g.FirstOrDefault().UnloadingWarehouse,
                        LoadingLeftDate = g.FirstOrDefault().LoadingLeftDate,
                        LoadingVehicleClosed = g.FirstOrDefault().LoadingVehicleClosed,
                        LoadingArrivalDate = g.FirstOrDefault().LoadingArrivalDate,
                        VhclLoadingVehicleClosedDate = g.FirstOrDefault().VhclLoadingVehicleClosedDate,
                        VhclLoadingVehicleClosedTime = g.FirstOrDefault().VhclLoadingVehicleClosedTime,
                        VhclLoadingLeftDate = g.FirstOrDefault().VhclLoadingLeftDate,
                        VhclLoadingLeftTime = g.FirstOrDefault().VhclLoadingLeftTime,
                        VhclCompletedDate = g.FirstOrDefault().VhclCompletedDate,
                        VhclCompletedTime = g.FirstOrDefault().VhclCompletedTime,
                        VhclUnloadingArrivalDate = g.FirstOrDefault().VhclUnloadingArrivalDate,
                        VhclUnloadingArrivalTime = g.FirstOrDefault().VhclUnloadingArrivalTime,
                        VhclUnloadingActivationDate = g.FirstOrDefault().VhclUnloadingActivationDate,
                        VhclUnloadingActivationTime = g.FirstOrDefault().VhclUnloadingActivationTime,
                        VhclVehicleComplete = g.FirstOrDefault().VhclVehicleComplete,
                    })
                    .WhereIf("Ready to load".Equals(status), x => x.TotalPcs == 0 && !x.LoadingVehicleClosed)
                    .WhereIf("Loading".Equals(status), x => x.TotalPcs > 0 && !x.LoadingVehicleClosed && x.LoadingLeftDate == null)
                    .WhereIf("Closed".Equals(status), x => x.TotalPcs > 0 && x.LoadingVehicleClosed && x.LoadingLeftDate == null)
                    .WhereIf("In Transit".Equals(status), x => x.TotalPcs > 0 && x.LoadingLeftDate != null && x.VhclUnloadingArrivalDate == null && x.VhclUnloadingActivationDate == null && (!x.VhclVehicleComplete.HasValue || x.VhclVehicleComplete == false))
                    .WhereIf("Arrived Terminal".Equals(status), x => x.TotalPcs > 0 && x.VhclUnloadingArrivalDate != null && x.VhclUnloadingActivationDate == null && (!x.VhclVehicleComplete.HasValue || x.VhclVehicleComplete == false))
                    .WhereIf("Unloading".Equals(status), x => x.TotalPcs > 0 && x.VhclUnloadingActivationDate != null && (!x.VhclVehicleComplete.HasValue || x.VhclVehicleComplete == false))
                    .WhereIf("Completed".Equals(status), x => x.TotalPcs > 0 && x.VhclVehicleComplete == true)
                    .OrderByDescending(x => x.VhclLoadingLeftDate == null)
                    .ThenByDescending(x => x.LoadingLeftDate)
                    .ToList();
        }

        public async Task<List<TruckSeaInfo>> GetListTruckSeaAsync(string truckNo = null, DateTime? startDate = null, string status = null, long truckId = 0)
        {
            var query = (from truck in (await GetDbSetAsync()).Where(x => x.VhclTruckType == "SEA")
                            .WhereIf(truckId != 0, x => x.VhclMasterIsn == truckId)
                            .WhereIf(!string.IsNullOrWhiteSpace(truckNo), x => x.VehicRegNo.Contains(truckNo))
                            .WhereIf(startDate.HasValue, x => x.VhclLoadingArrivalDate.Value.Year == startDate.Value.Year && x.VhclLoadingArrivalDate.Value.Month == startDate.Value.Month
                                && x.VhclLoadingArrivalDate.Value.Day == startDate.Value.Day)
                         orderby truck.VhclLoadingArrivalDate descending
                         select new TruckSeaInfo
                         {
                             Id = truck.Id,
                             MasterIsn = truck.VhclMasterIsn,
                             TruckNo = truck.VehicRegNo,
                             VhclDriverId = truck.VhclDriverId,
                             VhclDriverName = truck.VhclDriverName,
                             VhclAssigneeId = truck.VhclAssigneeId,
                             VhclAssigneeName = truck.VhclAssigneeName,
                             VhclSupervisorId = truck.VhclSupervisorId,
                             VhclSupervisorName = truck.VhclSupervisorName,
                             VhclSupervisor = truck.VhclSupervisor,
                             VhclReleasedBy = truck.VhclReleasedBy,
                             VhclLoadingArrivalDate = truck.VhclLoadingArrivalDate,
                             VhclLoadingArrivalTime = truck.VhclLoadingArrivalTime,
                             VhclLoadingAtDoorDate = truck.VhclLoadingAtDoorDate,
                             VhclLoadingAtDoorTime = truck.VhclLoadingAtDoorTime,
                             VhclLoadingVehicleClosedDate = truck.VhclLoadingVehicleClosedDate,
                             VhclLoadingVehicleClosedTime = truck.VhclLoadingVehicleClosedTime,
                             VhclLoadingVehicleClosed = truck.VhclLoadingVehicleClosed,
                             VhclLoadingLeftDate = truck.VhclLoadingLeftDate,
                             VhclLoadingLeftTime = truck.VhclLoadingLeftTime,
                             VhclCompletedDate = truck.VhclCompletedDate,
                             VhclCompletedTime = truck.VhclCompletedTime,
                             VhclLoadingDoor = truck.VhclLoadingDoor,
                             VhclRemarks = truck.VhclRemarks
                         }).ToList();
            return query;
        }

        public async Task<List<GetMessageByTruckId>> GetListMessageByTruckIdAsync(long truckId = 0)
        {
            var query = (from vhcl in (await GetDbSetAsync()).Where(x => x.Id == truckId)
                         join vhld in (await GetDbContextAsync()).VhldVehicleDetail.Where(x => x.VhldDeleted == false) on vhcl.Id equals vhld.VhldVehicleisn
                         join labs in (await GetDbContextAsync()).Labs on vhld.VhldObjectisn equals labs.Id
                         select new GetMessageByTruckId
                         {
                             LabsId = vhld.VhldObjectisn,
                             Mawb = vhld.VhldAwbprefix + "-" + vhld.VhldAwbserial,
                             LabsFwdCapture = labs.LabsFwbcapture,
                             LabsDateFwdCaptured = labs.LabsDatefwbcaptured
                         }).Distinct().ToList();

            var queryMessage = (
                            from root in query
                            join mess in (await GetDbContextAsync()).Messages.Where(x => x.MessMessageId.HasValue) on root.MessMessageId equals mess.MessMessageId
                            select new GetMessageByTruckId
                            {
                                LabsId = root.LabsId,
                                Mawb = root.Mawb,
                                LabsFwdCapture = root.LabsFwdCapture,
                                LabsDateFwdCaptured = root.LabsDateFwdCaptured,

                                MessageId = mess.Id,
                                Message = mess.MessTemplate,
                                Ucr = mess.MessBeginofmessage,
                                Type = mess.MessMessageType,
                                MessageDateime = mess.MessMessageDatetime
                            }
                         );

            return queryMessage.ToList();
        }

        public async Task<List<TruckSeaInfo>> GetListTruckSeaForReportAsync(DateTime? startDate = null,DateTime? endDate = null, long? truckId = 0)
        {
            var query = (from truck in (await GetDbSetAsync()).Where(x => x.VhclTruckType == "SEA")
                            .WhereIf(truckId != 0, x => x.VhclMasterIsn == truckId)
                            //.WhereIf(!string.IsNullOrWhiteSpace(truckNo), x => x.VehicRegNo.Contains(truckNo))
                            .WhereIf(startDate.HasValue && endDate.HasValue, x => x.VhclLoadingLeftDate >= startDate && x.VhclLoadingLeftDate <= endDate)
                         orderby truck.VhclLoadingLeftDate descending
                         select new TruckSeaInfo
                         {
                             Id = truck.Id,
                             MasterIsn = truck.VhclMasterIsn,
                             TruckNo = truck.VehicRegNo,
                             VhclDriverId = truck.VhclDriverId,
                             VhclDriverName = truck.VhclDriverName,
                             VhclAssigneeId = truck.VhclAssigneeId,
                             VhclAssigneeName = truck.VhclAssigneeName,
                             VhclSupervisorId = truck.VhclSupervisorId,
                             VhclSupervisorName = truck.VhclSupervisorName,
                             VhclSupervisor = truck.VhclSupervisor,
                             VhclReleasedBy = truck.VhclReleasedBy,
                             VhclLoadingArrivalDate = truck.VhclLoadingArrivalDate,
                             VhclLoadingArrivalTime = truck.VhclLoadingArrivalTime,
                             VhclLoadingAtDoorDate = truck.VhclLoadingAtDoorDate,
                             VhclLoadingAtDoorTime = truck.VhclLoadingAtDoorTime,
                             VhclLoadingVehicleClosedDate = truck.VhclLoadingVehicleClosedDate,
                             VhclLoadingVehicleClosedTime = truck.VhclLoadingVehicleClosedTime,
                             VhclLoadingVehicleClosed = truck.VhclLoadingVehicleClosed,
                             VhclLoadingLeftDate = truck.VhclLoadingLeftDate,
                             VhclLoadingLeftTime = truck.VhclLoadingLeftTime,
                             VhclCompletedDate = truck.VhclCompletedDate,
                             VhclCompletedTime = truck.VhclCompletedTime,
                             VhclLoadingDoor = truck.VhclLoadingDoor,
                             VhclRemarks = truck.VhclRemarks
                         }).ToList();
            return query;
        }

        public async Task<List<ListCheckDoc>> GetListTruckForCheckDocForReport1Async(
            DateTime? transitStartDate = null,
            DateTime? transitEndDate = null)
        {
            var query = (from truck in (await GetDbSetAsync()).Where(x => x.VhclImportExport == "EXPORT" && x.VhclTruckType == "TRANSIT")
                         .WhereIf(transitStartDate.HasValue && transitEndDate.HasValue, x => x.VhclLoadingLeftDate >= transitStartDate
                            && x.VhclLoadingLeftDate <= transitEndDate)
                         join warehouse in (await GetDbContextAsync()).WareHouses
                         on truck.VhclLoadingWarehouse equals warehouse.Id.ToString()
                         join vhld in (await GetDbContextAsync()).VhldVehicleDetail.Where(x => x.VhldDeleted == false)
                             on truck.Id equals vhld.VhldVehicleisn into ljvhld
                         from vhld in ljvhld.DefaultIfEmpty()

                             // không get ở vhld mà get ở dodnn
                         join pallet in (await GetDbContextAsync()).DoDnnPalletMgrs
                             on vhld.VhldGroupIsn equals pallet.PltGroupPalletNo into ljpallet
                         from pallet in ljpallet.DefaultIfEmpty()
                         join dopo in (await GetDbContextAsync()).DoPoManages.Where(x => x.DoMgrDeleted == false)
                             on pallet.PltDoNo equals dopo.DomgrDono into ljdopo
                         from dopo in ljdopo.DefaultIfEmpty()
                         join lab in (await GetDbContextAsync()).Labs.Where(x => x.LabsDeleted == false)
                             on dopo.MawbId equals lab.Id into ljlab
                         from lab in ljlab.DefaultIfEmpty()

                         orderby truck.VhclLoadingArrivalDate descending
                         select new ListCheckDoc
                         {
                             Id = truck.Id,
                             EcusId = truck.VhclMessageIsn,
                             VehicleId = truck.VhclMasterIsn,
                             TruckNo = truck.VehicRegNo,
                             UnloadingWarehouse = truck.VhclUnloadingWarehouse,
                             LoadingVehicleClosed = truck.VhclLoadingVehicleClosed,
                             LoadingLeftDate = truck.VhclLoadingLeftDate,
                             LoadingArrivalDate = truck.VhclLoadingArrivalDate,
                             WarehouseName = warehouse.WarehouseName,
                             Objectisn = vhld.VhldGroupIsn,
                             /*Prefix = vhld.VhldAwbprefix,
                             Serial = vhld.VhldAwbserial,
                             Pcs = vhld.VhldQtyreceived,
                             GW = vhld.VhldWeightReceived,*/
                             LabsId = lab.Id,
                             Prefix = lab.LabsMawbPrefix,
                             Serial = lab.LabsMawbSerialNo,
                             Pcs = pallet.PltReceivedPieces ?? 0,
                             GW = pallet.PltReceivedWeight ?? 0,
                             PcsMawb = lab.LabsQuantityDel,
                             GWMawb = lab.LabsWeightDel,
                             VhclLoadingVehicleClosedDate = truck.VhclLoadingVehicleClosedDate,
                             VhclLoadingVehicleClosedTime = truck.VhclLoadingVehicleClosedTime,
                             VhclLoadingLeftDate = truck.VhclLoadingLeftDate,
                             VhclLoadingLeftTime = truck.VhclLoadingLeftTime,
                             VhclCompletedDate = truck.VhclCompletedDate,
                             VhclCompletedTime = truck.VhclCompletedTime,
                             VhclUnloadingArrivalDate = truck.VhclUnloadingArrivalDate,
                             VhclUnloadingArrivalTime = truck.VhclUnloadingArrivalTime,
                             VhclUnloadingActivationDate = truck.VhclUnloadingActivationDate,
                             VhclUnloadingActivationTime = truck.VhclUnloadingActivationTime,
                             VhclVehicleComplete = truck.VhclVehicleComplete,
                         }).ToList();
            return (from m in query
                    group m by m.Id into g
                    select new ListCheckDoc
                    {
                        Id = g.Key,
                        EcusId = g.FirstOrDefault().EcusId,
                        VehicleId = g.FirstOrDefault().VehicleId,
                        TruckNo = g.FirstOrDefault().TruckNo,
                        TotalMawb = g.Where(x => !string.IsNullOrWhiteSpace(x.Prefix)).Select(x => new { x.Prefix, x.Serial }).Distinct().Count(),
                        TotalPcs = g.Where(x => x.Pcs.HasValue).Select(x => new { x.Objectisn, x.Pcs }).Distinct().Sum(x => x.Pcs ?? 0),
                        TotalGW = g.Where(x => x.GWMawb.HasValue).Select(x => new { x.LabsId, x.GWMawb }).Distinct().Sum(x => x.GWMawb ?? 0),
                        WarehouseName = g.FirstOrDefault().WarehouseName,
                        UnloadingWarehouse = g.FirstOrDefault().UnloadingWarehouse,
                        LoadingLeftDate = g.FirstOrDefault().LoadingLeftDate,
                        LoadingVehicleClosed = g.FirstOrDefault().LoadingVehicleClosed,
                        LoadingArrivalDate = g.FirstOrDefault().LoadingArrivalDate,
                        VhclLoadingVehicleClosedDate = g.FirstOrDefault().VhclLoadingVehicleClosedDate,
                        VhclLoadingVehicleClosedTime = g.FirstOrDefault().VhclLoadingVehicleClosedTime,
                        VhclLoadingLeftDate = g.FirstOrDefault().VhclLoadingLeftDate,
                        VhclLoadingLeftTime = g.FirstOrDefault().VhclLoadingLeftTime,
                        VhclCompletedDate = g.FirstOrDefault().VhclCompletedDate,
                        VhclCompletedTime = g.FirstOrDefault().VhclCompletedTime,
                        VhclUnloadingArrivalDate = g.FirstOrDefault().VhclUnloadingArrivalDate,
                        VhclUnloadingArrivalTime = g.FirstOrDefault().VhclUnloadingArrivalTime,
                        VhclUnloadingActivationDate = g.FirstOrDefault().VhclUnloadingActivationDate,
                        VhclUnloadingActivationTime = g.FirstOrDefault().VhclUnloadingActivationTime,
                        VhclVehicleComplete = g.FirstOrDefault().VhclVehicleComplete,
                    })
                    .OrderByDescending(x => x.VhclLoadingLeftDate == null)
                    .ThenByDescending(x => x.LoadingLeftDate)
                    .ToList();
        }
    }
}