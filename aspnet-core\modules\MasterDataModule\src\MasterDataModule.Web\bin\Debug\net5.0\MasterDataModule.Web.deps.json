{"runtimeTarget": {"name": ".NETCoreApp,Version=v5.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET5_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "latest", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": false, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v5.0": {"MasterDataModule.Web/0.1.0": {"dependencies": {"ConfigureAwait.Fody": "3.3.1", "Fody": "6.5.0", "MasterDataModule.Application": "0.1.0", "MasterDataModule.HttpApi": "0.1.0", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": "4.3.0", "Volo.Abp.AutoMapper": "4.3.0", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core.Reference": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization.Policy.Reference": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions": "*******", "Microsoft.AspNetCore.DataProtection": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http.Reference": "*******", "Microsoft.AspNetCore.Http.Extensions.Reference": "*******", "Microsoft.AspNetCore.Http.Features.Reference": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Mvc.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core.Reference": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json.Reference": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions.Reference": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Routing.Reference": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities.Reference": "*******", "Microsoft.CSharp.Reference": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine.Reference": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference": "*******", "Microsoft.Extensions.Configuration.FileExtensions.Reference": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json.Reference": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets.Reference": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite.Reference": "*******", "Microsoft.Extensions.FileProviders.Physical.Reference": "*******", "Microsoft.Extensions.FileSystemGlobbing.Reference": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http": "*******", "Microsoft.Extensions.Identity.Core": "*******", "Microsoft.Extensions.Identity.Stores": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool.Reference": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers.Reference": "*******", "Microsoft.VisualBasic.Core": "10.0.6.0", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "*******", "Microsoft.Win32.Registry.Reference": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext": "*******", "System.Buffers.Reference": "*******", "System.Collections.Concurrent.Reference": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric.Reference": "*******", "System.Collections.Specialized.Reference": "*******", "System.ComponentModel.Annotations.Reference": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel.Reference": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives.Reference": "*******", "System.ComponentModel.TypeConverter.Reference": "*******", "System.Configuration": "*******", "System.Console": "*******", "System.Core": "*******", "System.Data.Common.Reference": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.EventLog": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing.Reference": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Asn1": "*******", "System.Globalization.Calendars.Reference": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions.Reference": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.Reference": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives.Reference": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions.Reference": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable.Reference": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors": "*******", "System.ObjectModel.Reference": "*******", "System.Reflection.DispatchProxy.Reference": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions.Reference": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles.Reference": "*******", "System.Runtime.InteropServices.Reference": "*******", "System.Runtime.InteropServices.RuntimeInformation": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader.Reference": "*******", "System.Runtime.Numerics.Reference": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl.Reference": "*******", "System.Security.Claims.Reference": "*******", "System.Security.Cryptography.Algorithms.Reference": "*******", "System.Security.Cryptography.Cng.Reference": "*******", "System.Security.Cryptography.Csp.Reference": "*******", "System.Security.Cryptography.Encoding.Reference": "*******", "System.Security.Cryptography.Primitives.Reference": "*******", "System.Security.Cryptography.X509Certificates.Reference": "*******", "System.Security.Cryptography.Xml": "*******", "System.Security": "*******", "System.Security.Permissions.Reference": "*******", "System.Security.Principal.Reference": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages.Reference": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions.Reference": "*******", "System.Text.Encodings.Web.Reference": "*******", "System.Text.Json": "*******", "System.Text.RegularExpressions.Reference": "*******", "System.Threading.Channels": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Windows.Extensions": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter.Reference": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "*******", "System.Xml.XmlDocument.Reference": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath.Reference": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "runtime": {"MasterDataModule.Web.dll": {}}, "compile": {"MasterDataModule.Web.dll": {}}}, "AutoMapper/10.1.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.1.1.0"}}, "compile": {"lib/netstandard2.0/AutoMapper.dll": {}}}, "ConfigureAwait.Fody/3.3.1": {"dependencies": {"Fody": "6.5.0"}, "runtime": {"lib/netstandard2.0/ConfigureAwait.dll": {"assemblyVersion": "3.3.1.0", "fileVersion": "3.3.1.0"}}, "compile": {"lib/netstandard2.0/ConfigureAwait.dll": {}}}, "EPPlus/4.5.3.3": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "5.0.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Data.Common": "4.3.0", "System.Drawing.Common": "4.7.0", "System.Reflection": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Pkcs": "4.7.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding.CodePages": "4.7.0", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.1/EPPlus.dll": {"assemblyVersion": "4.5.3.3", "fileVersion": "4.5.3.3"}}, "compile": {"lib/netstandard2.1/EPPlus.dll": {}}}, "FirebaseAdmin/2.2.0": {"dependencies": {"Google.Api.Gax.Rest": "3.2.0", "Google.Apis.Auth": "1.49.0", "System.Collections.Immutable": "5.0.0"}, "runtime": {"lib/netstandard2.0/FirebaseAdmin.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/FirebaseAdmin.dll": {}}}, "Fody/6.5.0": {}, "Google.Api.Gax/3.2.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}, "compile": {"lib/netstandard2.0/Google.Api.Gax.dll": {}}}, "Google.Api.Gax.Rest/3.2.0": {"dependencies": {"Google.Api.Gax": "3.2.0", "Google.Apis.Auth": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}, "compile": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {}}}, "Google.Apis/1.49.0": {"dependencies": {"Google.Apis.Core": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}, "compile": {"lib/netstandard2.0/Google.Apis.dll": {}}}, "Google.Apis.Auth/1.49.0": {"dependencies": {"Google.Apis": "1.49.0", "Google.Apis.Core": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.Auth.PlatformServices.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}, "lib/netstandard2.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}, "compile": {"lib/netstandard2.0/Google.Apis.Auth.PlatformServices.dll": {}, "lib/netstandard2.0/Google.Apis.Auth.dll": {}}}, "Google.Apis.Core/1.49.0": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Google.Apis.Core.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}, "compile": {"lib/netstandard2.0/Google.Apis.Core.dll": {}}}, "JetBrains.Annotations/2020.3.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "2020.3.0.0", "fileVersion": "2020.3.0.0"}}, "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authorization/5.0.5": {"dependencies": {"Microsoft.AspNetCore.Metadata": "5.0.5", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "5.0.5"}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.JsonPatch/5.0.5": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "5.0.5.0", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Metadata/5.0.5": {"runtime": {"lib/net5.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Metadata.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "5.0.1", "Microsoft.Extensions.DependencyModel": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "5.0.5", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/5.0.5": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "5.0.5", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "5.0.5.0", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/5.0.5": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "5.0.5", "Microsoft.CodeAnalysis.Razor": "5.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {"assemblyVersion": "5.0.5.0", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/5.0.5": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "5.0.5", "Microsoft.CodeAnalysis.Razor": "5.0.5", "Microsoft.Extensions.DependencyModel": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"assemblyVersion": "5.0.5.0", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {}}}, "Microsoft.AspNetCore.Mvc.Versioning/5.0.0": {"runtime": {"lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.7710.5690"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.dll": {}}}, "Microsoft.AspNetCore.Razor.Language/5.0.5": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "5.0.5.0", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.46214"}}, "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.0.0": {}, "Microsoft.CodeAnalysis.Common/3.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.0.0", "System.Collections.Immutable": "5.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1", "System.Text.Encoding.CodePages": "4.7.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/3.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "3.8.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.Razor/5.0.5": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "5.0.5", "Microsoft.CodeAnalysis.CSharp": "3.8.0", "Microsoft.CodeAnalysis.Common": "3.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "5.0.5.0", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0"}}, "Microsoft.Extensions.DependencyInjection/5.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "5.0.0.1", "fileVersion": "5.0.120.57516"}}, "compile": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {}, "Microsoft.Extensions.DependencyModel/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.FileProviders.Composite/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.FileProviders.Embedded/5.0.17": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.Extensions.FileProviders.Embedded.dll": {}}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Localization/5.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Localization.Abstractions": "5.0.5", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/net5.0/Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/5.0.5": {"runtime": {"lib/net5.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16708"}}, "compile": {"lib/net5.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Primitives/5.0.0": {}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1.25517"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {}}}, "Nito.AsyncEx.Context/5.1.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {}}}, "Nito.AsyncEx.Coordination/5.1.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.0", "Nito.Collections.Deque": "1.1.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {}}}, "Nito.AsyncEx.Tasks/5.1.0": {"dependencies": {"Nito.Disposables": "2.2.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {}}}, "Nito.Collections.Deque/1.1.0": {"runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {}}}, "Nito.Disposables/2.2.0": {"dependencies": {"System.Collections.Immutable": "5.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {}}}, "NPOI/2.5.4": {"dependencies": {"Portable.BouncyCastle": "1.8.6", "SharpZipLib": "1.3.1", "System.Configuration.ConfigurationManager": "4.5.0", "System.Drawing.Common": "4.7.0"}, "runtime": {"lib/netstandard2.1/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.1/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.1/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.1/NPOI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/NPOI.OOXML.dll": {}, "lib/netstandard2.1/NPOI.OpenXml4Net.dll": {}, "lib/netstandard2.1/NPOI.OpenXmlFormats.dll": {}, "lib/netstandard2.1/NPOI.dll": {}}}, "NUglify/1.13.8": {"runtime": {"lib/netstandard2.0/NUglify.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/NUglify.dll": {}}}, "Portable.BouncyCastle/1.8.6": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "1.8.6.1"}}, "compile": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SharpZipLib/1.3.1": {"runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {}}}, "System.Buffers/4.5.0": {}, "System.CodeDom/4.7.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "compile": {"ref/netstandard2.0/System.CodeDom.dll": {}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/5.0.0": {}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/4.5.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.6.26515.6"}}, "compile": {"ref/netstandard2.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/4.5.0": {}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.SystemEvents": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Drawing.Common.dll": {}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.2.9": {"runtime": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.2.9.0", "fileVersion": "1.2.9.0"}}, "compile": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Management/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Registry": "4.7.0", "System.CodeDom": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Management.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}, "compile": {"ref/netstandard2.0/System.Management.dll": {}}}, "System.Memory/4.5.4": {}, "System.Net.Http.WinHttpHandler/4.4.0": {"runtime": {"lib/netstandard2.0/System.Net.Http.WinHttpHandler.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Net.Http.WinHttpHandler.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.ServiceModel/4.4.4": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Net.Http.WinHttpHandler": "4.4.0", "System.Reflection.DispatchProxy": "4.4.0", "System.Security.Principal.Windows": "4.7.0"}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/System.Private.ServiceModel.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}, "runtimes/win7/lib/netstandard2.0/System.Private.ServiceModel.dll": {"rid": "win7", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.DispatchProxy/4.4.0": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/5.0.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.7.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/4.7.0": {"dependencies": {"System.Security.Cryptography.Cng": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Cryptography.X509Certificates/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.7.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.7.0"}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.ServiceModel.Duplex/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4", "System.ServiceModel.Primitives": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Duplex.dll": {}}}, "System.ServiceModel.Http/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4", "System.ServiceModel.Primitives": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Http.dll": {}}}, "System.ServiceModel.NetTcp/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4", "System.ServiceModel.Primitives": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.NetTcp.dll": {}}}, "System.ServiceModel.Primitives/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "4.2.0.3", "fileVersion": "4.6.26720.1"}, "lib/netstandard2.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.65535.65535"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Primitives.dll": {}, "ref/netstandard2.0/System.ServiceModel.dll": {}}}, "System.ServiceModel.Security/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4", "System.ServiceModel.Primitives": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Security.dll": {"assemblyVersion": "4.0.3.3", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Security.dll": {}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.5.0": {}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XPath": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}, "compile": {"ref/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {}}}, "TimeZoneConverter/3.4.0": {"runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"assemblyVersion": "3.4.0.0", "fileVersion": "3.4.0.0"}}, "compile": {"lib/netstandard2.0/TimeZoneConverter.dll": {}}}, "Volo.Abp.ApiVersioning.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.dll": {}}}, "Volo.Abp.AspNetCore/4.3.0": {"dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.ExceptionHandling": "4.3.0", "Volo.Abp.Http": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Uow": "4.3.0", "Volo.Abp.Validation": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.dll": {}}}, "Volo.Abp.AspNetCore.Mvc/4.3.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "5.0.5", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "5.0.5", "Microsoft.AspNetCore.Mvc.Versioning": "5.0.0", "Volo.Abp.ApiVersioning.Abstractions": "4.3.0", "Volo.Abp.AspNetCore": "4.3.0", "Volo.Abp.AspNetCore.Mvc.Contracts": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.GlobalFeatures": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.UI.Navigation": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.Contracts/4.3.0": {"dependencies": {"Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI/4.3.0": {"dependencies": {"NUglify": "1.13.8", "Volo.Abp.AspNetCore.Mvc": "4.3.0", "Volo.Abp.UI.Navigation": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Views.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Views.dll": {}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/4.3.0": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/4.3.0": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "4.3.0", "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "4.3.0", "Volo.Abp.Minify": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/4.3.0": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/4.3.0": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "4.3.0", "Volo.Abp.AspNetCore.Mvc.UI.Packages": "4.3.0", "Volo.Abp.AspNetCore.Mvc.UI.Widgets": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Views.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Views.dll": {}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/4.3.0": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling": "4.3.0"}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.Views.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.Views.dll": {}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {}}}, "Volo.Abp.Auditing/4.3.0": {"dependencies": {"Volo.Abp.Data": "4.3.0", "Volo.Abp.Json": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Threading": "4.3.0", "Volo.Abp.Timing": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {}}}, "Volo.Abp.AuditLogging.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {}}}, "Volo.Abp.Authorization/4.3.0": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {}}}, "Volo.Abp.Authorization.Abstractions/4.3.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "5.0.5", "Volo.Abp.MultiTenancy": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll": {}}}, "Volo.Abp.AutoMapper/4.3.0": {"dependencies": {"AutoMapper": "10.1.1", "Volo.Abp.Auditing": "4.3.0", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.AutoMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.AutoMapper.dll": {}}}, "Volo.Abp.BackgroundJobs.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {}}}, "Volo.Abp.BlobStoring/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Threading": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.BlobStoring.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.BlobStoring.dll": {}}}, "Volo.Abp.BlobStoring.Database.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.BlobStoring.Database.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.BlobStoring.Database.Domain.Shared.dll": {}}}, "Volo.Abp.Caching/4.3.0": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "5.0.0", "Volo.Abp.Json": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Serialization": "4.3.0", "Volo.Abp.Threading": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Caching.dll": {}}}, "Volo.Abp.Commercial.Core/4.3.0": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Portable.BouncyCastle": "1.8.6", "System.Management": "4.7.0", "Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Commercial.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Commercial.Core.dll": {}}}, "Volo.Abp.Commercial.SuiteTemplates/4.3.0": {"dependencies": {"Volo.Abp.Commercial.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Commercial.SuiteTemplates.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Commercial.SuiteTemplates.dll": {}}}, "Volo.Abp.Core/4.3.0": {"dependencies": {"JetBrains.Annotations": "2020.3.0", "Microsoft.Extensions.Configuration.CommandLine": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.1", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0", "Microsoft.Extensions.Localization": "5.0.5", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0", "Nito.AsyncEx.Context": "5.1.0", "Nito.AsyncEx.Coordination": "5.1.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Linq.Dynamic.Core": "1.2.9", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Core.dll": {}}}, "Volo.Abp.Data/4.3.0": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.Uow": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Data.dll": {}}}, "Volo.Abp.Ddd.Application/4.3.0": {"dependencies": {"Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.GlobalFeatures": "4.3.0", "Volo.Abp.Http.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.dll": {}}}, "Volo.Abp.Ddd.Application.Contracts/4.3.0": {"dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll": {}}}, "Volo.Abp.Ddd.Domain/4.3.0": {"dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Data": "4.3.0", "Volo.Abp.EventBus": "4.3.0", "Volo.Abp.ExceptionHandling": "4.3.0", "Volo.Abp.Guids": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0", "Volo.Abp.Specifications": "4.3.0", "Volo.Abp.Threading": "4.3.0", "Volo.Abp.Timing": "4.3.0", "Volo.Abp.Uow": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {}}}, "Volo.Abp.EventBus/4.3.0": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {}}}, "Volo.Abp.EventBus.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll": {}}}, "Volo.Abp.ExceptionHandling/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll": {}}}, "Volo.Abp.FeatureManagement.Application/4.3.0": {"dependencies": {"Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.FeatureManagement.Application.Contracts": "4.3.0", "Volo.Abp.FeatureManagement.Domain": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.dll": {}}}, "Volo.Abp.FeatureManagement.Application.Contracts/4.3.0": {"dependencies": {"Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.FeatureManagement.Domain.Shared": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.Contracts.dll": {}}}, "Volo.Abp.FeatureManagement.Domain/4.3.0": {"dependencies": {"Volo.Abp.Caching": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.Abp.FeatureManagement.Domain.Shared": "4.3.0", "Volo.Abp.Features": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.dll": {}}}, "Volo.Abp.FeatureManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {}}}, "Volo.Abp.Features/4.3.0": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Features.dll": {}}}, "Volo.Abp.GlobalFeatures/4.3.0": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Core": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll": {}}}, "Volo.Abp.Guids/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {}}}, "Volo.Abp.Http/4.3.0": {"dependencies": {"Volo.Abp.Http.Abstractions": "4.3.0", "Volo.Abp.Json": "4.3.0", "Volo.Abp.Minify": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Http.dll": {}}}, "Volo.Abp.Http.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll": {}}}, "Volo.Abp.Identity.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Users.Domain.Shared": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll": {}}}, "Volo.Abp.Identity.Pro.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Identity.Domain.Shared": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Pro.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Identity.Pro.Domain.Shared.dll": {}}}, "Volo.Abp.IdentityServer.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll": {}}}, "Volo.Abp.Json/4.3.0": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.Timing": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Json.dll": {}}}, "Volo.Abp.LanguageManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.LanguageManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.LanguageManagement.Domain.Shared.dll": {}}}, "Volo.Abp.Localization/4.3.0": {"dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {}}}, "Volo.Abp.Localization.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {}}}, "Volo.Abp.Minify/4.3.0": {"dependencies": {"NUglify": "1.13.8", "Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Minify.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Minify.dll": {}}}, "Volo.Abp.MultiTenancy/4.3.0": {"dependencies": {"Volo.Abp.Data": "4.3.0", "Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.dll": {}}}, "Volo.Abp.ObjectExtending/4.3.0": {"dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.Validation.Abstractions": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {}}}, "Volo.Abp.ObjectMapping/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {}}}, "Volo.Abp.PermissionManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {}}}, "Volo.Abp.Security/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Security.dll": {}}}, "Volo.Abp.Serialization/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Serialization.dll": {}}}, "Volo.Abp.SettingManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {}}}, "Volo.Abp.Settings/4.3.0": {"dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {}}}, "Volo.Abp.Specifications/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Specifications.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Specifications.dll": {}}}, "Volo.Abp.TextTemplateManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.TextTemplateManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.TextTemplateManagement.Domain.Shared.dll": {}}}, "Volo.Abp.Threading/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {}}}, "Volo.Abp.Timing/4.3.0": {"dependencies": {"TimeZoneConverter": "3.4.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Settings": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {}}}, "Volo.Abp.UI/4.3.0": {"dependencies": {"Volo.Abp.ExceptionHandling": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.UI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.UI.dll": {}}}, "Volo.Abp.UI.Navigation/4.3.0": {"dependencies": {"Volo.Abp.Authorization": "4.3.0", "Volo.Abp.UI": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.UI.Navigation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.UI.Navigation.dll": {}}}, "Volo.Abp.Uow/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {}}}, "Volo.Abp.Users.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll": {}}}, "Volo.Abp.Validation/4.3.0": {"dependencies": {"Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation.Abstractions": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {}}}, "Volo.Abp.Validation.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll": {}}}, "Volo.Abp.VirtualFileSystem/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "5.0.0", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {}}}, "Volo.Saas.Domain/4.3.0": {"dependencies": {"Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Data": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.Abp.FeatureManagement.Domain": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Saas.Domain.Shared": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Saas.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Saas.Domain.dll": {}}}, "Volo.Saas.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.FeatureManagement.Domain.Shared": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Saas.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Saas.Domain.Shared.dll": {}}}, "Volo.Saas.Host.Application/4.3.0": {"dependencies": {"Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.FeatureManagement.Application": "4.3.0", "Volo.Saas.Domain": "4.3.0", "Volo.Saas.Host.Application.Contracts": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Saas.Host.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Saas.Host.Application.dll": {}}}, "Volo.Saas.Host.Application.Contracts/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.FeatureManagement.Application.Contracts": "4.3.0", "Volo.Saas.Domain.Shared": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Saas.Host.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Volo.Saas.Host.Application.Contracts.dll": {}}}, "AOMS.Domain.Shared/1.0.0": {"dependencies": {"BondedModuleV2.Domain.Shared": "0.1.0", "GeneralModule.Domain.Shared": "0.1.0", "InboundModule.Domain.Shared": "0.1.0", "MasterDataModule.Domain.Shared": "0.1.0", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "NPOI": "2.5.4", "OutboundModule.Domain.Shared": "0.1.0", "ReportModule.Domain.Shared": "0.1.0", "SeaModule.Domain.Shared": "0.1.0", "ShareDataModule.Domain.Shared": "0.1.0", "TrayModule.Domain.Shared": "0.1.0", "Volo.Abp.AuditLogging.Domain.Shared": "4.3.0", "Volo.Abp.BackgroundJobs.Domain.Shared": "4.3.0", "Volo.Abp.BlobStoring.Database.Domain.Shared": "4.3.0", "Volo.Abp.Commercial.SuiteTemplates": "4.3.0", "Volo.Abp.FeatureManagement.Domain.Shared": "4.3.0", "Volo.Abp.GlobalFeatures": "4.3.0", "Volo.Abp.Identity.Pro.Domain.Shared": "4.3.0", "Volo.Abp.IdentityServer.Domain.Shared": "4.3.0", "Volo.Abp.LanguageManagement.Domain.Shared": "4.3.0", "Volo.Abp.LeptonTheme.Management.Domain.Shared": "1.0.0", "Volo.Abp.PermissionManagement.Domain.Shared": "4.3.0", "Volo.Abp.SettingManagement.Domain.Shared": "4.3.0", "Volo.Abp.TextTemplateManagement.Domain.Shared": "4.3.0", "Volo.Chat.Domain.Shared": "1.0.0", "Volo.FileManagement.Domain.Shared": "1.0.0", "Volo.Saas.Domain.Shared": "4.3.0"}, "runtime": {"AOMS.Domain.Shared.dll": {}}, "compile": {"AOMS.Domain.Shared.dll": {}}}, "BondedModuleV2.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"BondedModuleV2.Domain.Shared.dll": {}}, "compile": {"BondedModuleV2.Domain.Shared.dll": {}}}, "GeneralModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"GeneralModule.Domain.Shared.dll": {}}, "compile": {"GeneralModule.Domain.Shared.dll": {}}}, "InboundModule.Domain/0.1.0": {"dependencies": {"InboundModule.Domain.Shared": "0.1.0", "MasterDataModule.Domain": "0.1.0", "ShareDataModule.Application.Contracts": "0.1.0", "ShareDataModule.Domain": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"InboundModule.Domain.dll": {}}, "compile": {"InboundModule.Domain.dll": {}}}, "InboundModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"InboundModule.Domain.Shared.dll": {}}, "compile": {"InboundModule.Domain.Shared.dll": {}}}, "MasterDataModule.Application/0.1.0": {"dependencies": {"AOMS.Domain.Shared": "1.0.0", "FirebaseAdmin": "2.2.0", "InboundModule.Domain": "0.1.0", "MasterDataModule.Application.Contracts": "0.1.0", "MasterDataModule.Domain": "0.1.0", "OutboundModule.Domain": "0.1.0", "ShareDataModule.Application": "0.1.0", "System.ServiceModel.Duplex": "4.4.4", "System.ServiceModel.Http": "4.4.4", "System.ServiceModel.NetTcp": "4.4.4", "System.ServiceModel.Security": "4.4.4", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.BlobStoring": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.FileManagement.Domain": "1.0.0", "Volo.Saas.Host.Application": "4.3.0"}, "runtime": {"MasterDataModule.Application.dll": {}}, "compile": {"MasterDataModule.Application.dll": {}}}, "MasterDataModule.Application.Contracts/0.1.0": {"dependencies": {"MasterDataModule.Domain.Shared": "0.1.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0", "Volo.FileManagement.Application.Contracts": "1.0.0"}, "runtime": {"MasterDataModule.Application.Contracts.dll": {}}, "compile": {"MasterDataModule.Application.Contracts.dll": {}}}, "MasterDataModule.Domain/0.1.0": {"dependencies": {"MasterDataModule.Domain.Shared": "0.1.0", "Microsoft.CSharp": "4.7.0", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"MasterDataModule.Domain.dll": {}}, "compile": {"MasterDataModule.Domain.dll": {}}}, "MasterDataModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"MasterDataModule.Domain.Shared.dll": {}}, "compile": {"MasterDataModule.Domain.Shared.dll": {}}}, "MasterDataModule.HttpApi/0.1.0": {"dependencies": {"FirebaseAdmin": "2.2.0", "MasterDataModule.Application.Contracts": "0.1.0", "Volo.Abp.AspNetCore.Mvc": "4.3.0"}, "runtime": {"MasterDataModule.HttpApi.dll": {}}, "compile": {"MasterDataModule.HttpApi.dll": {}}}, "OutboundModule.Domain/0.1.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "OutboundModule.Domain.Shared": "0.1.0", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"OutboundModule.Domain.dll": {}}, "compile": {"OutboundModule.Domain.dll": {}}}, "OutboundModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"OutboundModule.Domain.Shared.dll": {}}, "compile": {"OutboundModule.Domain.Shared.dll": {}}}, "ReportModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"ReportModule.Domain.Shared.dll": {}}, "compile": {"ReportModule.Domain.Shared.dll": {}}}, "SeaModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"SeaModule.Domain.Shared.dll": {}}, "compile": {"SeaModule.Domain.Shared.dll": {}}}, "ShareDataModule.Application/0.1.0": {"dependencies": {"ShareDataModule.Application.Contracts": "0.1.0", "ShareDataModule.Domain": "0.1.0", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0"}, "runtime": {"ShareDataModule.Application.dll": {}}, "compile": {"ShareDataModule.Application.dll": {}}}, "ShareDataModule.Application.Contracts/0.1.0": {"dependencies": {"ShareDataModule.Domain.Shared": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"ShareDataModule.Application.Contracts.dll": {}}, "compile": {"ShareDataModule.Application.Contracts.dll": {}}}, "ShareDataModule.Domain/0.1.0": {"dependencies": {"ShareDataModule.Domain.Shared": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"ShareDataModule.Domain.dll": {}}, "compile": {"ShareDataModule.Domain.dll": {}}}, "ShareDataModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"ShareDataModule.Domain.Shared.dll": {}}, "compile": {"ShareDataModule.Domain.Shared.dll": {}}}, "TrayModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"TrayModule.Domain.Shared.dll": {}}, "compile": {"TrayModule.Domain.Shared.dll": {}}}, "Volo.Abp.LeptonTheme.Management.Domain.Shared/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"Volo.Abp.LeptonTheme.Management.Domain.Shared.dll": {}}, "compile": {"Volo.Abp.LeptonTheme.Management.Domain.Shared.dll": {}}}, "Volo.Chat.Domain.Shared/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"Volo.Chat.Domain.Shared.dll": {}}, "compile": {"Volo.Chat.Domain.Shared.dll": {}}}, "Volo.FileManagement.Application.Contracts/1.0.0": {"dependencies": {"EPPlus": "4.5.3.3", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0", "Volo.FileManagement.Domain.Shared": "1.0.0"}, "runtime": {"Volo.FileManagement.Application.Contracts.dll": {}}, "compile": {"Volo.FileManagement.Application.Contracts.dll": {}}}, "Volo.FileManagement.Domain/1.0.0": {"dependencies": {"EPPlus": "4.5.3.3", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.BlobStoring": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.FileManagement.Domain.Shared": "1.0.0"}, "runtime": {"Volo.FileManagement.Domain.dll": {}}, "compile": {"Volo.FileManagement.Domain.dll": {}}}, "Volo.FileManagement.Domain.Shared/1.0.0": {"dependencies": {"EPPlus": "4.5.3.3", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"Volo.FileManagement.Domain.Shared.dll": {}}, "compile": {"Volo.FileManagement.Domain.Shared.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core.Reference/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json.Reference/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Reference/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp.Reference/*******": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core/*******": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/*******": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.Reference/*******": {"compile": {"Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives.Reference/*******": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers.Reference/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/10.0.6.0": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry.Reference/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext/*******": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers.Reference/*******": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent.Reference/*******": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable.Reference/*******": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric.Reference/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized.Reference/*******": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations.Reference/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.Reference/*******": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives.Reference/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter.Reference/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console/*******": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common.Reference/*******": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing.Reference/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Asn1/*******": {"compile": {"System.Formats.Asn1.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars.Reference/*******": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions.Reference/*******": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression/*******": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Reference/*******": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives.Reference/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines/*******": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions.Reference/*******": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable.Reference/*******": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http/*******": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/*******": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives/*******": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets/*******": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/*******": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel.Reference/*******": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy.Reference/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Reference/*******": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight.Reference/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions.Reference/*******": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata.Reference/*******": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions.Reference/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles.Reference/*******": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.Reference/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader.Reference/*******": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics.Reference/*******": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl.Reference/*******": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims.Reference/*******": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms.Reference/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng.Reference/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp.Reference/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding.Reference/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives.Reference/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Permissions.Reference/*******": {"compile": {"System.Security.Permissions.dll": {}}, "compileOnly": true}, "System.Security.Principal.Reference/*******": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows.Reference/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages.Reference/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions.Reference/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.Encodings.Web.Reference/*******": {"compile": {"System.Text.Encodings.Web.dll": {}}, "compileOnly": true}, "System.Text.Json/*******": {"compile": {"System.Text.Json.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions.Reference/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.Channels/*******": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading.Reference/*******": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer/*******": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Windows.Extensions/*******": {"compile": {"System.Windows.Extensions.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter.Reference/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument/*******": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument.Reference/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath.Reference/*******": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}}}, "libraries": {"MasterDataModule.Web/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/10.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "path": "automapper/10.1.1", "hashPath": "automapper.10.1.1.nupkg.sha512"}, "ConfigureAwait.Fody/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-R9PQYf0AT4RBZcUXm22xWkCpSmNHdTzQ0dOyLIsxIK6dwXH4S9pY/rZdXU/63i8vZvSzZ99sB1kP7xer8MCe6w==", "path": "configureawait.fody/3.3.1", "hashPath": "configureawait.fody.3.3.1.nupkg.sha512"}, "EPPlus/4.5.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-aZnWmrQNRQuV/2Kwu+pTv1HVX1ZBvoGKw2GuzUOmBRpOD09sYJFGHww8NVQd3IIeb1vqosPDFZgND/1nxTzFKQ==", "path": "epplus/4.5.3.3", "hashPath": "epplus.4.5.3.3.nupkg.sha512"}, "FirebaseAdmin/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nF+7Z7kJ2ikjh75vjKkSmuqgI9b0O8Azygu3GMvx3/z3v5dnk5b6O9nL5/WkuCenYDzyz35r85pkN3C9K93+2g==", "path": "firebaseadmin/2.2.0", "hashPath": "firebaseadmin.2.2.0.nupkg.sha512"}, "Fody/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+bc+fzXFum7/MPjhEsxw+kIGcwfnI3JHv4w2T0jLLvk9HfO/xoq/aIjF1TSPlUWl+A1k2odtwdEdKZXJZXZzbQ==", "path": "fody/6.5.0", "hashPath": "fody.6.5.0.nupkg.sha512"}, "Google.Api.Gax/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-0<PERSON>jahFAHTOoprSgvJiQ6/fIQLrUYU4QIFgkuJ51/lcmhZbuXxB3ycPk3JTVEvx6A5yQBL14wgmHgwXLcEsnu3Q==", "path": "google.api.gax/3.2.0", "hashPath": "google.api.gax.3.2.0.nupkg.sha512"}, "Google.Api.Gax.Rest/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YY4mD0nGxTx1uez7Perm+zAd3FH50dd3+7HTYsRFCywDEtj3RkrMjcAmw6mNpKkw2sRICu7aYNy1mgMjd3nVbw==", "path": "google.api.gax.rest/3.2.0", "hashPath": "google.api.gax.rest.3.2.0.nupkg.sha512"}, "Google.Apis/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-fmXQQTxZFOBlnvokvdQMzq0Lt+g2QzpZ4H6U8lMMFHgAR8ZqxQnPN5yHDpoHf7a++hJHb5W3pALxauGsq+afKQ==", "path": "google.apis/1.49.0", "hashPath": "google.apis.1.49.0.nupkg.sha512"}, "Google.Apis.Auth/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-3V9ohvixQtjaEvk7T9Ac7E/KvwEPa7eL4aMNreJDI0CEPzCdQdk2zCvaJPRrNIjhe+UuBeOeom1oAOMFB74JHg==", "path": "google.apis.auth/1.49.0", "hashPath": "google.apis.auth.1.49.0.nupkg.sha512"}, "Google.Apis.Core/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-9DgGdtyzgrCfHWwc/HiDXDbykNMeKQozbEHYEUEcezRuH+YR3fvq7YGVBDmUM8g6qEL3kDk6h5EU4h0IJwue1w==", "path": "google.apis.core/1.49.0", "hashPath": "google.apis.core.1.49.0.nupkg.sha512"}, "JetBrains.Annotations/2020.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FnX06vtxuoZnhZdR6UHt5kJ7HUC/syODfGLnhPDn1x5sXvvepNyCl4jMtPUzJfsPWh7q0Jo+AIYz5xaVbbyikA==", "path": "jetbrains.annotations/2020.3.0", "hashPath": "jetbrains.annotations.2020.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pQSx1MrLJlKwlEclliO9aUwKxe9EKI2Mff39VE1t5VYOjqsMyI2ujWKGI6XAUsnmC0Bta67GZ1k4DbQZd7tJKg==", "path": "microsoft.aspnetcore.authorization/5.0.5", "hashPath": "microsoft.aspnetcore.authorization.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ViYrCnQFWP3BHVyq73YYe7OijodEp0qBt3l0H3tQtdGB83fb/uEBPGXGrSvfCLkLJPa/Rk4/ZH3oEQcQao2VUg==", "path": "microsoft.aspnetcore.jsonpatch/5.0.5", "hashPath": "microsoft.aspnetcore.jsonpatch.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-SD+puPsFLQXWwzoMiXv8lpFGHyZg0gKp3OP2EXg3eRlwJQOMaUGMIqbCdfHGR4MBaUZtqY/tU68H1bzb6+FSxA==", "path": "microsoft.aspnetcore.metadata/5.0.5", "hashPath": "microsoft.aspnetcore.metadata.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ALiY4a6BYsghw8PT5+VU593Kqp911U3w9f/dH9/ZoI3ezDsDAGiObqPu/HP1oXK80Ceu0XdQ3F0bx5AXBeuN/Q==", "path": "microsoft.aspnetcore.mvc.core/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ScWwXrkAvw6PekWUFkIr5qa9NKn4uZGRvxtt3DvtUrBYW5Iu2y4SS/vx79JN0XDHNYgAJ81nVs+4M7UE1Y/O+g==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-3Vd2hbJwrHFOk6dYpqFQ10BBLeJvMPmbx3hxqH5erxSlvFOufnq3C9Nnz+hPpaqazRAQcOgkAWGvBsB3AM2XlQ==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/5.0.5", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-wj6Q8mcbL8xDS47axjojDiB0nBhRekBl2Ng/v9Hnk0TxxqD5eMxn6OeKELJjLkQqW6P4vCmdAiBEYLGAr5P/8g==", "path": "microsoft.aspnetcore.mvc.razor.extensions/5.0.5", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-nzYMwt6zGvpWFD4RdMnPyJGzHgXusCjx6jOqGyJ+goZHi9QhzS94dUOyQ5Q3aULAC4Qh2B0p5Gx7V+7RJdr1XA==", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/5.0.5", "hashPath": "microsoft.aspnetcore.mvc.razor.runtimecompilation.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Versioning/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mN9IARvNpHMBD2/oGmp5Bxp1Dg45Hfcp+LWaAyTtL2HisWLMOIcf0Ox1qW9IvCvdbHM+2A9dWEInhiqBsNxsJA==", "path": "microsoft.aspnetcore.mvc.versioning/5.0.0", "hashPath": "microsoft.aspnetcore.mvc.versioning.5.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-wh4JC/1sWxythiEEUKacWqZWNWnx6Kbmpj6FhYc28zV2VAV1+c9Ii1y7fmRefZciJpI/b25bm7c8icT72g2O9g==", "path": "microsoft.aspnetcore.razor.language/5.0.5", "hashPath": "microsoft.aspnetcore.razor.language.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K63Y4hORbBcKLWH5wnKgzyn7TOfYzevIEwIedQHBIkmkEBA9SCqgvom+XTuE+fAFGvINGkhFItaZ2dvMGdT5iw==", "path": "microsoft.bcl.asyncinterfaces/1.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojG5pGAhTPmjxRGTNvuszO3H8XPZqksDwr9xLd4Ae/JBjZZdl6GuoLk7uLMf+o7yl5wO0TAqoWcEKkEWqrZE5g==", "path": "microsoft.codeanalysis.analyzers/3.0.0", "hashPath": "microsoft.codeanalysis.analyzers.3.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-8YTZ7GpsbTdC08DITx7/kwV0k4SC6cbBAFqc13cOm5vKJZcEIAh51tNSyGSkWisMgYCr96B2wb5Zri1bsla3+g==", "path": "microsoft.codeanalysis.common/3.8.0", "hashPath": "microsoft.codeanalysis.common.3.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-hKqFCUSk9TIMBDjiYMF8/ZfK9p9mzpU+slM73CaCHu4ctfkoqJGHLQhyT8wvrYsIg+ufrUWBF8hcJYmyr5rc5Q==", "path": "microsoft.codeanalysis.csharp/3.8.0", "hashPath": "microsoft.codeanalysis.csharp.3.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-UYfBFE6dWkFXAIZlc/p8DYawHAPdfPdWBcX5Zrvx8WxYV74qdomARTyDDArKfbfm2MMi2oIReNjabPrBz4T42w==", "path": "microsoft.codeanalysis.razor/5.0.5", "hashPath": "microsoft.codeanalysis.razor.5.0.5.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "path": "microsoft.extensions.caching.abstractions/5.0.0", "hashPath": "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "path": "microsoft.extensions.caching.memory/5.0.0", "hashPath": "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "path": "microsoft.extensions.configuration/5.0.0", "hashPath": "microsoft.extensions.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Of1Irt1+NzWO+yEYkuDh5TpT4On7LKl98Q9iLqCdOZps6XXEWDj3AKtmyvzJPVXZe4apmkJJIiDL7rR1yC+hjQ==", "path": "microsoft.extensions.configuration.binder/5.0.0", "hashPath": "microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OelM+VQdhZ0XMXsEQBq/bt3kFzD+EBGqR4TAgFDRAye0JfvHAaRi+3BxCRcwqUAwDhV0U0HieljBGHlTgYseRA==", "path": "microsoft.extensions.configuration.commandline/5.0.0", "hashPath": "microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fqh6y6hAi0Z0fRsb4B/mP9OkKkSlifh5osa+N/YSQ+/S2a//+zYApZMUC1XeP9fdjlgZoPQoZ72Q2eLHyKLddQ==", "path": "microsoft.extensions.configuration.environmentvariables/5.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "path": "microsoft.extensions.configuration.json/5.0.0", "hashPath": "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+tK3seG68106lN277YWQvqmfyI/89w0uTu/5Gz5VYSUu5TI4mqwsaWLlSmT9Bl1yW/i1Nr06gHJxqaqB5NU9Tw==", "path": "microsoft.extensions.configuration.usersecrets/5.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-//mDNrYeiJ0eh/awFhDFJQzkRVra/njU5Y4fyK7X29g5HScrzbUkKOKlyTtygthcGFt4zNC8G5CFCjb/oizomA==", "path": "microsoft.extensions.dependencyinjection/5.0.1", "hashPath": "microsoft.extensions.dependencyinjection.5.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-umBECCoMC+sOUgm083yFr8SxTobUOcPFH4AXigdO2xJiszCHAnmeDl4qPphJt+oaJ/XIfV1wOjIts2nRnki61Q==", "path": "microsoft.extensions.dependencymodel/5.0.0", "hashPath": "microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0IoXXfkgKpYJB1t2lC0jPXAxuaywRNc9y2Mq96ZZNKBthL38vusa2UK73+Bm6Kq/9a5xNHJS6NhsSN+i5TEtkA==", "path": "microsoft.extensions.fileproviders.composite/5.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-sSf5oTuE/BYju9hqvwL6CSwArv76mONplyVpYV2J8Il/m2mtuabx2o3YmTeO8aa5+2JaFWZlOX+2X3fWYEp79w==", "path": "microsoft.extensions.fileproviders.embedded/5.0.17", "hashPath": "microsoft.extensions.fileproviders.embedded.5.0.17.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbUOCePYBl1UhM+N2zmDSUyJ6cODulbtUd9gEzMFIK3RQDtP/gJsE08oLcBSXH3Q1RAQ0ex7OAB3HeTKB9bXpg==", "path": "microsoft.extensions.hosting.abstractions/5.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-XBX02xG84g6q+sGnUnBLuRHZt+ZfKIKeY+oLsSqSb/0Hy53lmCGiufCpMH4TZVqmpT3xmFb47YKhA4ROt0SwVQ==", "path": "microsoft.extensions.localization/5.0.5", "hashPath": "microsoft.extensions.localization.5.0.5.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-NIKR1AE1gt4QfThQCxJnzQIYcp0sDijX61GtkrgDce0kqatAME7oZDnYQAZTYlm/QYXKNYqu+S58BW53QRM7oQ==", "path": "microsoft.extensions.localization.abstractions/5.0.5", "hashPath": "microsoft.extensions.localization.abstractions.5.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-280RxNJqOeQqq47aJLy5D9LN61CAWeuRA83gPToQ8B9jl9SNdQ5EXjlfvF66zQI5AXMl+C/3hGnbtIEN+X3mqA==", "path": "microsoft.extensions.options.configurationextensions/5.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-EE7M37c5E/kvulzEkpUR6v1AnK34b2wysOLJHSjl78p/3hL7grte0XCPRqCfLZDwq98AD9GHMTCRfZy7TEeHhw==", "path": "nito.asyncex.context/5.1.0", "hashPath": "nito.asyncex.context.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Coordination/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nv+oA+cSxidjOImiKcz2FJgMIDxiK0A6xormKmsUklUBjTNqQpjtdJsACMgTQG56PkTHdbMi5QijPTTUsmcCeg==", "path": "nito.asyncex.coordination/5.1.0", "hashPath": "nito.asyncex.coordination.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tU3Ib4zs8ivM+uS8n7F7ReWZlA3mODyLqwPE+v+WJI94hZ8xLXl+a9npfj/IcmeXo9a6fGKLWkswKQHOeTWqwA==", "path": "nito.asyncex.tasks/5.1.0", "hashPath": "nito.asyncex.tasks.5.1.0.nupkg.sha512"}, "Nito.Collections.Deque/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-RXHe531Oaw2IathDr0Q2kbid0iuudBxtgZsfBZ2eUPuFI8I1P7HMiuUeaIefqYykcDYFTDQsFAPAljduIjihLA==", "path": "nito.collections.deque/1.1.0", "hashPath": "nito.collections.deque.1.1.0.nupkg.sha512"}, "Nito.Disposables/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-QcL+uBwUCEoK8GKp/WzjdCiG8/3G1WLlVNJgLJUNG7bIIVAcEV+Mro4s53VT4Nd8xMSplv0gy+Priw44vRvLaA==", "path": "nito.disposables/2.2.0", "hashPath": "nito.disposables.2.2.0.nupkg.sha512"}, "NPOI/2.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-RvkPglpis+1P7pmygJBk08XSZGFSBTdAlqpxbh8jQBIZnkXEfKJay0BrhSjXn/Nn+vFcTNyLYVdBPBMalSX32g==", "path": "npoi/2.5.4", "hashPath": "npoi.2.5.4.nupkg.sha512"}, "NUglify/1.13.8": {"type": "package", "serviceable": true, "sha512": "sha512-S6ofErWTXnV/5k2GUirnhC+xisauluBki1L0MWBQKGFnSQcMB3B3AbUsDoJE7XVgo4ZgVcqXZ0eMeZieKe1kKA==", "path": "nuglify/1.13.8", "hashPath": "nuglify.1.13.8.nupkg.sha512"}, "Portable.BouncyCastle/1.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-y+GvZomzhY+Lwu5mMeNmFFYLHiEr2xFDOANhABn/wgg64/QpTzfgpNGPct+pXgQHjmutd363ZCur/91DLaBxOw==", "path": "portable.bouncycastle/1.8.6", "hashPath": "portable.bouncycastle.1.8.6.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SharpZipLib/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-/iMph6bLdKzDhwM/vkAo4BU8z5QQnyodlkZon3XRMtRmVuWv5Rph1kaDmd9XjrQxjPJPuLquTSrkEoSPq/flVw==", "path": "sharpziplib/1.3.1", "hashPath": "sharpziplib.1.3.1.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.CodeDom/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hs9pw/kmvH3lXaZ1LFKj3pLQsiGfj2xo3sxSzwiLlRL6UcMZUTeCfoJ9Udalvn3yq5dLlPEZzYegrTQ1/LhPOQ==", "path": "system.codedom/4.7.0", "hashPath": "system.codedom.4.7.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "path": "system.configuration.configurationmanager/4.5.0", "hashPath": "system.configuration.configurationmanager.4.5.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIHRELiYDQvsMToML81QFkXEEYXUSUT2F28t1SGrevWqP+epFdw80SyAXIKTXOHrIEXReFOEnEr7XlGiC2GgOg==", "path": "system.diagnostics.diagnosticsource/4.5.0", "hashPath": "system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-v+XbyYHaZjDfn0ENmJEV1VYLgGgCTx1gnfOBcppowbpOAriglYgGCvFCPr2EEZyBvXlpxbEsTwkOlInl107ahA==", "path": "system.drawing.common/4.7.0", "hashPath": "system.drawing.common.4.7.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.2.9": {"type": "package", "serviceable": true, "sha512": "sha512-GjR4CCtIkiJSU6N8cidG4aa0ph+HBzFOq3uhLybuq4zjlKy3hjDrGbcEUeBiGpBmUrnUhTAJ5SCZGDoZS7d9SA==", "path": "system.linq.dynamic.core/1.2.9", "hashPath": "system.linq.dynamic.core.1.2.9.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Management/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-IY+uuGhgzWiCg21i8IvQeY/Z7m1tX8VuPF+ludfn7iTCaccTtJo5HkjZbBEL8kbBubKhAKKtNXr7uMtmAc28Pw==", "path": "system.management/4.7.0", "hashPath": "system.management.4.7.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Net.Http.WinHttpHandler/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZmsFZIZ4PL2UfUlfj4KCzzAGGE2SF39ENIqtvfgu0bwMEAe3J3CqZr765E2W6eQQtNK08/8DpHcsA0sAKZdEtA==", "path": "system.net.http.winhttphandler/4.4.0", "hashPath": "system.net.http.winhttphandler.4.4.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-tsDkr5hwYwxV2LaB3H5BvCltCX57wv7JzwU5q8IV9vrFdzq/e/nPJAj4U5Ny/FzR4sMTVRprVSwl4mZqfFkt4Q==", "path": "system.private.servicemodel/4.4.4", "hashPath": "system.private.servicemodel.4.4.4.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-xK6JE0mpsBD+T0qu3V6dmVRa06PxAvIIM/zSjouqP7Sk6X+FQj+9XFRz9GmZk9aJGMU1LX6AgTZIlsYZ64QKsw==", "path": "system.reflection.dispatchproxy/4.4.0", "hashPath": "system.reflection.dispatchproxy.4.4.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-zOHkQmzPCn5zm/BH+cxC1XbUS3P4Yoi3xzW7eRgVpDR2tPGSzyMZ17Ig1iRkfJuY0nhxkQQde8pgePNiA7z7TQ==", "path": "system.runtime.compilerservices.unsafe/4.7.1", "hashPath": "system.runtime.compilerservices.unsafe.4.7.1.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-4WQjFuypWtxb/bl/YwEE7LYGn4fgpsikFfBU6xwEm4YBYiRAhXAEJ62lILBu2JJSFbClIAntFTGfDZafn8yZTg==", "path": "system.security.cryptography.cng/4.7.0", "hashPath": "system.security.cryptography.cng.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0Srzh6YlhjuMxaqMyeCCdZs22cucaUAG6SKDd3gNHBJmre0VZ71ekzWu9rvLD4YXPetyNdPvV6Qst+8C++9v3Q==", "path": "system.security.cryptography.pkcs/4.7.0", "hashPath": "system.security.cryptography.pkcs.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-uwlfOnvJd7rXRvP3aV126Q9XebIIEGEaZ245Rd5/ZwOg7U7AU+AmpE0vRh2F0DFjfOTuk7MAexv4nYiNP/RYnQ==", "path": "system.security.cryptography.x509certificates/4.3.2", "hashPath": "system.security.cryptography.x509certificates.4.3.2.nupkg.sha512"}, "System.Security.Permissions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "path": "system.security.permissions/4.5.0", "hashPath": "system.security.permissions.4.5.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-y/XLnKJ+xnuEUjrgJkXeLKCH4A+EwkX2TdOcTdgsEtxWmWq1+RbCjMd0zIlyNrbGD+nM9BxNg9rLVWVAPq81RA==", "path": "system.servicemodel.duplex/4.4.4", "hashPath": "system.servicemodel.duplex.4.4.4.nupkg.sha512"}, "System.ServiceModel.Http/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-f7OWPKqfTaCzjpc6n+/xqNwv7YAHKMiBCPIgwxIXVnf0Vu9+yzfX6tXV9pSSCEFuqJ5tXGLz9MRRExrQEqVUkA==", "path": "system.servicemodel.http/4.4.4", "hashPath": "system.servicemodel.http.4.4.4.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-TZUwkBUHK+HgPVpypcnCEzenn+Hly3mQ+QDqDtHKyqVBP2Yt+DAMp3NwuW35mTSMRqna5p9hd0U0vVq5azovOQ==", "path": "system.servicemodel.nettcp/4.4.4", "hashPath": "system.servicemodel.nettcp.4.4.4.nupkg.sha512"}, "System.ServiceModel.Primitives/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-Jv882Qt+tSJ2KnLGGMSGPwBvOxfXRncXW0YV/beBKRQ2c6bDbXVXwqaS2U9h+/cK1uL7C4zc66lnC7qvSBqPHw==", "path": "system.servicemodel.primitives/4.4.4", "hashPath": "system.servicemodel.primitives.4.4.4.nupkg.sha512"}, "System.ServiceModel.Security/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-8mJj3lUNbBkntouQ3Eg3IF04GxiDrDK2X79+kcfq8V+W7NoYBREgWczaD60ZmW4KKRKnL0q3OnUNJlkzJJfe5w==", "path": "system.servicemodel.security/4.4.4", "hashPath": "system.servicemodel.security.4.4.4.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-aeu4FlaUTemuT1qOd1MyU4T516QR4Fy+9yDbwWMPHOHy7U8FD6SgTzdZFO7gHcfAPHtECqInbwklVvUK4RHcNg==", "path": "system.text.encoding.codepages/4.7.0", "hashPath": "system.text.encoding.codepages.4.7.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "path": "system.text.encodings.web/4.5.0", "hashPath": "system.text.encodings.web.4.5.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-A/uxsWi/Ifzkmd4ArTLISMbfFs6XpRPsXZonrIqyTY70xi8t+mDtvSM5Os0RqyRDobjMBwIDHDL4NOIbkDwf7A==", "path": "system.xml.xpath.xmldocument/4.3.0", "hashPath": "system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"}, "TimeZoneConverter/3.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-4rqXd06uLqaLPGpO+whrZnSTJyG3SG5DEbeldMTvaGit0+gI6XLVb9YMnfM+5wtV0DDM1QPd/QhbE6j5vJCWDw==", "path": "timezoneconverter/3.4.0", "hashPath": "timezoneconverter.3.4.0.nupkg.sha512"}, "Volo.Abp.ApiVersioning.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-x6dgBSTcOJTUbN9BHCBwzZ61h9ujW6rVaUyNjJ+1wnM+BRI9LhvoXIODA7160i5OKxKsbBlSV4qV45hHGIy47A==", "path": "volo.abp.apiversioning.abstractions/4.3.0", "hashPath": "volo.abp.apiversioning.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-dGFxH4wgax5SsjHg8rAyBDMa+JX/i9yDLmvQi7uXPN2G3QdPYKgm8VxZecxuA8G8EwPg2Fc+An/7OZUcg2Xsvw==", "path": "volo.abp.aspnetcore/4.3.0", "hashPath": "volo.abp.aspnetcore.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFpGvCjrav/PMoWSDXg6bIa1jdPNSVXs9bOZ/4v2CN4DvjRxjPKJXTrDm5VmcyWvi/9eItxBtSQhq89/8Y586w==", "path": "volo.abp.aspnetcore.mvc/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-QoWDbsp5Tpm6nhZqJX0d1Sz/XPs1NhEIijzbKH6aJqRypyV7RLimhj7uFflCyB0ZFA5Vl4yhjp7ZehMuEY321A==", "path": "volo.abp.aspnetcore.mvc.contracts/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.contracts.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-s3P9h/YbUD5g5EAcJalce0RZiu/SMeU+DgHHhyPgqK+yNS7kVrLCftwGDRZAlzmLyIETA7Z+oXw+esu7cTHSpA==", "path": "volo.abp.aspnetcore.mvc.ui/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.ui.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cmO+1dBQb9oWcU3lgw6UEjptNcS/JL/vU7OOE2/xr1ScUgNQ+q42VY3AYI+49Qu2+1EymmdGpXAP5WTfeUjffw==", "path": "volo.abp.aspnetcore.mvc.ui.bootstrap/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.ui.bootstrap.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-8KTKH+b5kmOZFU5TTCSk8ZP195oNJ0Mh5v68ecH7+mhM8zX8HF9qF8kTjGf9Ic9MeHm9ZqtadgSMjAJhO2O01w==", "path": "volo.abp.aspnetcore.mvc.ui.bundling/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.ui.bundling.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-IsBAzY811Xd3Ei31XKkQHiFWy6PiMGWO8xxydyBOaGewfp/ROX9RUrKL+ZDmXyAj0dN8lIgMfi//UUfIMbKe6w==", "path": "volo.abp.aspnetcore.mvc.ui.bundling.abstractions/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.ui.bundling.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6PWDHGFWuT2ruIFJFriYssY88LnkWDZ2d1XztFOo5kYvc5sxb663Eg3wTsDDMuZW7f977X+3b6CB0Np6TLLPYg==", "path": "volo.abp.aspnetcore.mvc.ui.packages/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.ui.packages.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Uuz8O1pkGWP5vIPiC63RyJmNVN2I1XtjXlLhYFvSUnE6DKjgx9GrerIgrHSenx6+4IGuBKFWQdfjmhtGlWIqg==", "path": "volo.abp.aspnetcore.mvc.ui.theme.shared/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.ui.theme.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nvefhpGzg/99XvK7d5fUmP8e7bgpUVWY4ip+uMEikaM0USuFqKR3t7YFjY37nDCPwMNBBK3B+XwxX8/ZM6/vGg==", "path": "volo.abp.aspnetcore.mvc.ui.widgets/4.3.0", "hashPath": "volo.abp.aspnetcore.mvc.ui.widgets.4.3.0.nupkg.sha512"}, "Volo.Abp.Auditing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-zptBxUin6U/LbdRdG6rRn+NyTQIAqN7wqjx3Ui7G9vvTHrRCreqzvuOsPC767HDUlcu+8o3LNJLQCuE981219A==", "path": "volo.abp.auditing/4.3.0", "hashPath": "volo.abp.auditing.4.3.0.nupkg.sha512"}, "Volo.Abp.AuditLogging.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hFvjlGIYGdxX2m0l9Byax+VVH1yJnUvsem2XZF4rfchbOT6ncANCmwuQXUP1Ij2Vls1cwDP4D1wJPqGDtnmbFA==", "path": "volo.abp.auditlogging.domain.shared/4.3.0", "hashPath": "volo.abp.auditlogging.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Authorization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9j5U19ntqS8JR+s86xNFm0ieH9CHz/po51d6BHxLTZzJuKbIu9xg453KvhKyuXYYnhp+/afu7Uq5EEi/s7sl9A==", "path": "volo.abp.authorization/4.3.0", "hashPath": "volo.abp.authorization.4.3.0.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-enL7a1MfJR0yjPVhf2UCgEmmQQ5wNb2GGX3k/iKzW5i//3lcnsDZQLAg17NKcbLc+imQ39RALNTOE4ALZQ4C7Q==", "path": "volo.abp.authorization.abstractions/4.3.0", "hashPath": "volo.abp.authorization.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.AutoMapper/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-mqeDGZxdhXFQMvxfM26snT2q0pbA+3OFE4rIoFmNdooK6fE0oWCcRg5vAOuaZNlzMCfpRFtNatSfhwZUXXfYCA==", "path": "volo.abp.automapper/4.3.0", "hashPath": "volo.abp.automapper.4.3.0.nupkg.sha512"}, "Volo.Abp.BackgroundJobs.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fXOJQadj8j7WYbm8gWZhdb1b0euOVbt0hwk2ov6078IdxN14mriDjmCNIg69C1LmNDc7gDs3L8YbswQlIUOsPA==", "path": "volo.abp.backgroundjobs.domain.shared/4.3.0", "hashPath": "volo.abp.backgroundjobs.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.BlobStoring/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-EGOqlOVqeTyiPrbd+SmwUlalW5mHDFbmHp2B7TAeeGW5rO3EVqkkQEcJVnIlguAVHrdNnGFE6Swf+XtNHjoNfA==", "path": "volo.abp.blobstoring/4.3.0", "hashPath": "volo.abp.blobstoring.4.3.0.nupkg.sha512"}, "Volo.Abp.BlobStoring.Database.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1d88ahE8dK9Y7SIq6vbtIdGmD1OHfpqmwpVdyH5NWKnJM5P4WkcuFheOR7S4Zp0/gRDTejxS0RMnyVrUSm8WmQ==", "path": "volo.abp.blobstoring.database.domain.shared/4.3.0", "hashPath": "volo.abp.blobstoring.database.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Caching/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-QYq+uERJoiWqGUmF5UhI3UB1SPoVV1AknnUelnBNVorfcPyT39CUhbHUlgUuCfSC2zpF5yvXyj+18ioOhht+kQ==", "path": "volo.abp.caching/4.3.0", "hashPath": "volo.abp.caching.4.3.0.nupkg.sha512"}, "Volo.Abp.Commercial.Core/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FCf1InEuqKPE36zhiy5biNtm3N+SlR9sztxLIHCJ5kCueYqFH9Te75Lj+G2dm3fLsE2Fmuvv+zmMBoNIhzeU5g==", "path": "volo.abp.commercial.core/4.3.0", "hashPath": "volo.abp.commercial.core.4.3.0.nupkg.sha512"}, "Volo.Abp.Commercial.SuiteTemplates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rw5/wElG4KB0JJ8Fd/WgmXh47AZXhN1UsujqzAFed8saUJCR4jav9M90bWbSrv+Eht6D5TPS7nHNzsge/VpCFw==", "path": "volo.abp.commercial.suitetemplates/4.3.0", "hashPath": "volo.abp.commercial.suitetemplates.4.3.0.nupkg.sha512"}, "Volo.Abp.Core/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GTXSY8W+AG5sMczOv40AR85eq9a+R/qI4Ci7h853rudprGWkwtfYw9OQnrVd3Xu74WceSTfCDtHWbu2AuJ3Q+w==", "path": "volo.abp.core/4.3.0", "hashPath": "volo.abp.core.4.3.0.nupkg.sha512"}, "Volo.Abp.Data/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2r42Sq03qH9z/B3fUZebbAeiEAPO3poHtzLslBhm4Qjjf9jlhBrkVjqZNkCDggNFNMVzRwWmrzSzIop8q8Crug==", "path": "volo.abp.data/4.3.0", "hashPath": "volo.abp.data.4.3.0.nupkg.sha512"}, "Volo.Abp.Ddd.Application/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CGyrh+hAV6RPCVk+yT+BUa2itmnjbSCkBZBrUhkFvEJior8BvLVbMT9DGOxoDE3pOzeIkcqdk2bog9Wiq19fkg==", "path": "volo.abp.ddd.application/4.3.0", "hashPath": "volo.abp.ddd.application.4.3.0.nupkg.sha512"}, "Volo.Abp.Ddd.Application.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ysaifHH09mGhQLxEBTAKu8F/GsKbZmcs78CIulkqliPM9qMtyqIuNDSPq/LvWbyRBfPWb/wFdHiBtcBGj2tZVw==", "path": "volo.abp.ddd.application.contracts/4.3.0", "hashPath": "volo.abp.ddd.application.contracts.4.3.0.nupkg.sha512"}, "Volo.Abp.Ddd.Domain/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nq5cMx0Uifg7GW8E8NedJZ7jaiPJv2Y9axYht5xbHEJ9gpsy1oovOp2VPKZ5Y5YYJY+M/1dlUEMAjM2LAANfpA==", "path": "volo.abp.ddd.domain/4.3.0", "hashPath": "volo.abp.ddd.domain.4.3.0.nupkg.sha512"}, "Volo.Abp.EventBus/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AjBsTwigVF/LJVJf/5JgwWUX7vRNvHR/HRxst4+mDS/VJZDsgqIZLwDOFnFuA+wKtOW49e9+wuaU/LLat9HPoQ==", "path": "volo.abp.eventbus/4.3.0", "hashPath": "volo.abp.eventbus.4.3.0.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DW2Cr8k/g2kWqxLBEZvBjT/BSpbUo3zw8uSBMaAGAAKn4k7cNp7FC3YESu0C3y45Nd8Lo/3JLc9Iw4NN5uFmSA==", "path": "volo.abp.eventbus.abstractions/4.3.0", "hashPath": "volo.abp.eventbus.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.ExceptionHandling/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7ht+TOh8YlBZ+06GQ9vXP0FEbCPh7suNqUeKkz034NNNBKYVga9LU6qIpSaquVsM4++8NI/5AZpNh+ocgGhUBw==", "path": "volo.abp.exceptionhandling/4.3.0", "hashPath": "volo.abp.exceptionhandling.4.3.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Application/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DwHeg8JPO4ZQAG7K8A2N0oMf2ChJ2vitKvF4Eiki2VQ2xISmyndRKpnopWhP/N7mV+iBlORGjw7AYkxpOWZ2uQ==", "path": "volo.abp.featuremanagement.application/4.3.0", "hashPath": "volo.abp.featuremanagement.application.4.3.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Application.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cCYP/q7Dbn43lBeaT9NLKumpD+19xJlMbw2129RjkC5sIj7fqSwuX20+yCY6R3yz1Rd0bmMG4vYRQ+dLgso8cQ==", "path": "volo.abp.featuremanagement.application.contracts/4.3.0", "hashPath": "volo.abp.featuremanagement.application.contracts.4.3.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Domain/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-huvba+96YGVINLqbwzpGwreIhqkAVGwhjwd9OA1tFLlNi17INSHocMyKhxU/vQIpyAkAT+UumtO8Pwr+I2NJ3g==", "path": "volo.abp.featuremanagement.domain/4.3.0", "hashPath": "volo.abp.featuremanagement.domain.4.3.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-aBtNfDG98wRuCGiNi0ylJRN+80p5t5R8Y0A2qFnYdCQAGbTE/6la6wzMc0Uvrjag9JnQldNmf9Q9qTSqWp5rSw==", "path": "volo.abp.featuremanagement.domain.shared/4.3.0", "hashPath": "volo.abp.featuremanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Features/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-8pHFDHqOQ54UEDciL8deBMg7nSIteTQWUzOT2ieuTl2WC8Mw198v3DYpXH1BTz0s9A9lM/i2h1qRVzVW+TMjrg==", "path": "volo.abp.features/4.3.0", "hashPath": "volo.abp.features.4.3.0.nupkg.sha512"}, "Volo.Abp.GlobalFeatures/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DvDZ+7aQzEB2gtcRBJVFYiobfU5Rzj3p6Wsbp8XdEOlYLQXP55Ga8wiUMtMH4crOzHYSwHbdPC1s2bHMgVM9rA==", "path": "volo.abp.globalfeatures/4.3.0", "hashPath": "volo.abp.globalfeatures.4.3.0.nupkg.sha512"}, "Volo.Abp.Guids/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Czq7YNU8zrSLQMx+8tpRbxjqN0eOmXAcr7XXy/qJSIoFLGCgh9xkH5CQhKmEFzl0vJFXf4DiHHEPAoRbW6mrhw==", "path": "volo.abp.guids/4.3.0", "hashPath": "volo.abp.guids.4.3.0.nupkg.sha512"}, "Volo.Abp.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXszHkbaNSbq12frlgGo/7IcolTjbb65KASaDYeLXlaJBkM5PRpzfgufHChgYlTT/QfX6V0B+xVXcSn4hf9H1w==", "path": "volo.abp.http/4.3.0", "hashPath": "volo.abp.http.4.3.0.nupkg.sha512"}, "Volo.Abp.Http.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Fy32oLk5amHZlObVzCz+FWDcjqd06IOhDSN10dg6Cueaefh/BRTd72tGSHPrEpKpjLLvnCU/L19ZIZd9DJAabQ==", "path": "volo.abp.http.abstractions/4.3.0", "hashPath": "volo.abp.http.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-s9/gdb8ZUXx7VjI2OE6hiBuPcnNpFTeWjEV5VcT5gPAYOXICdFj7LqiZdDypAAiM0RpthJoUpE4zQDRlg3ZN3g==", "path": "volo.abp.identity.domain.shared/4.3.0", "hashPath": "volo.abp.identity.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Identity.Pro.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GQcVxMqPAqmP+L4P+QtCzQ5GZl+zE35+RSuvndCkFuKi/Dl8hrIsLF6USUvFDb7SSoXVZSQhzrAvVbuXdWUa2w==", "path": "volo.abp.identity.pro.domain.shared/4.3.0", "hashPath": "volo.abp.identity.pro.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.IdentityServer.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbEHuIhHlFsM8ZQRo7rgQboU43N31DQX6Ceji27JrgIvCL6PdvcAu8ISAvX2nZUyo4NO1MLDmV+PI+cpuVf5NA==", "path": "volo.abp.identityserver.domain.shared/4.3.0", "hashPath": "volo.abp.identityserver.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Json/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yZJ/JjJeEO7vaQ/5vH1k0WUDPLjr7rkJ/JkhB6yjRDVirISPWL3y2CbTh6NPSyjTBShkaETCsOeAQBbjDvEbPQ==", "path": "volo.abp.json/4.3.0", "hashPath": "volo.abp.json.4.3.0.nupkg.sha512"}, "Volo.Abp.LanguageManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DcFGUIVg732oQ6FerPdhKfTOEwoVt2X+979gsC+WTJHkyUAv+ij2AAesR2ZRpn9NcoESG87yJgyYQo0FbBaqOQ==", "path": "volo.abp.languagemanagement.domain.shared/4.3.0", "hashPath": "volo.abp.languagemanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Localization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-d5yfg69jxE+rvyhjR1RoeF5KW0gNay2xnhixa6gAOXQ/8ZG7WmgU+G5Aa8GjgFzccL9XgFJYq7Ehzwtz3BS5ZA==", "path": "volo.abp.localization/4.3.0", "hashPath": "volo.abp.localization.4.3.0.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-8edOpld98PimnPODkrWtw/chW+K+1oAQUZccaO6F1DdnUsknQ03k6tXNvdVaXDQft74SsMcaf1tlHrvfrKPhZA==", "path": "volo.abp.localization.abstractions/4.3.0", "hashPath": "volo.abp.localization.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.Minify/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-i7XOHJmigo0XsG+Il0AHuwrGnFrdkkgFmAcjeziwMEo52W2FkGZG7DMDQ9n/TD+cBYLQCQ7ckDxxlBvZU51jeg==", "path": "volo.abp.minify/4.3.0", "hashPath": "volo.abp.minify.4.3.0.nupkg.sha512"}, "Volo.Abp.MultiTenancy/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/DU+5xg+7v889I86EguH+s/2C8wleVaphlTAuRgSPlWIujeOh6O7Dj+ImBb+LOEFK+6PvnzDyDCca57k2lwRsw==", "path": "volo.abp.multitenancy/4.3.0", "hashPath": "volo.abp.multitenancy.4.3.0.nupkg.sha512"}, "Volo.Abp.ObjectExtending/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBfao2YPtulP+ZaDrNe0Tx/K9bAiodv+gqYVWQ7UsrVloLIiQ1EMFv0Ln5zI9O111cgQHfLvEVYiFguP2GjSjA==", "path": "volo.abp.objectextending/4.3.0", "hashPath": "volo.abp.objectextending.4.3.0.nupkg.sha512"}, "Volo.Abp.ObjectMapping/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4QwdHZFbDiLMW4QNaQvDqjmHw5a3BEKhDmGUkWYwqNHVBTpwj/t7Fm+N0g1FXGjYE5muioko168pbsA8oPwDDw==", "path": "volo.abp.objectmapping/4.3.0", "hashPath": "volo.abp.objectmapping.4.3.0.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P4aQJiM7cmfkTSwADALSRDJbuJi6oLv2JXytmUqpxh5Qjp/xYafRWIuul7TurJY7Ps8RpAogEAkukqTfuFyoHA==", "path": "volo.abp.permissionmanagement.domain.shared/4.3.0", "hashPath": "volo.abp.permissionmanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Security/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HNShcU4urZUP4McuJFqv/X3m2tyJQkrruvuIbV+4MR9a817zo82BPqmzYG7RrZnG2Spyvqaa8GtXBfCGgFVUZA==", "path": "volo.abp.security/4.3.0", "hashPath": "volo.abp.security.4.3.0.nupkg.sha512"}, "Volo.Abp.Serialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YWnDgZbX1cvbKWKjs7reRjrxXfWNEFioHYxXxKhe/fAUbnBIA4lJWenMwsbZ5kioaiFZu6uQtj7qfs07AgxnuA==", "path": "volo.abp.serialization/4.3.0", "hashPath": "volo.abp.serialization.4.3.0.nupkg.sha512"}, "Volo.Abp.SettingManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wtv5TitvfXhR8+lOIUz1p4gAkcUxVZP8qeKW5SjaNqDFvZnJNZTj8ug+u9Hi8YfKxPpZ74n9gSZ7VUYWFwrDMQ==", "path": "volo.abp.settingmanagement.domain.shared/4.3.0", "hashPath": "volo.abp.settingmanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Settings/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-aCEhagDlTzoS+c/RI/geH7YlK+d3fYvQ/WRI75PLVJMMhMALXT25cZr71BsNwu00LLUnPf6xFfLkfwlsZfIaKA==", "path": "volo.abp.settings/4.3.0", "hashPath": "volo.abp.settings.4.3.0.nupkg.sha512"}, "Volo.Abp.Specifications/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/Yg1LiFm+JPX5sGTS/OPxTRvaaEaNlkvCAJAwjTC2CkjrdjPSKDEiB47tCWHYW0dIcIyEx/bV9L5inyLc2qAQ==", "path": "volo.abp.specifications/4.3.0", "hashPath": "volo.abp.specifications.4.3.0.nupkg.sha512"}, "Volo.Abp.TextTemplateManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NPQDCSdt2CMSWKMqxeJ8kGRvCeuvs0WQqao3OPFGFoz8i38NgMhtrTyxwzBld5MaxRHo5WHUldZ/o/68eERmFQ==", "path": "volo.abp.texttemplatemanagement.domain.shared/4.3.0", "hashPath": "volo.abp.texttemplatemanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HGZ7MNUNUamMKwhp9taCv8LTnsltA3WchiO0qIuf1BRAg3333muQqrtmraIB/k6T+1ecPoEIpxXzxwgMmTvvxg==", "path": "volo.abp.threading/4.3.0", "hashPath": "volo.abp.threading.4.3.0.nupkg.sha512"}, "Volo.Abp.Timing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ohLNz/1T1sKLFV0IXeqxrMlu2sf/qdM3Bj4hOzhse9MGB+jO2l8DKmzOOH70xpH6wWrRELjmRyOoR8U3jwc8Hg==", "path": "volo.abp.timing/4.3.0", "hashPath": "volo.abp.timing.4.3.0.nupkg.sha512"}, "Volo.Abp.UI/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I2e6XYnu+q0ljJ7h+9YNGDUh8GI6gC3K+WvjrXMQ8MJi9iEUDfWJXp6XpXWqT8swwNqHRk/Dw4BOpAhTdJF9gw==", "path": "volo.abp.ui/4.3.0", "hashPath": "volo.abp.ui.4.3.0.nupkg.sha512"}, "Volo.Abp.UI.Navigation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JQW6TZUwXaTbsXxdgz4RUIXkISEmowIRU2tFbSaE7werawUlhaWl1NlKgx4QXFCA7qCiO0SUlJCkZqai99t32g==", "path": "volo.abp.ui.navigation/4.3.0", "hashPath": "volo.abp.ui.navigation.4.3.0.nupkg.sha512"}, "Volo.Abp.Uow/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CwmJseLIBUcFsAiIVSr9WiNBcn/cqzJDCZmAYqzTLy7etETO+nMXSqohvAt6LlbwmjXHfMO5EZb6mYy4nnTbKQ==", "path": "volo.abp.uow/4.3.0", "hashPath": "volo.abp.uow.4.3.0.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-8Kqu7sjhWod7GuvqMYi5xYPbgKjDRBHE5MHDo3OF5FLHn2uCd597jlupvd6paSsrvDA2HgmpG88m6dkanmG+MQ==", "path": "volo.abp.users.domain.shared/4.3.0", "hashPath": "volo.abp.users.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Validation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SIXT7AImi3XRgC1XIs1QJhyEXNOEugYgpR8dpB17sIBcOBdkz9r4XO6lxlSarT5+vo7I9Qut8zmH9S2T76nNWg==", "path": "volo.abp.validation/4.3.0", "hashPath": "volo.abp.validation.4.3.0.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2dj1wA0YTVxA1wQQ0zE8R3a8nBk4fo/ibUYNV8aMRxkxX83e80BVNHi/5hMabG2ybHmTZrSLxquZyzwKvgSWkw==", "path": "volo.abp.validation.abstractions/4.3.0", "hashPath": "volo.abp.validation.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YL<PERSON>rsAsqFzDOIOAGmCk78KskDcwLR8EsDxRdtgnztxGIEfok7c3/39nwbpwrkFBrwN5ZP0Qoo03VjFc+49cgqg==", "path": "volo.abp.virtualfilesystem/4.3.0", "hashPath": "volo.abp.virtualfilesystem.4.3.0.nupkg.sha512"}, "Volo.Saas.Domain/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-wYbXIKES2twqfCIKQYTr1v3EJnJqdJM/kCk4aLvDdMrsM9ztzAkmlv8oufHm0jS4dbTeogX48HajC4hSnnJdUQ==", "path": "volo.saas.domain/4.3.0", "hashPath": "volo.saas.domain.4.3.0.nupkg.sha512"}, "Volo.Saas.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9MMtuAmDoingHJoE0o+GBLvMJ20Fn5fUXgW7TsogBB1cRvsjabs4qTy6H8pF7JszEIlzuWb+0cxWKqXZeKN/cA==", "path": "volo.saas.domain.shared/4.3.0", "hashPath": "volo.saas.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Saas.Host.Application/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-EoqOxL9LesEyzyltFovZUve9W14J5e9k9jmBWBfSbX9RWrK+FOe0mevJPsiCxFG9LeGUFER6Sin/297iEC16xg==", "path": "volo.saas.host.application/4.3.0", "hashPath": "volo.saas.host.application.4.3.0.nupkg.sha512"}, "Volo.Saas.Host.Application.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-wg8AzHM89se7h8PmBYyPCtgoXZl2/bppzcDOtpZx71xFrQy+J7tGp57JdNELXroqo5tjNl9ZP0Os10QWtCVk0Q==", "path": "volo.saas.host.application.contracts/4.3.0", "hashPath": "volo.saas.host.application.contracts.4.3.0.nupkg.sha512"}, "AOMS.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BondedModuleV2.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "GeneralModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "InboundModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "InboundModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.HttpApi/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "OutboundModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "OutboundModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ReportModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "SeaModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShareDataModule.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShareDataModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShareDataModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShareDataModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "TrayModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.Abp.LeptonTheme.Management.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.Chat.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.FileManagement.Application.Contracts/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.FileManagement.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.FileManagement.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/10.0.6.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Permissions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}