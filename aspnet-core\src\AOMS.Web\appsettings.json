{
  "Environment": "Development", // Development, UAT, Staging, Production, 
  "Redis": {
    "IsEnabled": "false",
    "Configuration": "127.0.0.1"
  },
  "App": {
    "SelfUrl": "https://localhost:44328"
  },
  "ConnectionStrings": {
    //"Default": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=*********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=pdb1)));User Id=HAN_W1_HL;Password=********;", //DB dev
    //"Default": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=*********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=pdb1)));User Id=AOMS_HOST;Password=********;"
    "Default": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=aomsdc)));User Id=AOMS_HOST;Password=*********;"
  },
  "AuthServer": {
    "Authority": "https://localhost:44328",
    "RequireHttpsMetadata": "false"
  },
  "StringEncryption": {
    "DefaultPassPhrase": "kbelv1jm5VYeKqSN"
  },
  "Settings": {
    "Volo.Abp.LeptonTheme.Style": "Style6", /* Options: Style1, Style2... Style6 */
    "Volo.Abp.LeptonTheme.Layout.MenuPlacement": "Left", /* Options: Left, Top */
    "Volo.Abp.LeptonTheme.Layout.MenuStatus": "AlwaysOpened", /* Options: AlwaysOpened, OpenOnHover */
    "Volo.Abp.LeptonTheme.Layout.Boxed": "False", /* Options: True, False */
    "AppPath": "https://drive.google.com/drive/folders/1mWVDqwqZXbcQP-K35pgClr1RUPB7B8VG?usp=sharing",
    "LoginExpireTimeSpanMinutes": 14400 //Minutes
  },
  "AbpLicenseCode": "PABMAGkAYwBlAG4AcwBlAD4ACgAgACAAPABJAGQAPgAyADYANwAzADEAMwAyADYALQA3ADAAMQBlAC0ANwAwAGEAOAAtAGEAMQBmADQALQAzADkAZgA5ADMAOQBlADgAZQBmAGIAMwA8AC8ASQBkAD4ACgAgACAAPABMAGkAYwBlAG4AcwBlAEEAdAB0AHIAaQBiAHUAdABlAHMAPgAKACAAIAAgACAAPABBAHQAdAByAGkAYgB1AHQAZQAgAG4AYQBtAGUAPQAiAE8AcgBnAGEAbgBpAHoAYQB0AGkAbwBuAEkAZAAiAD4AMgA2ADcAMwAxADMAMgA2AC0ANwAwADEAZQAtADcAMABhADgALQBhADEAZgA0AC0AMwA5AGYAOQAzADkAZQA4AGUAZgBiADMAPAAvAEEAdAB0AHIAaQBiAHUAdABlAD4ACgAgACAAIAAgADwAQQB0AHQAcgBpAGIAdQB0AGUAIABuAGEAbQBlAD0AIgBPAHIAZwBhAG4AaQB6AGEAdABpAG8AbgBOAGEAbQBlACIAPgBDAE8ATgBHACAAVABZACAAQwBPACAAUABIAEEATgAgAEwATwBHAEkAUwBUAEkAQwBTADwALwBBAHQAdAByAGkAYgB1AHQAZQA+AAoAIAAgACAAIAA8AEEAdAB0AHIAaQBiAHUAdABlACAAbgBhAG0AZQA9ACIAQwByAGUAYQB0AGkAbwBuAFQAaQBtAGUAIgA+ADIAMAAyADEALQAwADQALQAyADkAIAAwADcAOgA1ADEAOgA1ADcAPAAvAEEAdAB0AHIAaQBiAHUAdABlAD4ACgAgACAAPAAvAEwAaQBjAGUAbgBzAGUAQQB0AHQAcgBpAGIAdQB0AGUAcwA+AAoAIAAgADwAUwBpAGcAbgBhAHQAdQByAGUAPgBNAEUAUQBDAEkASAA5AGIAQQBPAGsATAA4AEoANQBoAEgAKwBYAEcAbwBnAHgAWABLADEAdwBFAFAASgAvAHYAOQBBADIAVABNAG8AQwBaADQATABXAGUAMQBVAFMAWgBBAGkAQQB0AHcAQQB3ACsAdABKAGcARABMAFAAdABVAEUAUABzAC8AMwBvAE8ALwBSAGcANwA4ADcAaQBRAGYAeQBuAHIAcAB3AGIAVQBTAGsAVgBJAFkANgBBAD0APQA8AC8AUwBpAGcAbgBhAHQAdQByAGUAPgAKADwALwBMAGkAYwBlAG4AcwBlAD4ATQBGAGsAdwBFAHcAWQBIAEsAbwBaAEkAegBqADAAQwBBAFEAWQBJAEsAbwBaAEkAegBqADAARABBAFEAYwBEAFEAZwBBAEUAVABoAFEAVQB3AGIAeQBxAHAAMABxAHYAWgBWAHAAegBmAEcAcwBIAGMATgBpAEsAWQBCAEEAYQAyAHkAVgA4AGkARgBoAGIAbABIADEAOQBXAHkAMwB0AFcASwBHAE4AcABJAGgAdQB2AG4ANgBHAFYANQA3AEkASgA3ADUAYQBEAFYAUgBuAEMAeQArAGUARwBjAEkARQB5AFIARQBsAEUAMwBBAE4ANAB3AD0APQBfADEAMgA0AA==",
  // Azure settings here
  "AzureStorageAccountSettings": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=aomsstorage01;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "ContainersUseAzure": false
  },
  "FileSysBasePath": "aoms-files",
  "Api_HQ1C": "http://hq1c.alsc.com.vn/api/kvgs?sdd={0}&type={1}",
  // config riêng cho tenant ALSW_DEV
  "ALSW_UAT": {
    "ReportPath": {
      "ReportPathBase": "https://aoms-uat.alssys.vn",
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/ALSW_UAT/"
    },
    // Template import excel file name settings here
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "4601157771",
      "TEN_DV": "CÔNG TY CỔ PHẦN TIẾP VẬN THẾ KỶ",
      "DIA_CHI": "LÔ CN 1-1, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ ĐÔNG PHONG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!"
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "0106232917.admin",
      "PasswordEInvoiceALSW": "admin@123",
      "InvoiceFieldTypeALSW": "01GTKT",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "https://api-uat.einvoice.fpt.com.vn/appr-invoice",
      "URL_SearchPDF": "https://api-einvoice.xplat.fpt.com.vn/search-invoice?stax=0106232917",
      "URL_SearchPDF_ALSX": "https://api-uat.einvoice.fpt.com.vn/search-invoice",
      "URL_Cancel": "https://api-uat.einvoice.fpt.com.vn/cancel-invoice",
      "InvoiceFieldSerialALSW-MD": "K22TAA",
      "InvoiceFieldSerialALSW-GL": "K22TAA",
      "StaxALSW": "0106232917",
      "SeqCancelALSW": "01GTKT0/002-AA/21E-",
      "SeqCancelALSW_IMPORT": "01GTKT0/001-AB/21E-",
      "SeqCancel": "01GTKT0/001-AH/21E-",
      "VAT": "10",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440 // 24 hour = 1440 minutes
    },
    "Pod": {
      "Count": "6",
      "UpdateDate": "05/01/2023"
    },
    "PodBonded": {
      "Count": "6",
      "UpdateDate": "05/01/2023"
    },
    "PodGeneral": {
      "Count": "6",
      "UpdateDate": "05/01/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ]
  },
  "ALSW_DEV": {
    "ReportPath": {
      "ReportPathBase": "https://aoms.alssys.vn",
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/ALSW_DEV/"
    },
    // Template import excel file name settings here
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "4601157771",
      "TEN_DV": "CÔNG TY CỔ PHẦN TIẾP VẬN THẾ KỶ",
      "DIA_CHI": "LÔ CN 1-1, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ ĐÔNG PHONG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!"
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "0106232917.admin",
      "PasswordEInvoiceALSW": "admin@123",
      "InvoiceFieldTypeALSW": "01GTKT",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "https://api-uat.einvoice.fpt.com.vn/appr-invoice",
      "URL_SearchPDF": "https://api-einvoice.xplat.fpt.com.vn/search-invoice?stax=0106232917",
      "URL_SearchPDF_ALSX": "https://api-uat.einvoice.fpt.com.vn/search-invoice",
      "URL_Cancel": "https://api-uat.einvoice.fpt.com.vn/cancel-invoice",
      "InvoiceFieldSerialALSW-MD": "K22TAA",
      "InvoiceFieldSerialALSW-GL": "K22TAA",
      "StaxALSW": "0106232917",
      "SeqCancelALSW": "01GTKT0/002-AA/21E-",
      "SeqCancelALSW_IMPORT": "01GTKT0/001-AB/21E-",
      "SeqCancel": "01GTKT0/001-AH/21E-",
      "VAT": "10",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440 // 24 hour = 1440 minutes
    },
    "Pod": {
      "Count": "6",
      "UpdateDate": "05/01/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ]
  },
  "CLC_UAT": {
    "ReportPath": {
      "ReportPathBase": "https://aoms-uat.alssys.vn",
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/CLC_UAT/"
    },
    // Template import excel file name settings here
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "4601157771",
      "TEN_DV": "CÔNG TY CỔ PHẦN TIẾP VẬN THẾ KỶ",
      "DIA_CHI": "LÔ CN 1-1, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ ĐÔNG PHONG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!"
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "0106232917.admin",
      "PasswordEInvoiceALSW": "admin@123",
      "InvoiceFieldTypeALSW": "01GTKT",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "https://api-uat.einvoice.fpt.com.vn/appr-invoice",
      "URL_SearchPDF": "https://api-einvoice.xplat.fpt.com.vn/search-invoice?stax=0106232917",
      "URL_SearchPDF_ALSX": "https://api-uat.einvoice.fpt.com.vn/search-invoice",
      "URL_Cancel": "https://api-uat.einvoice.fpt.com.vn/cancel-invoice",
      "InvoiceFieldSerialALSW-MD": "K22TAA",
      "InvoiceFieldSerialALSW-GL": "K22TAA",
      "StaxALSW": "0106232917",
      "SeqCancelALSW": "01GTKT0/002-AA/21E-",
      "SeqCancelALSW_IMPORT": "01GTKT0/001-AB/21E-",
      "SeqCancel": "01GTKT0/001-AH/21E-",
      "VAT": "10",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440 // 24 hour = 1440 minutes
    },
    "Pod": {
      "Count": "6",
      "UpdateDate": "05/01/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ]
  },
  "CLC_DEV": {
    "ReportPath": {
      "ReportPathBase": "https://aoms-uat.alssys.vn",
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/CLC_DEV/"
    },
    // Template import excel file name settings here
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "4601157771",
      "TEN_DV": "CÔNG TY CỔ PHẦN TIẾP VẬN THẾ KỶ",
      "DIA_CHI": "LÔ CN 1-1, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ ĐÔNG PHONG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!"
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "0106232917.admin",
      "PasswordEInvoiceALSW": "admin@123",
      "InvoiceFieldTypeALSW": "01GTKT",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "https://api-uat.einvoice.fpt.com.vn/appr-invoice",
      "URL_SearchPDF": "https://api-einvoice.xplat.fpt.com.vn/search-invoice?stax=0106232917",
      "URL_SearchPDF_ALSX": "https://api-uat.einvoice.fpt.com.vn/search-invoice",
      "URL_Cancel": "https://api-uat.einvoice.fpt.com.vn/cancel-invoice",
      "InvoiceFieldSerialALSW-MD": "K22TAA",
      "InvoiceFieldSerialALSW-GL": "K22TAA",
      "StaxALSW": "0106232917",
      "SeqCancelALSW": "01GTKT0/002-AA/21E-",
      "SeqCancelALSW_IMPORT": "01GTKT0/001-AB/21E-",
      "SeqCancel": "01GTKT0/001-AH/21E-",
      "VAT": "10",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440 // 24 hour = 1440 minutes
    },
    "Pod": {
      "Count": "6",
      "UpdateDate": "05/01/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ]
  },
  "ALSB_UAT": {
    "ReportPath": {
      "ReportPathBase": "https://aoms-uat.alssys.vn",
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/ALSB_UAT/"
    },
    // Template import excel file name settings here
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx",
      "InboundImportOla": "Template_Inbound_Import_Ola_ALSB.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "4601157771",
      "TEN_DV": "CÔNG TY CỔ PHẦN TIẾP VẬN THẾ KỶ",
      "DIA_CHI": "LÔ CN 1-1, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ ĐÔNG PHONG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!"
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "0106232917.admin",
      "PasswordEInvoiceALSW": "admin@123",
      "InvoiceFieldTypeALSW": "01GTKT",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "https://api-uat.einvoice.fpt.com.vn/appr-invoice",
      "URL_SearchPDF": "https://api-einvoice.xplat.fpt.com.vn/search-invoice?stax=0106232917",
      "URL_SearchPDF_ALSX": "https://api-uat.einvoice.fpt.com.vn/search-invoice",
      "URL_Cancel": "https://api-uat.einvoice.fpt.com.vn/cancel-invoice",
      "InvoiceFieldSerialALSW-MD": "K22TAA",
      "InvoiceFieldSerialALSW-GL": "K22TAA",
      "StaxALSW": "0106232917",
      "SeqCancelALSW": "01GTKT0/002-AA/21E-",
      "SeqCancelALSW_IMPORT": "01GTKT0/001-AB/21E-",
      "SeqCancel": "01GTKT0/001-AH/21E-",
      "VAT": "10",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440 // 24 hour = 1440 minutes
    },
    "Pod": {
      "Count": "6",
      "UpdateDate": "05/01/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ]
  },
  "ALSB_DEV": {
    "ReportPath": {
      "ReportPathBase": "https://aoms-uat.alssys.vn",
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/ALSB_DEV/"
    },
    // Template import excel file name settings here
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx",
      "InboundImportOla": "Template_Inbound_Import_Ola_ALSB.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "4601157771",
      "TEN_DV": "CÔNG TY CỔ PHẦN TIẾP VẬN THẾ KỶ",
      "DIA_CHI": "LÔ CN 1-1, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ ĐÔNG PHONG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!"
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "0106232917.admin",
      "PasswordEInvoiceALSW": "admin@123",
      "InvoiceFieldTypeALSW": "01GTKT",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "https://api-uat.einvoice.fpt.com.vn/appr-invoice",
      "URL_SearchPDF": "https://api-einvoice.xplat.fpt.com.vn/search-invoice?stax=0106232917",
      "URL_SearchPDF_ALSX": "https://api-uat.einvoice.fpt.com.vn/search-invoice",
      "URL_Cancel": "https://api-uat.einvoice.fpt.com.vn/cancel-invoice",
      "InvoiceFieldSerialALSW-MD": "K22TAA",
      "InvoiceFieldSerialALSW-GL": "K22TAA",
      "StaxALSW": "0106232917",
      "SeqCancelALSW": "01GTKT0/002-AA/21E-",
      "SeqCancelALSW_IMPORT": "01GTKT0/001-AB/21E-",
      "SeqCancel": "01GTKT0/001-AH/21E-",
      "VAT": "10",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440 // 24 hour = 1440 minutes
    },
    "Pod": {
      "Count": "6",
      "UpdateDate": "05/01/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ]
  },
  "ALSB": {
    "ReportPath": {
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/ALSB/"
    },
    "QRCodePrint": {
      "URL": "https://aoms-alsb.alssys.vn?PalletNo="
    },
    // Template import excel file name settings here
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx",
      "MasterDataDestManage": "Template_import_Dest.xlsx",
      "OutboundSLI": "Template_Outbound_SLI.xlsx",
      "InboundImportOla": "Template_Inbound_Import_Ola_ALSB.xlsx"
    },
    "TemplateExportExcelFileName": {
      "SeaboundContainerMap": "Template_Seabound_Container_Map.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "2300478023",
      "TEN_DV": "CÔNG TY TRÁCH NHIỆM HỮU HẠN ALS BẮC NINH",
      "DIA_CHI": "CN05, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ YÊN TRUNG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!" //9fe6973faad2453330dd167d49117383
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "0106232917.admin",
      "PasswordEInvoiceALSW": "admin@123",
      "InvoiceFieldTypeALSW": "01GTKT",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "https://api-uat.einvoice.fpt.com.vn/appr-invoice",
      "URL_SearchPDF": "https://api-einvoice.xplat.fpt.com.vn/search-invoice?stax=0106232917",
      "URL_SearchPDF_ALSX": "https://api-uat.einvoice.fpt.com.vn/search-invoice",
      "URL_Cancel": "https://api-uat.einvoice.fpt.com.vn/cancel-invoice",
      "InvoiceFieldSerialALSW-MD": "K22TAA",
      "InvoiceFieldSerialALSW-GL": "K22TAA",
      "StaxALSW": "0106232917",
      "SeqCancelALSW": "01GTKT0/002-AA/21E-",
      "SeqCancelALSW_IMPORT": "01GTKT0/001-AB/21E-",
      "SeqCancel": "01GTKT0/001-AH/21E-",
      "VAT": "10",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440 // 24 hour = 1440 minutes
    },
    "Pod": {
      "Count": "3",
      "UpdateDate": "24/03/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ],
    "HawbSplit": {
      "DHL": {
        "MAD": {
          "Left": 7,
          "RemoveCharacter": "H"
        }
      },
      "HTNS": {
        "TSN": {
          "Left": 11
        },
        "VIE": {
          "Left": 10
        },
        "CAN": {
          "Left": 10
        },
        "HXG": {
          "Left": 10
        }
      },
      "DSV": {
        "AMS": {
          "Left": 11
        },
        "GRU": {
          "Left": 11
        },
        "SCL": {
          "Left": 11
        }
      },
      "EI": {
        "HKG": {
          "Left": 9
        },
        "MNL": {
          "Left": 9
        },
        "DXB": {
          "Left": 10
        },
        "LAX": {
          "Left": 10
        },
        "PVG": {
          "Left": 10
        },
        "ORD": {
          "Left": 10
        },
        "JFK": {
          "Left": 10
        },
        "YYZ": {
          "Left": 10
        },
        "DFW": {
          "Left": 10
        }
      }
    },
    "TemplateExportExcel": {
      "OutboundKHCX": "Template_Outbound_KHCX_ALSB.xlsx",
      "BBBT": "Template_BBBT_ALSB.xlsx",
      "InboundPodDelivery": "Template_Inbound_Pod_ALSB.xlsx",
      "BondedReportInventory": "Template_Bonded_ReportInventory.xlsx",
      "BondedPodDelivery": "Template_Bonded_Pod_ALSB.xlsx",
      "GeneralPodDelivery": "Template_General_Pod_ALSB.xlsx",
      "BondedWarehouseReceipt": "Template_Bonded_WarehouseReceipt_ALSB.xlsx",
      "OutboundPodReturn": "Template_Outbound_Pod_Return_ALSB.xlsx",
      "InboundDlvBill": "Template_Inbound_PXK_ALSB.xlsx",
      "OutboundBBCheckDO": "Template_Outbound_Bien_ban_kiem_tra_DO_ALSB.xlsx",
      "OutboundGroundShippingList": "Template_Outbound_DS_VC_Mat_Dat_ALSB.xlsx",
      "SeaStuffingCheckList": "Template_Sea_Stuffing_Check_List_ALSB.xlsx",
      "SeaBBCX": "Template_Sea_BBCX_ALSB.xlsx",
      "SeaChecklistCont": "Template_Sea_Checklist_Cont_ALSB.xlsx",
      "OutboundDailyMiningReport": "Template_Outbound_Daily_Mining_Report.xlsx"
    },
    "TemplateHtmlReport": {
      "InboundDlvBill": "Template_Inbound_PXK_ALSB.html"
    }
  },
  "CLC": {
    "TemplateExportExcel": {
      "OutboundKHCX": "Template_Outbound_KHCX_CLC.xlsx",
      "BBBT": "Template_BBBT_CLC.xlsx",
      "InboundPodDelivery": "Template_Inbound_Pod_CLC.xlsx",
      "TrayPodDelivery": "Template_Tray_Pod_CLC.xlsx",
      "TrayReportInventory": "Template_Report_Tray_CLC.xlsx"
    },
    "ReportPath": {
      "ReportPathBase": "https://aoms-uat.alssys.vn",
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/CLC/"
    },
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "4601157771",
      "TEN_DV": "CÔNG TY CỔ PHẦN TIẾP VẬN THẾ KỶ",
      "DIA_CHI": "LÔ CN 1-1, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ ĐÔNG PHONG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!"
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "0106232917.admin",
      "PasswordEInvoiceALSW": "admin@123",
      "InvoiceFieldTypeALSW": "01GTKT",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "https://api-uat.einvoice.fpt.com.vn/appr-invoice",
      "URL_SearchPDF": "https://api-einvoice.xplat.fpt.com.vn/search-invoice?stax=0106232917",
      "URL_SearchPDF_ALSX": "https://api-uat.einvoice.fpt.com.vn/search-invoice",
      "URL_Cancel": "https://api-uat.einvoice.fpt.com.vn/cancel-invoice",
      "InvoiceFieldSerialALSW-MD": "K22TAA",
      "InvoiceFieldSerialALSW-GL": "K22TAA",
      "StaxALSW": "0106232917",
      "SeqCancelALSW": "01GTKT0/002-AA/21E-",
      "SeqCancelALSW_IMPORT": "01GTKT0/001-AB/21E-",
      "SeqCancel": "01GTKT0/001-AH/21E-",
      "VAT": "10",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440
    },
    "Pod": {
      "Count": "4",
      "UpdateDate": "26/05/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ]
  },
  "ALSW": {
    "ReportPath": {
      "ReportPathBase": "https://aoms.alssys.vn",
      "BirtViewerPath": "/birt-viewer/output?__report=",
      "FolderPath": "report/"
    },
    "TemplateImportExcelFileName": {
      "OutboundReceivingPlanImportMAWB": "Template_Outbound_ReceivingPlan_ImportMAWB.xlsx",
      "InboundPickUpPlan": "Template_Inbound_PickUpPlan_ALSx.xlsx",
      "InboundDeliveryPlan": "Template_Inbound_DeliveryPlan.xlsx",
      "InboundWarehouseCDUpdate": "Template_Inbound_Warehouse_CD_Update.xlsx",
      "InboundWarehouseClassUpdate": "Template_Inbound_Warehouse_CLASS_Update.xlsx",
      "OutboundImportPODO": "Template_Outbound_Import_PO_DO.xlsx",
      "InboundForecast": "Template_Outbound_Forecast_ALSB.xlsx"
    },
    "ApiPath": "https://aoms-share.alssys.vn",
    "TokenParams": {
      "ClientId": "AOMSACC_App",
      "ClientSecret": "E5Xd4yMqjP5kjWFKrYgySBju6JVfCzMyFp7n2QmMrME=",
      "Scope": "AOMSACC"
    },
    "OLA": {
      "MA_DV": "4601157771",
      "TEN_DV": "CÔNG TY CỔ PHẦN TIẾP VẬN THẾ KỶ",
      "DIA_CHI": "LÔ CN 1-1, ĐƯỜNG YP6, KCN YÊN PHONG, XÃ ĐÔNG PHONG, HUYỆN YÊN PHONG, TỈNH BẮC NINH, VIỆT NAM"
    },
    "EcusAuth": {
      "UserName": "********",
      "Password": "alsvn@)@!"
    },
    "E_Invoice": {
      "UserNameEInvoiceALSW": "",
      "PasswordEInvoiceALSW": "",
      "InvoiceFieldTypeALSW": "",
      "InvoiceFieldFormALSW": "1",
      "URL_Appr": "",
      "URL_SearchPDF": "",
      "URL_SearchPDF_ALSX": "",
      "URL_Cancel": "",
      "InvoiceFieldSerialALSW-MD": "",
      "InvoiceFieldSerialALSW-GL": "",
      "StaxALSW": "",
      "SeqCancelALSW": "",
      "SeqCancelALSW_IMPORT": "",
      "SeqCancel": "",
      "VAT": "",
      "Email-Test-UAT": "<EMAIL>",
      "CancelTime": 1440
    },
    "Pod": {
      "Count": "4",
      "UpdateDate": "26/05/2023"
    },
    "DefaultGoodClasses": [
      {
        "Code": "AVI",
        "Name": "HÀNG ĐỘNG VẬT SỐNG",
        "IsDefault": false
      },
      {
        "Code": "DGR",
        "Name": "HÀNG NGUY HIỂM",
        "IsDefault": false
      },
      {
        "Code": "DIP",
        "Name": "DIP",
        "IsDefault": false
      },
      {
        "Code": "EWS",
        "Name": "EWS",
        "IsDefault": false
      },
      {
        "Code": "FRZ",
        "Name": "HÀNG BẢO QUẢN LẠNH",
        "IsDefault": false
      },
      {
        "Code": "GCR",
        "Name": "HÀNG THÔNG THƯỜNG",
        "IsDefault": true
      },
      {
        "Code": "HEA",
        "Name": "HÀNG NẶNG",
        "IsDefault": false
      },
      {
        "Code": "HUM",
        "Name": "HÀNG QUAN TÀI",
        "IsDefault": false
      },
      {
        "Code": "OHG",
        "Name": "OHG",
        "IsDefault": false
      },
      {
        "Code": "PER",
        "Name": "HÀNG TƯƠI SỐNG",
        "IsDefault": false
      },
      {
        "Code": "POS",
        "Name": "POS",
        "IsDefault": false
      },
      {
        "Code": "VAN",
        "Name": "VAN",
        "IsDefault": false
      },
      {
        "Code": "VUN",
        "Name": "HÀNG KHÓ BẢO QUẢN",
        "IsDefault": false
      },
      {
        "Code": "VAL",
        "Name": "HÀNG GIÁ TRỊ",
        "IsDefault": false
      }
    ]
  }
}
