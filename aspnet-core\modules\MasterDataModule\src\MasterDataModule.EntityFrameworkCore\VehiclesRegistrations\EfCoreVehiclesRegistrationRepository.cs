using MasterDataModule.EntityFrameworkCore;
using MasterDataModule.WareHousePickups;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OutboundModule.DoPoManages;
using OutboundModule.GroupPrcvHistories;
using ShareDataModule.Shared;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace MasterDataModule.VehiclesRegistrations
{
    public class EfCoreVehiclesRegistrationRepository : EfCoreRepository<MasterDataModuleDbContext, VehiclesRegistration, long>, IVehiclesRegistrationRepository
    {
        IGroupPrcvHistoryRepository _groupPrcvHistoryRepository;
        public EfCoreVehiclesRegistrationRepository(IDbContextProvider<MasterDataModuleDbContext> dbContextProvider,
            IGroupPrcvHistoryRepository groupPrcvHistoryRepository)
            : base(dbContextProvider)
        {
            _groupPrcvHistoryRepository = groupPrcvHistoryRepository;
        }

        public async Task<List<VehiclesRegistration>> GetListAsync(
            string filterText = null,
            string vehicRegNo = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter(await GetQueryableAsync(), filterText, vehicRegNo);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? VehiclesRegistrationConsts.GetDefaultSorting(false) : sorting);

            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string vehicRegNo = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter(await GetDbSetAsync(), filterText, vehicRegNo);

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<VehiclesRegistration> ApplyFilter(
            IQueryable<VehiclesRegistration> query,
            string filterText,
            string vehicRegNo = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.VehicRegNo.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(vehicRegNo), e => e.VehicRegNo.Contains(vehicRegNo));
        }

        public async Task<VehiclesRegistrationStatus> GetVehicleRegStatus(long vehicleRegId = 0)
        {
            string status = string.Empty;
            var VehicleReg = from vr in (await GetDbSetAsync()) where vr.Id == vehicleRegId select vr;
            var ReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldDeleted == false select v;
            if (ReadyToload.Count() == 0)
            {
                status = "Ready to load";
            }
            else
            {
                var CountReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldQtyreceived > 0 && v.VhldDeleted == false select v;
                if (CountReadyToload.Count() == 0)
                {
                    status = "Ready to load";
                }
                else
                {
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == false)
                    {
                        status = "Loading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == true)
                    {
                        status = "Closed";
                    }
                    if (VehicleReg.FirstOrDefault().VhclLoadingLeftDate != null)
                    {
                        status = "In Transit";
                    }
                    if (VehicleReg.FirstOrDefault().VhclVehicleComplete == true)
                    {
                        status = "Delivered";
                    }
                }
            }

            return new()
            {
                Status = status
            };
        }

        private async Task<IQueryable<VehiclesCargoTerminal>> GetQueryVehiclesCargoTerminal(
            long vehicleRegId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string status = null,
            string loadingArrivalDate = null)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(loadingArrivalDate) &&
                    DateTime.TryParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
            {
                startOfDay = date.Date;
                endOfDay = date.Date.AddDays(1).AddTicks(-1);
            }

            var query = from v in (await GetDbSetAsync())
                        join vh in (await GetDbContextAsync()).Vehicles
                           on v.VhclMasterIsn equals vh.Id
                        where v.VhclImportExport == "EXPORT" && v.VhclLoadingLeftDate != null && v.VhclTruckType == "TRANSIT"
                        orderby v.VhclLoadingArrivalDate descending
                        select new VehiclesCargoTerminal
                        {
                            Id = v.Id,
                            VehicleId = v.VhclMasterIsn,
                            VehicRegNo = v.VehicRegNo,
                            VehicleType = vh.VehicleType,
                            ShedWarehouse = v.VhclUnloadingWarehouse,
                            LoadingLeftDate = v.VhclLoadingLeftDate,
                            VhclLoadingLeftTime = v.VhclLoadingLeftTime,
                            UnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            UnloadingActivationDate = v.VhclUnloadingActivationDate,
                            CompleteDate = v.VhclCompletedDate,
                            LoadingArrivalDate = v.VhclLoadingArrivalDate,
                            Status = (bool)v.VhclVehicleComplete ? "Completed" : (v.VhclUnloadingActivationDate != null ? "Unloading" : (v.VhclUnloadingArrivalDate != null ? "Arrived Terminal" : (v.VhclLoadingLeftDate != null ? "In Transit" : "")))
                        };

            return query.WhereIf(vehicleRegId > 0, e => e.Id == vehicleRegId)
                         .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                         .WhereIf(!string.IsNullOrWhiteSpace(shedWareHouse), e => e.ShedWarehouse.Contains(shedWareHouse))
                         .WhereIf(!string.IsNullOrWhiteSpace(loadingArrivalDate), x => x.LoadingArrivalDate >= startOfDay && x.LoadingArrivalDate <= endOfDay)
                         .WhereIf(!string.IsNullOrWhiteSpace(status), e => e.Status.ToLower() == status.ToLower());
        }

        public async Task<List<VehiclesCargoTerminal>> GetVehiclesCargoTerminalListAsync(
            long vehicleRegId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehiclesCargoTerminal(vehicleRegId, vehicleRegNo, shedWareHouse, status, loadingArrivalDate);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountVehiclesCargoTerminalAsync(
            long vehicleRegId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehiclesCargoTerminal(vehicleRegId, vehicleRegNo, shedWareHouse, status, loadingArrivalDate);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<VehiclesLoading>> GetVehiclesLoadingListAsync(
            string keyword = null,
            long vehicleRegId = 0,
            long customerId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string wareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string type = null,
            string truckType = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(loadingArrivalDate))
            {
                if (DateTime.TryParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startOfDay = date.Date;
                    endOfDay = date.Date.AddDays(1).AddTicks(-1);
                }
                else
                {
                    startOfDay = DateTime.Now.Date.AddYears(-10);
                    endOfDay = DateTime.Now.Date.AddDays(1).AddTicks(-1);
                }
            }

            var query = from v in (await GetDbSetAsync())
                            //join w in (await GetDbContextAsync()).ErtsRemoteTransitSheds
                            //on v.VhclUnloadingWarehouse equals w.Id.ToString()

                        join warehouse in (await GetDbContextAsync()).WareHouses
                        on v.VhclLoadingWarehouse equals warehouse.Id.ToString()

                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        where v.VhclImportExport == type && v.VhclTruckType == truckType

                        join pickup in (await GetDbContextAsync()).WareHousePickups
                        on v.VhclWareHousePickupIsn equals pickup.Id
                        into pick
                        from p in pick.DefaultIfEmpty()

                        join kund in (await GetDbContextAsync()).Kunds
                        on p.FactoryId equals kund.Id
                        into temp
                        from k in temp.DefaultIfEmpty()

                        orderby v.VhclLoadingArrivalDate descending
                        select new VehiclesLoading
                        {
                            Id = v.Id,
                            VehicleId = v.VhclMasterIsn,
                            VehicRegNo = v.VehicRegNo,
                            VehicleLoadWeight = vh.VehicleLoadWeight,
                            VehicleType = vh.VehicleType,
                            ShedWarehouse = v.VhclUnloadingWarehouse,
                            Warehouse = warehouse.WarehouseName,
                            DriverName = v.VhclDriverName,
                            UnloadingArrivalDatetime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclUnloadingArrivalDate),
                            LoadingArrivalDatetime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingArrivalDate),
                            VhclLoadingVehicleClosed = v.VhclLoadingVehicleClosed,
                            VhclLoadingLeftDate = v.VhclLoadingLeftDate,
                            VhclVehicleComplete = v.VhclVehicleComplete,
                            LoadingArrivalDate = v.VhclLoadingArrivalDate,
                            UnloadingActivationDate = v.VhclUnloadingActivationDate,
                            UnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            TransitToFactoryDate = v.VhclTransitToFactoryDate,
                            ArrivedFactoryDate = v.VhclArrivedFactoryDate,
                            CustomerName = k.KundName1,
                            CustomerId = k.Id,
                            CustomerCode = k.Kund3letterCode,
                            WarehousePickup = p.Name
                        };

            query = query.WhereIf(!string.IsNullOrWhiteSpace(keyword), e => e.VehicRegNo.ToUpper().Contains(keyword.ToUpper()) || e.ShedWarehouse.ToUpper().Contains(keyword.ToUpper()) || e.Id.ToString() == keyword)
                       .WhereIf(vehicleRegId > 0, e => e.Id == vehicleRegId)
                       .WhereIf(customerId > 0, x => x.CustomerId == customerId)
                       .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                       .WhereIf(!string.IsNullOrWhiteSpace(shedWareHouse), e => e.ShedWarehouse.Contains(shedWareHouse))
                       .WhereIf(!string.IsNullOrWhiteSpace(wareHouse), e => e.Warehouse.Contains(wareHouse))
                       .WhereIf(!string.IsNullOrWhiteSpace(loadingArrivalDate), x => x.LoadingArrivalDate >= startOfDay && x.LoadingArrivalDate <= endOfDay);
            var listItem = query.ToList();
            foreach (var item in listItem)
            {
                //Status: Ready to load
                var ReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == item.Id && v.VhldDeleted == false select v.Id;
                if (ReadyToload.Count() == 0)
                {
                    item.Status = "Ready to load";
                }
                else
                {
                    var CountReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == item.Id && v.VhldQtyreceived > 0 && v.VhldDeleted == false select v.Id;
                    if (CountReadyToload.Count() == 0)
                    {
                        item.Status = "Ready to load";
                    }
                    else
                    {
                        if (item.VhclLoadingVehicleClosed == false)
                        {
                            item.Status = "Loading";
                        }
                        if (item.VhclLoadingVehicleClosed == true)
                        {
                            item.Status = "Closed";
                        }
                        if (item.VhclLoadingLeftDate != null)
                        {
                            item.Status = "In Transit";
                        }
                        if (item.UnloadingArrivalDate != null)
                        {
                            item.Status = "Arrived Terminal";
                        }
                        if (item.UnloadingActivationDate != null)
                        {
                            item.Status = "Unloading";
                        }
                        if (item.VhclVehicleComplete == true)
                        {
                            item.Status = "Completed";
                        }
                        if (item.TransitToFactoryDate != null)
                        {
                            item.Status = "Transit To Factory";
                        }
                        if (item.ArrivedFactoryDate != null)
                        {
                            item.Status = "Arrived Factory";
                        }
                    }
                }
            }

            var list = listItem.WhereIf(!string.IsNullOrWhiteSpace(status), e => e.Status.ToLower() == status.ToLower()).Skip(skipCount).Take(maxResultCount).ToList();
            foreach (var item in list)
            {
                var data = await _groupPrcvHistoryRepository.GetTotalPcsWeightListByVehicle(item.Id);
                if (data.Any())
                {
                    var totalPcs = data.Select(p=>new
                    {
                        DoNo = p.DoNo,
                        ReceivedPieces=p.ReceivedPieces
                    }).Distinct().ToList().Sum(p => p.ReceivedPieces);
                    item.TotalDO = data.Select(x => x.DoNo).Distinct().Count();
                    item.Pieces = (int)totalPcs.Value;
                    item.PcsLoaded = (int)data.Sum(x => x.ReceivedPieces);
                    item.Weight = (decimal)data.Sum(x => x.ReceivedGrossWeight);
                }
            }

            return list;
        }

        protected virtual IQueryable<VehiclesLoading> ApplyFilterTruck(
            IQueryable<VehiclesLoading> query,
            long vehicleRegId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string status = null,
            string loadingArrivalDate = null)
        {
            return query
                    .WhereIf(vehicleRegId > 0, e => e.Id == vehicleRegId)
                    .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                    .WhereIf(!string.IsNullOrWhiteSpace(shedWareHouse), e => e.ShedWarehouse.Contains(shedWareHouse))
                    .WhereIf(!string.IsNullOrWhiteSpace(status), e => e.Status == status)
                    .WhereIf(!string.IsNullOrEmpty(loadingArrivalDate), e => e.LoadingArrivalDate.Value.Year == DateTime.ParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture).Year && e.LoadingArrivalDate.Value.Month == DateTime.ParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture).Month && e.LoadingArrivalDate.Value.Day == DateTime.ParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture).Day);
        }

        public async Task<long> GetCountVehiclesLoadingAsync(
            string keyword = null,
            long vehicleRegId = 0,
            long customerId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string wareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string type = null,
            string truckType = null,
            CancellationToken cancellationToken = default)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (DateTime.TryParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
            {
                startOfDay = date.Date;
                endOfDay = date.Date.AddDays(1).AddTicks(-1);
            }
            else
            {
                startOfDay = DateTime.Now.Date.AddYears(-10);
                endOfDay = DateTime.Now.Date.AddDays(1).AddTicks(-1);
            }

            var query = from v in (await GetDbSetAsync())
                            //join w in (await GetDbContextAsync()).ErtsRemoteTransitSheds
                            //on v.VhclUnloadingWarehouse equals w.Id.ToString()
                        join warehouse in (await GetDbContextAsync()).WareHouses
                        on v.VhclLoadingWarehouse equals warehouse.Id.ToString()
                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        where v.VhclImportExport == type && v.VhclTruckType == truckType
                        join kund in (await GetDbContextAsync()).Kunds
                        on v.VhclProviderCustomerIsn equals kund.Id
                        into temp
                        from k in temp.DefaultIfEmpty()
                            //.WhereIf(customerId > 0, x => x.Id == customerId)
                        orderby v.VhclLoadingArrivalDate descending
                        select new VehiclesLoading
                        {
                            Id = v.Id,
                            VehicleId = v.VhclMasterIsn,
                            VehicRegNo = v.VehicRegNo,
                            VehicleLoadWeight = vh.VehicleLoadWeight,
                            VehicleType = vh.VehicleType,
                            ShedWarehouse = v.VhclUnloadingWarehouse,
                            Warehouse = warehouse.WarehouseName,
                            DriverName = v.VhclDriverName,
                            UnloadingArrivalDatetime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclUnloadingArrivalDate),
                            LoadingArrivalDatetime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingArrivalDate),
                            VhclLoadingVehicleClosed = v.VhclLoadingVehicleClosed,
                            VhclLoadingLeftDate = v.VhclLoadingLeftDate,
                            VhclVehicleComplete = v.VhclVehicleComplete,
                            LoadingArrivalDate = v.VhclLoadingArrivalDate,
                            UnloadingActivationDate = v.VhclUnloadingActivationDate,
                            UnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            CustomerName = k.KundName1,
                            CustomerId = k.Id
                        };

            query = query.WhereIf(vehicleRegId > 0, e => e.Id == vehicleRegId)
                       .WhereIf(customerId > 0, x => x.CustomerId == customerId)
                       .WhereIf(!string.IsNullOrWhiteSpace(keyword), e => e.VehicRegNo.ToUpper().Contains(keyword.ToUpper()) || e.ShedWarehouse.ToUpper().Contains(keyword.ToUpper()))
                       .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                       .WhereIf(!string.IsNullOrWhiteSpace(shedWareHouse), e => e.ShedWarehouse.Contains(shedWareHouse))
                       .WhereIf(!string.IsNullOrWhiteSpace(wareHouse), e => e.Warehouse.Contains(wareHouse))
                       .WhereIf(!string.IsNullOrWhiteSpace(loadingArrivalDate), x => x.LoadingArrivalDate >= startOfDay && x.LoadingArrivalDate <= endOfDay);
            var listItem = query.ToList();
            foreach (var item in listItem)
            {
                //Status: Ready to load
                var ReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == item.Id && v.VhldDeleted == false select v.Id;
                if (ReadyToload.Count() == 0)
                {
                    item.Status = "Ready to load";
                }
                else
                {
                    var CountReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == item.Id && v.VhldQtyreceived > 0 && v.VhldDeleted == false select v.Id;
                    if (CountReadyToload.Count() == 0)
                    {
                        item.Status = "Ready to load";
                    }
                    else
                    {
                        if (item.VhclLoadingVehicleClosed == false)
                        {
                            item.Status = "Loading";
                        }
                        if (item.VhclLoadingVehicleClosed == true)
                        {
                            item.Status = "Closed";
                        }
                        if (item.VhclLoadingLeftDate != null)
                        {
                            item.Status = "In Transit";
                        }
                        if (item.UnloadingArrivalDate != null)
                        {
                            item.Status = "Arrived Terminal";
                        }
                        if (item.UnloadingActivationDate != null)
                        {
                            item.Status = "Unloading";
                        }
                        if (item.VhclVehicleComplete == true)
                        {
                            item.Status = "Completed";
                        }
                    }
                }
            }
            var list = listItem.WhereIf(!string.IsNullOrWhiteSpace(status), e => e.Status.ToLower() == status.ToLower());

            return list.Count();
        }

        private async Task<IQueryable<VehiclesUnLoading>> GetQueryVehiclesUnLoading(
            string keyword = null,
            long vehicleRegId = 0,
            long customerId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string wareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string type = null,
            string truckType = null,
            string consignee = null)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(loadingArrivalDate))
            {
                if (DateTime.TryParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startOfDay = date.Date;
                    endOfDay = date.Date.AddDays(1).AddTicks(-1);
                }
                else
                {
                    startOfDay = DateTime.Now.Date.AddYears(-10);
                    endOfDay = DateTime.Now.Date.AddDays(1).AddTicks(-1);
                }
            }

            var query = from v in (await GetDbSetAsync())
                        .WhereIf(!string.IsNullOrWhiteSpace(keyword), e => e.VehicRegNo.ToUpper().Contains(keyword.ToUpper()) || e.VhclUnloadingWarehouse.ToUpper().Contains(keyword.ToUpper()) || e.Id.ToString() == keyword)
                        .WhereIf(vehicleRegId > 0, e => e.Id == vehicleRegId)
                        .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                        .WhereIf(!string.IsNullOrWhiteSpace(shedWareHouse), e => e.VhclLoadingWarehouse.Contains(shedWareHouse))
                        .WhereIf(!string.IsNullOrWhiteSpace(loadingArrivalDate), x => x.VhclLoadingLeftDate >= startOfDay && x.VhclLoadingLeftDate <= endOfDay)
                        .WhereIf(!string.IsNullOrEmpty(consignee), x => x.VhclCompany.Equals(consignee))
                        join warehouse in (await GetDbContextAsync()).WareHouses
                        .WhereIf(!string.IsNullOrWhiteSpace(wareHouse), e => e.WarehouseName.Contains(wareHouse))
                        on v.VhclUnloadingWarehouse equals warehouse.Id.ToString()
                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        where v.VhclImportExport == type && v.VhclLoadingLeftDate != null && v.VhclTruckType == truckType
                        join kund in (await GetDbContextAsync()).Kunds
                        on v.VhclProviderCustomerIsn equals kund.Id
                        into temp
                        from k in temp.DefaultIfEmpty()
                        join warehousePickup in (await GetDbContextAsync()).WareHousePickups
                        on v.VhclWareHousePickupIsn equals warehousePickup.Id
                        into warehousePickupTemp
                        from w in warehousePickupTemp.DefaultIfEmpty()
                        join factory in (await GetDbContextAsync()).Kunds
                        on w.FactoryId equals factory.Id
                        into factoryTemp
                        from c in factoryTemp.DefaultIfEmpty()
                        orderby v.VhclLoadingArrivalDate descending, v.VhclLoadingArrivalTime descending
                        select new VehiclesUnLoading
                        {
                            Id = v.Id,
                            VehicleId = v.VhclMasterIsn,
                            VehicRegNo = v.VehicRegNo,
                            VehicleLoadWeight = vh.VehicleLoadWeight,
                            ShedWarehouse = v.VhclLoadingWarehouse,
                            Warehouse = warehouse.WarehouseName,
                            LoadingLeftDate = v.VhclLoadingLeftDate,
                            UnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            UnloadingActivationDate = v.VhclUnloadingActivationDate,
                            Status = v.VhclArrivedFactoryDate != null ? "Arrived Factory" : (v.VhclTransitToFactoryDate != null ? "Transit To Factory" : (bool)v.VhclVehicleComplete ? "Completed" : (v.VhclUnloadingActivationDate != null ? "Unloading" : (v.VhclUnloadingArrivalDate != null ? "Arrived Warehouse" : (v.VhclLoadingLeftDate != null ? "Transit To Warehouse" : "")))),
                            CustomerName = k.Kund3letterCode,
                            CustomerId = k.Id,
                            LoadingArrivalDatetime = v.VhclLoadingArrivalDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclLoadingArrivalDate, v.VhclLoadingArrivalTime)),
                            //SealNumber = v.VhclSealNumber,
                            ConsigneeName = v.VhclCompany,
                            CompleteDate = v.VhclCompletedDate,
                            UnloadingArrivalDatetime = v.VhclUnloadingArrivalDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclUnloadingArrivalDate, v.VhclUnloadingArrivalTime)),
                            CompleteDateTime = v.VhclCompletedDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclCompletedDate, v.VhclCompletedTime)),
                            Factory = c.Kund3letterCode,
                            OlaNumber = v.VhclRacNumber
                        };
            return query.WhereIf(customerId > 0, x => x.CustomerId == customerId).WhereIf(!string.IsNullOrWhiteSpace(status), e => status.ToLower().Contains(e.Status.ToLower()));
        }
        private async Task<IQueryable<VehiclesUnLoading>> GetQueryVehiclesUnLoadingExport(
            string keyword = null,
            long vehicleRegId = 0,
            long customerId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string wareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string type = null,
            string truckType = null)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(loadingArrivalDate))
            {
                if (DateTime.TryParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startOfDay = date.Date;
                    endOfDay = date.Date.AddDays(1).AddTicks(-1);
                }
                else
                {
                    startOfDay = DateTime.Now.Date.AddYears(-10);
                    endOfDay = DateTime.Now.Date.AddDays(1).AddTicks(-1);
                }
            }

            var query = from v in (await GetDbSetAsync())
                        .WhereIf(!string.IsNullOrWhiteSpace(keyword), e => e.VehicRegNo.ToUpper().Contains(keyword.ToUpper()) || e.VhclUnloadingWarehouse.ToUpper().Contains(keyword.ToUpper()) || e.Id.ToString() == keyword)
                        .WhereIf(vehicleRegId > 0, e => e.Id == vehicleRegId)
                        .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                        .WhereIf(!string.IsNullOrWhiteSpace(shedWareHouse), e => e.VhclUnloadingWarehouse.Contains(shedWareHouse))
                        .WhereIf(!string.IsNullOrWhiteSpace(loadingArrivalDate), x => x.VhclLoadingLeftDate >= startOfDay && x.VhclLoadingLeftDate <= endOfDay)
                        join warehouse in (await GetDbContextAsync()).WareHouses
                        .WhereIf(!string.IsNullOrWhiteSpace(wareHouse), e => e.WarehouseName.Contains(wareHouse))
                        on v.VhclLoadingWarehouse equals warehouse.Id.ToString()
                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        where v.VhclImportExport == type && v.VhclLoadingLeftDate != null && v.VhclTruckType == truckType
                        join kund in (await GetDbContextAsync()).Kunds
                        on v.VhclProviderCustomerIsn equals kund.Id
                        into temp
                        from k in temp.DefaultIfEmpty()
                        join warehousePickup in (await GetDbContextAsync()).WareHousePickups
                        on v.VhclWareHousePickupIsn equals warehousePickup.Id
                        into warehousePickupTemp
                        from w in warehousePickupTemp.DefaultIfEmpty()
                        join factory in (await GetDbContextAsync()).Kunds
                        on w.FactoryId equals factory.Id
                        into factoryTemp
                        from c in factoryTemp.DefaultIfEmpty()
                        orderby v.VhclLoadingArrivalDate descending, v.VhclLoadingArrivalTime descending
                        select new VehiclesUnLoading
                        {
                            Id = v.Id,
                            VehicleId = v.VhclMasterIsn,
                            VehicRegNo = v.VehicRegNo,
                            VehicleLoadWeight = vh.VehicleLoadWeight,
                            ShedWarehouse = v.VhclUnloadingWarehouse,
                            Warehouse = warehouse.WarehouseName,
                            LoadingLeftDate = v.VhclLoadingLeftDate,
                            UnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            UnloadingArrivalTime = v.VhclUnloadingArrivalTime,
                            UnloadingActivationDate = v.VhclUnloadingActivationDate,
                            Status = v.VhclArrivedFactoryDate != null ? "Arrived Factory" : (v.VhclTransitToFactoryDate != null ? "Transit To Factory" : (bool)v.VhclVehicleComplete ? "Completed" : (v.VhclUnloadingActivationDate != null ? "Unloading" : (v.VhclUnloadingArrivalDate != null ? "Arrived Warehouse" : (v.VhclLoadingLeftDate != null ? "Transit To Warehouse" : "")))),
                            CustomerName = k.Kund3letterCode,
                            CustomerId = k.Id,
                            LoadingArrivalDatetime = v.VhclLoadingArrivalDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclLoadingArrivalDate, v.VhclLoadingArrivalTime)),
                            //SealNumber = v.VhclSealNumber,
                            ConsigneeName = v.VhclCompany,
                            CompleteDate = v.VhclCompletedDate,
                            UnloadingArrivalDatetime = v.VhclUnloadingArrivalDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclUnloadingArrivalDate, v.VhclUnloadingArrivalTime)),
                            CompleteDateTime = v.VhclCompletedDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclCompletedDate, v.VhclCompletedTime)),
                            Factory = c.Kund3letterCode,
                            WarehousePickup = w.Name,
                            Seal = v.VhclSealNumber
                        };
            return query.WhereIf(customerId > 0, x => x.CustomerId == customerId).WhereIf(!string.IsNullOrWhiteSpace(status), e => status.ToLower().Contains(e.Status.ToLower()));
        }
        public async Task<List<VehiclesUnLoading>> GetVehiclesUnLoadingListAsync(
            string keyword = null,
            long vehicleRegId = 0,
            long customerId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string wareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string type = null,
            string truckType = null,
            string consignee = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehiclesUnLoading(keyword, vehicleRegId, customerId, vehicleRegNo, shedWareHouse, wareHouse, status, loadingArrivalDate, type, truckType, consignee);
            var list = await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            foreach (var item in list)
            {
                var dt = (await GetDbContextAsync()).VhldVehicledetails.Where(x => x.VhldVehicleisn == item.Id && x.VhldDeleted == false);
                item.Pieces = (int)dt.Sum(x => x.VhldLoadedPieces);
                item.Weight = (decimal)dt.Sum(x => x.VhldLoadedWeight);
                item.PiecesUnload = (int)dt.Sum(x => x.VhldUnloadingPieces);
                item.WeightUnload = (decimal)dt.Sum(x => x.VhldUnloadingWeight);
            }
            return list;
        }

        public async Task<long> GetCountVehiclesUnLoadingAsync(
           string keyword = null,
            long vehicleRegId = 0,
            long customerId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string wareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string type = null,
            string truckType = null,
            string consignee = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehiclesUnLoading(keyword, vehicleRegId, customerId, vehicleRegNo, shedWareHouse, wareHouse, status, loadingArrivalDate, type, truckType);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }
        public async Task<List<VehiclesUnLoading>> GetVehiclesUnLoadingExportListAsync(
            string keyword = null,
            long vehicleRegId = 0,
            long customerId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string wareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string type = null,
            string truckType = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehiclesUnLoadingExport(keyword, vehicleRegId, customerId, vehicleRegNo, shedWareHouse, wareHouse, status, loadingArrivalDate, type, truckType);
            var list = await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
            foreach (var item in list)
            {
                var dt = (await GetDbContextAsync()).VhldVehicledetails.Where(x => x.VhldVehicleisn == item.Id && x.VhldDeleted == false);
                item.Pieces = (int)dt.Sum(x => x.VhldLoadedPieces);
                item.Weight = (decimal)dt.Sum(x => x.VhldLoadedWeight);
            }
            return list;
        }

        public async Task<long> GetCountVehiclesUnLoadingExportAsync(
           string keyword = null,
            long vehicleRegId = 0,
            long customerId = 0,
            string vehicleRegNo = null,
            string shedWareHouse = null,
            string wareHouse = null,
            string status = null,
            string loadingArrivalDate = null,
            string type = null,
            string truckType = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehiclesUnLoadingExport(keyword, vehicleRegId, customerId, vehicleRegNo, shedWareHouse, wareHouse, status, loadingArrivalDate, type, truckType);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }
        public async Task<VehiclesRegistrationTruckDetail> GetTruckDetail(long vehicleRegId = 0)
        {
            // Trả về status
            string status = string.Empty;
            var VehicleReg = from vr in (await GetDbSetAsync()) where vr.Id == vehicleRegId select vr;
            var ReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldDeleted == false select v;
            if (ReadyToload.Count() == 0)
            {
                status = "Ready to load";
            }
            else
            {
                var CountReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldQtyreceived > 0 && v.VhldDeleted == false select v;
                if (CountReadyToload.Count() == 0)
                {
                    status = "Ready to load";
                }
                else
                {
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == false)
                    {
                        status = "Loading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == true)
                    {
                        status = "Closed";
                    }
                    if (VehicleReg.FirstOrDefault().VhclLoadingLeftDate != null)
                    {
                        status = "In Transit";
                    }
                    if (VehicleReg.FirstOrDefault().VhclUnloadingArrivalDate != null)
                    {
                        status = "Arrived Terminal";
                    }
                    if (VehicleReg.FirstOrDefault().VhclUnloadingActivationDate != null)
                    {
                        status = "Unloading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclVehicleComplete == true)
                    {
                        status = "Completed";
                    }
                    if (VehicleReg.FirstOrDefault().VhclTransitToFactoryDate != null)
                    {
                        status = "Transit To Factory";
                    }
                    if (VehicleReg.FirstOrDefault().VhclArrivedFactoryDate != null)
                    {
                        status = "Arrived Factory";
                    }
                }
            }
            //VehiclesRegistrationTruckDetail vehiclesRegistrationTruckDetail = new VehiclesRegistrationTruckDetail();
            var query = from v in (await GetDbSetAsync())
                        join warehouse in (await GetDbContextAsync()).WareHouses
                        on v.VhclUnloadingWarehouse equals warehouse.Id.ToString()
                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        where v.Id == vehicleRegId
                        select new VehiclesRegistrationTruckDetail
                        {
                            VhclRacNumber = v.VhclRacNumber,
                            VhclSealNumber = v.VhclSealNumber,
                            VhclDriverName = v.VhclDriverName,
                            VhclRemarks = v.VhclRemarks,
                            Warehouse = warehouse.WarehouseName,
                            VhclLoadingWarehouse = v.VhclLoadingWarehouse,
                            VhclLoadingArrivalTime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingArrivalTime),
                            VhclLoadingArrivalDate = v.VhclLoadingArrivalDate,
                            ShedWarehouse = v.VhclLoadingWarehouse,
                            VhclLinkedIsn = v.VhclLinkedIsn,
                            CreateDate = vh.CreateDate,
                            VhclLoadingVehicleClosedDate = v.VhclLoadingVehicleClosedDate,
                            VhclLoadingVehicleClosedTime = v.VhclLoadingVehicleClosedTime,
                            VhclLoadingLeftDate = v.VhclLoadingLeftDate,
                            VhclLoadingLeftTime = v.VhclLoadingLeftTime,
                            VhclUnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            VhclUnloadingArrivalTime = v.VhclUnloadingArrivalTime,
                            VhclUnloadingActivationDate = v.VhclUnloadingActivationDate,
                            VhclUnloadingActivationTime = v.VhclUnloadingActivationTime,
                            VhclCompletedDate = v.VhclCompletedDate,
                            VhclCompletedTime = v.VhclCompletedTime,
                            Status = status,
                            VhclMessageIsn = v.VhclMessageIsn ?? 0,
                            VhclVctType = v.VhclVctType,
                            Consignee = v.VhclCompany,
                            ConsigneeId= v.VhclInspectionPlace
                        };

            return query.FirstOrDefault();
        }
        public async Task<VehiclesRegistrationTruckDetail> GetTruckDetailExport(long vehicleRegId = 0)
        {
            // Trả về status
            string status = string.Empty;
            var VehicleReg = from vr in (await GetDbSetAsync()) where vr.Id == vehicleRegId select vr;
            var ReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldDeleted == false select v;
            if (ReadyToload.Count() == 0)
            {
                status = "Ready to load";
            }
            else
            {
                var CountReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldQtyreceived > 0 && v.VhldDeleted == false select v;
                if (CountReadyToload.Count() == 0)
                {
                    status = "Ready to load";
                }
                else
                {
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == false)
                    {
                        status = "Loading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == true)
                    {
                        status = "Closed";
                    }
                    if (VehicleReg.FirstOrDefault().VhclLoadingLeftDate != null)
                    {
                        status = "In Transit";
                    }
                    if (VehicleReg.FirstOrDefault().VhclUnloadingArrivalDate != null)
                    {
                        status = "Arrived Terminal";
                    }
                    if (VehicleReg.FirstOrDefault().VhclUnloadingActivationDate != null)
                    {
                        status = "Unloading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclVehicleComplete == true)
                    {
                        status = "Completed";
                    }
                    if (VehicleReg.FirstOrDefault().VhclTransitToFactoryDate != null)
                    {
                        status = "Transit To Factory";
                    }
                    if (VehicleReg.FirstOrDefault().VhclArrivedFactoryDate != null)
                    {
                        status = "Arrived Factory";
                    }
                }
            }
            //VehiclesRegistrationTruckDetail vehiclesRegistrationTruckDetail = new VehiclesRegistrationTruckDetail();
            var query = from v in (await GetDbSetAsync())
                        join warehouse in (await GetDbContextAsync()).WareHouses
                        on v.VhclLoadingWarehouse equals warehouse.Id.ToString()
                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        where v.Id == vehicleRegId
                        select new VehiclesRegistrationTruckDetail
                        {
                            VhclRacNumber = v.VhclRacNumber,
                            VhclSealNumber = v.VhclSealNumber,
                            VhclDriverName = v.VhclDriverName,
                            VhclRemarks = v.VhclRemarks,
                            Warehouse = warehouse.WarehouseName,
                            VhclLoadingWarehouse = v.VhclLoadingWarehouse,
                            VhclLoadingArrivalTime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingArrivalTime),
                            VhclLoadingArrivalDate = v.VhclLoadingArrivalDate,
                            ShedWarehouse = v.VhclLoadingWarehouse,
                            VhclLinkedIsn = v.VhclLinkedIsn,
                            CreateDate = vh.CreateDate,
                            VhclLoadingVehicleClosedDate = v.VhclLoadingVehicleClosedDate,
                            VhclLoadingVehicleClosedTime = v.VhclLoadingVehicleClosedTime,
                            VhclLoadingLeftDate = v.VhclLoadingLeftDate,
                            VhclLoadingLeftTime = v.VhclLoadingLeftTime,
                            VhclUnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            VhclUnloadingArrivalTime = v.VhclUnloadingArrivalTime,
                            VhclUnloadingActivationDate = v.VhclUnloadingActivationDate,
                            VhclUnloadingActivationTime = v.VhclUnloadingActivationTime,
                            VhclCompletedDate = v.VhclCompletedDate,
                            VhclCompletedTime = v.VhclCompletedTime,
                            Status = status,
                            VhclMessageIsn = v.VhclMessageIsn ?? 0,
                            VhclVctType = v.VhclVctType,
                            Consignee = v.VhclCompany
                        };

            return query.FirstOrDefault();
        }
        public async Task<PagedResult<TruckEcus>> GetListTruckSendToEcusAsync(string term, int maxResultCount = int.MaxValue, int skipCount = 0, CancellationToken cancellationToken = default)
        {
            IQueryable<TruckEcus> query = from v in (await GetDbContextAsync()).OlaVehiclesRegistrations
                                          .Where(x => !string.IsNullOrEmpty(x.VehicRegNo) && (x.VhclMessageIsn == 0 || x.VhclMessageIsn == null) && x.VhclImportExport == "IMPORT" && x.VhclTruckType == "ECUS")
                                          .WhereIf(term != null, x => x.VehicRegNo.Contains(term.Trim()))
                                          select new TruckEcus()
                                          {
                                              VehicRegNo = v.VehicRegNo,
                                              Id = v.Id,
                                          };
            return query.PageResult((int)Math.Floor((double)skipCount / maxResultCount) + 1, maxResultCount);
        }

        public async Task<IQueryable<VehiclesScale>> GetQueryVehiclesScaleListAsync(
            string keyword = null,
            long vehicleRegId = 0,
            string vehicleRegNo = null,
            string status = null,
            string loadingArrivalDate = null)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(loadingArrivalDate))
            {
                if (DateTime.TryParseExact(loadingArrivalDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startOfDay = date.Date;
                    endOfDay = date.Date.AddDays(1).AddTicks(-1);
                }
                else
                {
                    startOfDay = DateTime.Now.Date.AddYears(-10);
                    endOfDay = DateTime.Now.Date.AddDays(1).AddTicks(-1);
                }
            }

            var query = from v in (await GetDbSetAsync())
                        join wareHousePickup in (await GetDbContextAsync()).WareHousePickups
                        on v.VhclWareHousePickupIsn equals wareHousePickup.Id
                        into wareHousePickupTemp
                        from w in wareHousePickupTemp.DefaultIfEmpty()
                        join f in (await GetDbContextAsync()).Kunds
                        on w.FactoryId equals f.Id
                        into temp
                        from ft in temp.DefaultIfEmpty()
                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        where v.VhclImportExport == "EXPORT" && v.VhclTruckType == "PICK UP"
                        && (v.VhclUnloadingArrivalDate != null || v.VhclUnloadingActivationDate != null || v.VhclVehicleComplete == true)
                        orderby v.VhclLoadingArrivalDate descending
                        select new VehiclesScale
                        {
                            Id = v.Id,
                            Factory = ft.Kund3letterCode + " - " + w.Name,
                            VehicleId = v.VhclMasterIsn,
                            VehicRegNo = v.VehicRegNo,
                            SealNumber = v.VhclSealNumber,
                            VhclVehicleComplete = v.VhclVehicleComplete,
                            UnloadingActivationDate = v.VhclUnloadingActivationDate,
                            UnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            LoadingArrivalDate = v.VhclLoadingArrivalDate,
                            Status = v.VhclVehicleComplete == true ? "HoanThanh" : (v.VhclUnloadingActivationDate != null ? "DangCan" : (v.VhclUnloadingArrivalDate != null ? "XeVeKho" : ""))
                        };

            query = query.WhereIf(!string.IsNullOrWhiteSpace(keyword), e => e.VehicRegNo.ToUpper().Contains(keyword.ToUpper()))
                       .WhereIf(vehicleRegId > 0, e => e.Id == vehicleRegId)
                       .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                       .WhereIf(!string.IsNullOrWhiteSpace(loadingArrivalDate), x => x.LoadingArrivalDate >= startOfDay && x.LoadingArrivalDate <= endOfDay)
                       .WhereIf(!string.IsNullOrWhiteSpace(status), e => e.Status.ToLower() == status.ToLower())
                       .OrderBy(x => x.UnloadingArrivalDate).OrderBy(x => x.UnloadingActivationDate).OrderBy(x => x.VhclVehicleComplete);
            return query;
        }

        public async Task<List<VehiclesScale>> GetVehiclesScaleListAsync(
            string keyword = null,
            long vehicleRegId = 0,
            string vehicleRegNo = null,
            string status = null,
            string loadingArrivalDate = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehiclesScaleListAsync(keyword, vehicleRegId, vehicleRegNo, status, loadingArrivalDate);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountVehiclesScaleAsync(
            string keyword = null,
            long vehicleRegId = 0,
            string vehicleRegNo = null,
            string status = null,
            string loadingArrivalDate = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryVehiclesScaleListAsync(keyword, vehicleRegId, vehicleRegNo, status, loadingArrivalDate);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }
        public async Task<List<VehicleSecurityCheck>> GetVehicleSecurityCheckAsync(string vehicleRegNo = null, string importOrExport = null)
        {
            var dbContext = await GetDbContextAsync();

            var listUncheckSeal = (from seal in dbContext.VhslVehicleSealInfos
                           .Where(x => x.VhslSealConfirmed != 1 && x.VhslSealType == "SECURITY")
                           .WhereIf(!string.IsNullOrWhiteSpace(importOrExport), x => x.VhslSealImportExport == importOrExport)
                                   select new
                                   {
                                       seal.VhslVehicleRegIsn
                                   }).Distinct();

            var listCheckSeal = (from seal in dbContext.VhslVehicleSealInfos
                          .Where(x => x.VhslSealConfirmed == 1 && x.VhslSealType == "SECURITY")
                          .WhereIf(!string.IsNullOrWhiteSpace(importOrExport), x => x.VhslSealImportExport == importOrExport)
                                 select new
                                 {
                                     seal.VhslVehicleRegIsn
                                 }).Distinct();

            var listFirstSeal = (from seal in dbContext.VhslVehicleSealInfos
                    .Where(x => x.VhslSealConfirmed == -1 && x.VhslSealType == "SECURITY")
                    .WhereIf(!string.IsNullOrWhiteSpace(importOrExport), x => x.VhslSealImportExport == importOrExport)
                                 select new
                                 {
                                     seal.VhslVehicleRegIsn
                                 }).Distinct();

            return (from vhcl in dbContext.VehiclesRegistrations
                          .Where(x => x.VhclLoadingLeftDate != null)
                          .WhereIf(!string.IsNullOrWhiteSpace(importOrExport), x => x.VhclImportExport == importOrExport)
                          .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), x => x.VehicRegNo == vehicleRegNo)
                    join seal in listUncheckSeal on vhcl.Id equals seal.VhslVehicleRegIsn
                    select new VehicleSecurityCheck
                    {
                        VehicleRegId = vhcl.Id,
                        VehicleRegNo = vhcl.VehicRegNo,
                        CheckedSeal = listFirstSeal.Where(x => x.VhslVehicleRegIsn == vhcl.Id).Count() > 0
                    }).ToList();
        }
        public async Task<List<TruckIn>> GetVehicleSecurityCheckInAsync(string vehicleRegNo = null, string loadingLeftDate = null)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(loadingLeftDate))
            {
                if (DateTime.TryParseExact(loadingLeftDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startOfDay = date.Date;
                    endOfDay = date.Date.AddDays(1).AddTicks(-1);
                }
                else
                {
                    startOfDay = DateTime.Now.Date.AddYears(-10);
                    endOfDay = DateTime.Now.Date.AddDays(1).AddTicks(-1);
                }
            }
            var dbContext = await GetDbContextAsync();
            //var listSeal = (from seal in dbContext.VhslVehicleSealInfos
            //                       .Where(x => x.VhslSealType == "SECURITY" || x.VhslSealType=="CUSTOM")
            //                select new
            //                {
            //                    seal.VhslVehicleRegIsn
            //                }).Distinct();

            var listFirstSeal = (from seal in dbContext.VhslVehicleSealInfos
                    .Where(x => x.VhslSealConfirmed != 1 && (x.VhslSealType == "SECURITY" || x.VhslSealType == "CUSTOM"))
                                 select new
                                 {
                                     seal.VhslVehicleRegIsn
                                 }).Distinct();

            var listTruckLoading = (from v in dbContext.VehiclesRegistrations.OrderByDescending(x => x.VhclLoadingLeftDate)
                        .Where(x => x.VhclTruckType == "TRANSIT" && x.VhclImportExport == "IMPORT")
                        .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                        .WhereIf(!string.IsNullOrWhiteSpace(loadingLeftDate), x => x.VhclLoadingLeftDate >= startOfDay && x.VhclLoadingLeftDate <= endOfDay)
                                    join warehouse in (await GetDbContextAsync()).WareHouses
                                    on v.VhclUnloadingWarehouse equals warehouse.Id.ToString()
                                    //join seal in listUncheckSeal on v.Id equals seal.VhslVehicleRegIsn
                                    select new TruckIn
                                    {
                                        VehicleRegId = v.Id,
                                        VehicleRegNo = v.VehicRegNo,
                                        ShedWarehouseId = v.VhclLoadingWarehouse,
                                        WarehouseId = warehouse.Id,
                                        WarehouseName = warehouse.WarehouseName,
                                        WH = "",
                                        Type = "TRANSIT_IMPORT",
                                        StartDateTime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingLeftDate),
                                        CheckedSeal = listFirstSeal.Where(x => x.VhslVehicleRegIsn == v.Id).Count() == 0 ? false : true
                                    }).ToList();

            var listTruckPickup = (from v in dbContext.VehiclesRegistrations.OrderByDescending(x => x.VhclLoadingArrivalDate)
                         .Where(x => x.VhclTruckType == "PICK UP" && x.VhclImportExport == "EXPORT")
                         .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                         .WhereIf(!string.IsNullOrWhiteSpace(loadingLeftDate), x => x.VhclLoadingArrivalDate >= startOfDay && x.VhclLoadingArrivalDate <= endOfDay)
                                   join warehouse in (await GetDbContextAsync()).WareHouses
                                   on v.VhclLoadingWarehouse equals warehouse.Id.ToString()
                                   join pickup in (await GetDbContextAsync()).WareHousePickups
                                   on v.VhclWareHousePickupIsn equals pickup.Id
                                   into pick
                                   from p in pick.DefaultIfEmpty()
                                       //join seal in listUncheckSeal on v.Id equals seal.VhslVehicleRegIsn
                                   join kund in (await GetDbContextAsync()).Kunds
                                    on v.VhclProviderCustomerIsn equals kund.Id
                                    into temp
                                   from k in temp.DefaultIfEmpty()

                                   select new TruckIn
                                   {
                                       VehicleRegId = v.Id,
                                       VehicleRegNo = v.VehicRegNo,
                                       ShedWarehouseId = "",
                                       WarehouseId = warehouse.Id,
                                       WarehouseName = warehouse.WarehouseName,
                                       WH = k.Kund3letterCode + " - " + p.Code,
                                       Type = "PICK_UP_EXPORT",
                                       StartDateTime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingArrivalDate),
                                       CheckedSeal = listFirstSeal.Where(x => x.VhslVehicleRegIsn == v.Id).Count() == 0 ? false : true
                                   }).ToList();

            var listTruck = listTruckLoading.Concat(listTruckPickup).Where(x => x.CheckedSeal == true).ToList();
            return listTruck;
        }

        public async Task<List<TruckOut>> GetVehicleSecurityCheckOutAsync(string vehicleRegNo = null, string loadingLeftDate = null)
        {
            DateTime startOfDay = DateTime.Now, endOfDay = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(loadingLeftDate))
            {
                if (DateTime.TryParseExact(loadingLeftDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    startOfDay = date.Date;
                    endOfDay = date.Date.AddDays(1).AddTicks(-1);
                }
                else
                {
                    startOfDay = DateTime.Now.Date.AddYears(-10);
                    endOfDay = DateTime.Now.Date.AddDays(1).AddTicks(-1);
                }
            }
            var dbContext = await GetDbContextAsync();

            //var listSeal = (from seal in dbContext.VhslVehicleSealInfos
            //               .Where(x => x.VhslSealType == "SECURITY" || x.VhslSealType == "CUSTOM")
            //                select new
            //                {
            //                    seal.VhslVehicleRegIsn
            //                }).Distinct();

            var listFirstSeal = (from seal in dbContext.VhslVehicleSealInfos
                    .Where(x => x.VhslSealConfirmed != 1 && (x.VhslSealType == "SECURITY" || x.VhslSealType == "CUSTOM"))
                                 select new
                                 {
                                     seal.VhslVehicleRegIsn
                                 }).Distinct();

            var listTruckLoading = (from v in dbContext.VehiclesRegistrations.OrderByDescending(x => x.VhclLoadingArrivalDate)
                        .Where(x => x.VhclTruckType == "TRANSIT" && x.VhclImportExport == "EXPORT")
                        .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                        .WhereIf(!string.IsNullOrWhiteSpace(loadingLeftDate), x => x.VhclLoadingArrivalDate >= startOfDay && x.VhclLoadingArrivalDate <= endOfDay)
                                    join warehouse in (await GetDbContextAsync()).WareHouses
                                    on v.VhclLoadingWarehouse equals warehouse.Id.ToString()
                                    //join seal in listUncheckSeal on v.Id equals seal.VhslVehicleRegIsn
                                    //into s from sl in s.DefaultIfEmpty()
                                    select new TruckOut
                                    {
                                        VehicleRegId = v.Id,
                                        VehicleRegNo = v.VehicRegNo,
                                        ShedWarehouseId = v.VhclUnloadingWarehouse,
                                        WarehouseId = warehouse.Id,
                                        WarehouseName = warehouse.WarehouseName,
                                        WH = "",
                                        Type = "TRANSIT_EXPORT",
                                        StartDateTime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingArrivalDate),
                                        CheckedSeal = listFirstSeal.Where(x => x.VhslVehicleRegIsn == v.Id).Count() == 0 ? false : true
                                    }).ToList();

            var listTruckDelivery = (from v in dbContext.VehiclesRegistrations.OrderByDescending(x => x.VhclLoadingArrivalDate)
                         .Where(x => x.VhclTruckType == "DELIVERY" && x.VhclImportExport == "IMPORT")
                         .WhereIf(!string.IsNullOrWhiteSpace(vehicleRegNo), e => e.VehicRegNo.Contains(vehicleRegNo))
                         .WhereIf(!string.IsNullOrWhiteSpace(loadingLeftDate), x => x.VhclLoadingArrivalDate >= startOfDay && x.VhclLoadingArrivalDate <= endOfDay)
                                     join warehouse in (await GetDbContextAsync()).WareHouses
                                     on v.VhclLoadingWarehouse equals warehouse.Id.ToString()
                                     into wh
                                     from w in wh.DefaultIfEmpty()

                                     join pickup in (await GetDbContextAsync()).WareHousePickups
                                     on v.VhclUnloadingWarehouse equals pickup.Id.ToString()
                                     into pick
                                     from p in pick.DefaultIfEmpty()
                                     select new TruckOut
                                     {
                                         VehicleRegId = v.Id,
                                         VehicleRegNo = v.VehicRegNo,
                                         ShedWarehouseId = "",
                                         WarehouseId = w.Id,
                                         WarehouseName = w.WarehouseName,
                                         WH = w.WarehouseName + " - " + p.Code,
                                         Type = "DELIVERY_IMPORT",
                                         StartDateTime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingArrivalDate),
                                         CheckedSeal = listFirstSeal.Where(x => x.VhslVehicleRegIsn == v.Id).Count() == 0 ? false : true
                                     }).ToList();

            var listTruck = listTruckLoading.Concat(listTruckDelivery).Where(x => x.CheckedSeal == true).ToList();
            return listTruck;
        }
        public async Task<VehiclesRegistrationTruckDetail> GetTruckDetailBonded(long vehicleRegId = 0)
        {
            // Trả về status
            string status = string.Empty;
            var VehicleReg = from vr in (await GetDbSetAsync()) where vr.Id == vehicleRegId select vr;
            var ReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldDeleted == false select v;
            if (ReadyToload.Count() == 0)
            {
                status = "Ready to load";
            }
            else
            {
                var CountReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldQtyreceived > 0 && v.VhldDeleted == false select v;
                if (CountReadyToload.Count() == 0)
                {
                    status = "Ready to load";
                }
                else
                {
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == false)
                    {
                        status = "Loading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == true)
                    {
                        status = "Closed";
                    }
                    if (VehicleReg.FirstOrDefault().VhclLoadingLeftDate != null)
                    {
                        status = "In Transit";
                    }
                    if (VehicleReg.FirstOrDefault().VhclUnloadingArrivalDate != null)
                    {
                        status = "Arrived Terminal";
                    }
                    if (VehicleReg.FirstOrDefault().VhclUnloadingActivationDate != null)
                    {
                        status = "Unloading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclVehicleComplete == true)
                    {
                        status = "Completed";
                    }
                    if (VehicleReg.FirstOrDefault().VhclTransitToFactoryDate != null)
                    {
                        status = "Transit To Factory";
                    }
                    if (VehicleReg.FirstOrDefault().VhclArrivedFactoryDate != null)
                    {
                        status = "Arrived Factory";
                    }
                }
            }
            //VehiclesRegistrationTruckDetail vehiclesRegistrationTruckDetail = new VehiclesRegistrationTruckDetail();
            var query = from v in (await GetDbSetAsync())
                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        where v.Id == vehicleRegId
                        select new VehiclesRegistrationTruckDetail
                        {
                            VhclRacNumber = v.VhclRacNumber,
                            VhclSealNumber = v.VhclSealNumber,
                            VhclDriverName = v.VhclDriverName,
                            VhclRemarks = v.VhclRemarks,
                            Warehouse = v.VhclUnloadingWarehouse,
                            VhclLoadingWarehouse = v.VhclLoadingWarehouse,
                            VhclLoadingArrivalTime = string.Format("{0:dd/MM/yyyy HH:mm}", v.VhclLoadingArrivalTime),
                            VhclLoadingArrivalDate = v.VhclLoadingArrivalDate,
                            ShedWarehouse = v.VhclUnloadingWarehouse,
                            VhclLinkedIsn = v.VhclLinkedIsn,
                            CreateDate = vh.CreateDate,
                            VhclLoadingVehicleClosedDate = v.VhclLoadingVehicleClosedDate,
                            VhclLoadingVehicleClosedTime = v.VhclLoadingVehicleClosedTime,
                            VhclLoadingLeftDate = v.VhclLoadingLeftDate,
                            VhclLoadingLeftTime = v.VhclLoadingLeftTime,
                            VhclUnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            VhclUnloadingArrivalTime = v.VhclUnloadingArrivalTime,
                            VhclUnloadingActivationDate = v.VhclUnloadingActivationDate,
                            VhclUnloadingActivationTime = v.VhclUnloadingActivationTime,
                            VhclCompletedDate = v.VhclCompletedDate,
                            VhclCompletedTime = v.VhclCompletedTime,
                            Status = status,
                            VhclMessageIsn = v.VhclMessageIsn ?? 0,
                            VhclVctType = v.VhclVctType
                        };

            return query.FirstOrDefault();
        }

        public async Task<VehiclesRegistrationStatus> GetBondedVehicleRegStatus(long vehicleRegId = 0)
        {
            string status = string.Empty;
            var VehicleReg = from vr in (await GetDbSetAsync()) where vr.Id == vehicleRegId select vr;
            var ReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldDeleted == false select v;
            if (ReadyToload.Count() == 0)
            {
                status = "Ready to load";
            }
            else
            {
                var CountReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldQtyreceived > 0 && v.VhldDeleted == false select v;
                if (CountReadyToload.Count() == 0)
                {
                    status = "Ready to load";
                }
                else
                {
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == false)
                    {
                        status = "Loading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclVehicleComplete == true)
                    {
                        status = "Completed";
                    }
                }
            }

            return new()
            {
                Status = status
            };
        }


        public async Task<List<TruckReturnInfo>> GetListTruckReturnAsync(
            string truckNo = null,
            string factoryWarehouse = null,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            var query = (from truck in (await GetDbSetAsync()).Where(x => x.VhclImportExport == "EXPORT" && x.VhclTruckType == "RETURN")
                            .WhereIf(!string.IsNullOrWhiteSpace(truckNo), x => x.VehicRegNo.Contains(truckNo))
                            .WhereIf(startDate.HasValue, x => x.VhclLoadingAtDoorDate >= startDate)
                            .WhereIf(endDate.HasValue, x => x.VhclLoadingAtDoorDate <= endDate)
                            .WhereIf(!string.IsNullOrWhiteSpace(factoryWarehouse), e => e.VhclUnloadingWarehouse.Contains(factoryWarehouse))
                         join vhld in (await GetDbContextAsync()).VhldVehicledetails.Where(x => x.VhldDeleted == false)
                             on truck.Id equals vhld.VhldVehicleisn into vhld
                         from truckDetail in vhld.DefaultIfEmpty()
                         join whPickup in (await GetDbContextAsync()).WareHousePickups
                             on truck.VhclUnloadingWarehouse equals whPickup.Id.ToString() into whPickup
                         from factoryWh in whPickup.DefaultIfEmpty()
                         orderby truck.VhclLoadingAtDoorDate descending
                         select new TruckReturnInfo
                         {
                             Id = truck.Id,
                             VehicleId = truck.VhclMasterIsn,
                             TruckNo = truck.VehicRegNo,
                             LoadingArrivalDate = truck.VhclLoadingArrivalDate, // TransitTime
                             LoadingAtDoorDate = truck.VhclLoadingAtDoorDate,   // CreatedTime
                             ArrivedFactoryDate = truck.VhclArrivedFactoryDate, // ArrivalFactoryTime
                             Objectisn = truckDetail.VhldGroupIsn,
                             Pcs = truckDetail.VhldQtyexpected,
                             GW = truckDetail.VhldWeightExpected,
                             UnloadingWarehouse = factoryWh.Name
                         }).ToList();
            return (from m in query
                    group m by m.Id into g
                    select new TruckReturnInfo
                    {
                        Id = g.Key,
                        VehicleId = g.FirstOrDefault().VehicleId,
                        TruckNo = g.FirstOrDefault().TruckNo,
                        TotalPcs = g.Select(x => new { x.Objectisn, x.Pcs }).Distinct().Sum(x => x.Pcs ?? 0),
                        TotalGW = g.Select(x => new { x.Objectisn, x.GW }).Distinct().Sum(x => x.GW ?? 0),
                        UnloadingWarehouse = g.FirstOrDefault().UnloadingWarehouse,
                        ArrivedFactoryDate = g.FirstOrDefault().ArrivedFactoryDate,
                        LoadingAtDoorDate = g.FirstOrDefault().LoadingAtDoorDate,
                        LoadingArrivalDate = g.FirstOrDefault().LoadingArrivalDate,
                    })
                    .ToList();
        }
        public async Task<VehiclesRegistrationStatus> GetTrayVehicleRegStatus(long vehicleRegId = 0)
        {
            string status = string.Empty;
            var VehicleReg = from vr in (await GetDbSetAsync()) where vr.Id == vehicleRegId select vr;
            var ReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldDeleted == false select v;
            if (ReadyToload.Count() == 0)
            {
                status = "Ready to load";
            }
            else
            {
                var CountReadyToload = from v in (await GetDbContextAsync()).VhldVehicledetails where v.VhldVehicleisn == vehicleRegId && v.VhldDeleted == false select v;
                if (CountReadyToload.Count() == 0)
                {
                    status = "Ready to load";
                }
                else
                {
                    if (VehicleReg.FirstOrDefault().VhclLoadingVehicleClosed == false)
                    {
                        status = "Loading";
                    }
                    if (VehicleReg.FirstOrDefault().VhclVehicleComplete == true)
                    {
                        status = "Completed";
                    }
                }
            }

            return new()
            {
                Status = status
            };
        }

        public async Task<List<VehiclesUnLoading>> GetQueryVehiclesUnLoadingExportForReport(
            DateTime? unloadingDateFrom = null,
            DateTime? unloadingDateTo = null,
            string type = null,
            string truckType = "PICK UP",
            long truckId = 0
        )
        {
            var query = (from v in (await GetDbSetAsync()).Where(x => x.VhclImportExport == type && x.VhclTruckType == truckType && x.VhclDeleted == false)
                        .WhereIf(truckId > 0, x => x.Id == truckId)
                        .WhereIf(unloadingDateFrom.HasValue && unloadingDateTo.HasValue && truckId == 0, x => x.VhclUnloadingArrivalDate >= unloadingDateFrom && x.VhclUnloadingArrivalDate <= unloadingDateTo)
                            
                        join vh in (await GetDbContextAsync()).Vehicles
                        on v.VhclMasterIsn equals vh.Id
                        join warehousePickup in (await GetDbContextAsync()).WareHousePickups
                        on v.VhclWareHousePickupIsn equals warehousePickup.Id
                        into warehousePickupTemp
                        from w in warehousePickupTemp.DefaultIfEmpty()
                        join factory in (await GetDbContextAsync()).Kunds
                        on w.FactoryId equals factory.Id
                        into factoryTemp
                        from c in factoryTemp.DefaultIfEmpty()
                        select new VehiclesUnLoading
                        {
                            Id = v.Id,
                            VehicleId = v.VhclMasterIsn,
                            VehicRegNo = v.VehicRegNo,
                            VehicleLoadWeight = vh.VehicleLoadWeight,
                            ShedWarehouse = v.VhclLoadingWarehouse,
                            //Warehouse = warehouse.WarehouseName,
                            LoadingLeftDate = v.VhclLoadingLeftDate,
                            UnloadingArrivalDate = v.VhclUnloadingArrivalDate,
                            UnloadingActivationDate = v.VhclUnloadingActivationDate,
                            //Status = v.VhclArrivedFactoryDate != null ? "Arrived Factory" : (v.VhclTransitToFactoryDate != null ? "Transit To Factory" : (bool)v.VhclVehicleComplete ? "Completed" : (v.VhclUnloadingActivationDate != null ? "Unloading" : (v.VhclUnloadingArrivalDate != null ? "Arrived Warehouse" : (v.VhclLoadingLeftDate != null ? "Transit To Warehouse" : "")))),
                            LoadingArrivalDatetime = v.VhclLoadingArrivalDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclLoadingArrivalDate, v.VhclLoadingArrivalTime)),
                            //SealNumber = v.VhclSealNumber,
                            ConsigneeName = v.VhclCompany,
                            CompleteDate = v.VhclCompletedDate,
                            UnloadingArrivalDatetime = v.VhclUnloadingArrivalDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclUnloadingArrivalDate, v.VhclUnloadingArrivalTime)),
                            CompleteDateTime = v.VhclCompletedDate == null ? "" : string.Format("{0:dd/MM/yyyy HH:mm}", CommonFunction.CombineDateAndTime((DateTime)v.VhclCompletedDate, v.VhclCompletedTime)),
                            Factory = c.Kund3letterCode,
                            WarehousePickup = w.Name,
                            //OlaNumber = v.VhclRacNumber
                        }).ToList();
            foreach (var item in query)
            {
                if (item.Id > 0)
                {
                    var listScaleHistory = await _groupPrcvHistoryRepository.GetPcsWeightListByVehicle(item.Id);
                    if (listScaleHistory.Any())
                    {
                        item.TotalDO = listScaleHistory.Select(x => x.DoNo).Distinct().Count();
                        item.PiecesScale = listScaleHistory.Sum(x => x.ReceivedPieces) ?? 0;
                        item.WeightScale = listScaleHistory.Sum(x => x.ReceivedGrossWeight) ?? 0;
                    }
                }
            }
            return query;
        }
    }
}