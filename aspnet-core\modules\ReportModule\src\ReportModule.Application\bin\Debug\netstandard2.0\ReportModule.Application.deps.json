{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"ReportModule.Application/0.1.0": {"dependencies": {"BondedModuleV2.Application": "0.1.0", "ConfigureAwait.Fody": "3.3.1", "EPPlus": "4.5.3.3", "Fody": "6.5.0", "FreeSpire.XLS": "14.2.0", "GeneralModule.Application.Contracts": "0.1.0", "HtmlAgilityPack": "1.11.67", "InboundModule.Application": "0.1.0", "NETStandard.Library": "2.0.3", "OutboundModule.Application": "0.1.0", "ReportModule.Application.Contracts": "0.1.0", "ReportModule.Domain": "0.1.0", "TrayModule.Application.Contracts": "0.1.0", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.FileManagement.Application": "1.0.0", "ZXing.Net": "0.16.10"}, "runtime": {"ReportModule.Application.dll": {}}}, "AutoMapper/10.1.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.1.1.0"}}}, "ConfigureAwait.Fody/3.3.1": {"dependencies": {"Fody": "6.5.0"}, "runtime": {"lib/netstandard2.0/ConfigureAwait.dll": {"assemblyVersion": "3.3.1.0", "fileVersion": "3.3.1.0"}}}, "EPPlus/4.5.3.3": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "5.0.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Data.Common": "4.3.0", "System.Drawing.Common": "4.7.0", "System.Reflection": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Pkcs": "4.7.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding.CodePages": "4.7.0", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.0/EPPlus.dll": {"assemblyVersion": "4.5.3.3", "fileVersion": "4.5.3.3"}}}, "FirebaseAdmin/2.2.0": {"dependencies": {"Google.Api.Gax.Rest": "3.2.0", "Google.Apis.Auth": "1.49.0", "System.Collections.Immutable": "5.0.0"}, "runtime": {"lib/netstandard2.0/FirebaseAdmin.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Fody/6.5.0": {}, "FreeSpire.XLS/14.2.0": {"dependencies": {"SkiaSharp": "1.68.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Security.Cryptography.Pkcs": "4.7.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Permissions": "4.5.0", "System.Text.Encoding.CodePages": "4.7.0"}, "runtime": {"lib/netstandard2.0/Spire.XLS.dll": {"assemblyVersion": "14.2.0.0", "fileVersion": "14.2.0.5420"}}}, "Google.Api.Gax/3.2.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Google.Api.Gax.Rest/3.2.0": {"dependencies": {"Google.Api.Gax": "3.2.0", "Google.Apis.Auth": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Google.Apis/1.49.0": {"dependencies": {"Google.Apis.Core": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}}, "Google.Apis.Auth/1.49.0": {"dependencies": {"Google.Apis": "1.49.0", "Google.Apis.Core": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.Auth.PlatformServices.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}, "lib/netstandard2.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}}, "Google.Apis.Core/1.49.0": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Google.Apis.Core.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}}, "HtmlAgilityPack/1.11.67": {"runtime": {"lib/netstandard2.0/HtmlAgilityPack.dll": {"assemblyVersion": "1.11.67.0", "fileVersion": "1.11.67.0"}}}, "JetBrains.Annotations/2020.3.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "2020.3.0.0", "fileVersion": "2020.3.0.0"}}}, "Microsoft.AspNet.Mvc/5.2.7": {"dependencies": {"Microsoft.AspNet.Razor": "3.2.7", "Microsoft.AspNet.WebPages": "3.2.7"}, "runtime": {"lib/net45/System.Web.Mvc.dll": {"assemblyVersion": "5.2.7.0", "fileVersion": "5.2.61128.0"}}}, "Microsoft.AspNet.Razor/3.2.7": {"runtime": {"lib/net45/System.Web.Razor.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.61128.0"}}}, "Microsoft.AspNet.WebPages/3.2.7": {"dependencies": {"Microsoft.AspNet.Razor": "3.2.7", "Microsoft.Web.Infrastructure": "1.0.0"}, "runtime": {"lib/net45/System.Web.Helpers.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.61128.0"}, "lib/net45/System.Web.WebPages.Deployment.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.61128.0"}, "lib/net45/System.Web.WebPages.Razor.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.61128.0"}, "lib/net45/System.Web.WebPages.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.61128.0"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authorization/5.0.5": {"dependencies": {"Microsoft.AspNetCore.Metadata": "5.0.5", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16708"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "5.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.2.19024"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Metadata/5.0.5": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16708"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "5.0.1", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.19109"}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.CSharp/4.7.0": {"runtime": {"lib/netstandard2.0/Microsoft.CSharp.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "System.Text.Json": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.DependencyInjection/5.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "5.0.0.1", "fileVersion": "5.0.120.57516"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.1", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.3.0"}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileProviders.Composite/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileProviders.Embedded/5.0.17": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21507"}}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Localization/5.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Localization.Abstractions": "5.0.5", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16708"}}}, "Microsoft.Extensions.Localization.Abstractions/5.0.5": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16708"}}}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Primitives/5.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.NETCore.Platforms/2.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Web.Infrastructure/1.0.0": {"runtime": {"lib/net40/Microsoft.Web.Infrastructure.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.20105.407"}}}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "Nito.AsyncEx.Context/5.1.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.AsyncEx.Coordination/5.1.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.0", "Nito.Collections.Deque": "1.1.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.AsyncEx.Tasks/5.1.0": {"dependencies": {"Nito.Disposables": "2.2.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.Collections.Deque/1.1.0": {"runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.Disposables/2.2.0": {"dependencies": {"System.Collections.Immutable": "5.0.0"}, "runtime": {"lib/netstandard2.0/Nito.Disposables.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NPOI/2.5.5": {"dependencies": {"Portable.BouncyCastle": "1.8.9", "SharpZipLib": "1.3.2", "System.Configuration.ConfigurationManager": "4.5.0", "System.Drawing.Common": "4.7.0"}, "runtime": {"lib/netstandard2.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/NPOI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Portable.BouncyCastle/1.8.9": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SharpZipLib/1.3.2": {"runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp/1.68.0": {"runtime": {"lib/netstandard1.3/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24212.1"}}}, "System.Buffers/4.5.1": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.CodeDom/4.7.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Collections.Immutable/5.0.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.ComponentModel.Annotations/5.0.0": {"runtime": {"lib/netstandard2.0/System.ComponentModel.Annotations.dll": {"assemblyVersion": "4.2.1.0", "fileVersion": "4.6.26515.6"}}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Configuration.ConfigurationManager/4.5.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.2/System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/5.0.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/4.7.0": {"runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "4.0.0.1", "fileVersion": "4.6.26919.2"}}}, "System.Dynamic.Runtime/4.0.11": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {"assemblyVersion": "4.0.11.0", "fileVersion": "1.0.24212.1"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Linq.Dynamic.Core/1.2.9": {"dependencies": {"System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.2.9.0", "fileVersion": "1.2.9.0"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Management/4.7.0": {"dependencies": {"System.CodeDom": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "4.0.1.2", "fileVersion": "4.6.31308.1"}}}, "System.Net.Http.WinHttpHandler/4.4.0": {"dependencies": {"System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/System.Net.Http.WinHttpHandler.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Numerics.Vectors/4.5.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "4.1.4.0", "fileVersion": "4.6.26515.6"}}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Private.ServiceModel/4.4.4": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Net.Http.WinHttpHandler": "4.4.0", "System.Reflection.DispatchProxy": "4.4.0", "System.Security.Principal.Windows": "4.5.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.DispatchProxy/4.4.0": {"runtime": {"lib/netstandard2.0/System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Reflection.Emit/4.7.0": {"dependencies": {"System.Reflection.Emit.ILGeneration": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Reflection.Emit.ILGeneration/4.7.0": {"runtime": {"lib/netstandard2.0/System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Security.AccessControl/4.5.0": {"dependencies": {"System.Security.Principal.Windows": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}, "runtime": {"lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24212.1"}}}, "System.Security.Cryptography.Pkcs/4.7.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Security.Cryptography.Cng": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "4.0.4.0", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Cryptography.X509Certificates/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.7.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.7.0", "System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Security.Principal.Windows/4.5.0": {"runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.ServiceModel.Duplex/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4", "System.ServiceModel.Primitives": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.ServiceModel.Http/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4", "System.ServiceModel.Primitives": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.ServiceModel.NetTcp/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4", "System.ServiceModel.Primitives": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.ServiceModel.Primitives/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "4.2.0.3", "fileVersion": "4.6.26720.1"}, "lib/netstandard2.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.65535.65535"}}}, "System.ServiceModel.Security/4.4.4": {"dependencies": {"System.Private.ServiceModel": "4.4.4", "System.ServiceModel.Primitives": "4.4.4"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Security.dll": {"assemblyVersion": "4.0.3.3", "fileVersion": "4.6.26720.1"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.7.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "4.1.3.0", "fileVersion": "4.700.19.56404"}}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/5.0.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Text.Json/5.0.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encodings.Web": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "4.2.0.1", "fileVersion": "4.6.28619.1"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard1.3/System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XPath.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XPath": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "TimeZoneConverter/3.4.0": {"runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"assemblyVersion": "3.4.0.0", "fileVersion": "3.4.0.0"}}}, "Volo.Abp.Auditing/4.3.0": {"dependencies": {"Volo.Abp.Data": "4.3.0", "Volo.Abp.Json": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Threading": "4.3.0", "Volo.Abp.Timing": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.AuditLogging.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization/4.3.0": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization.Abstractions/4.3.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "5.0.5", "Volo.Abp.MultiTenancy": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.AutoMapper/4.3.0": {"dependencies": {"AutoMapper": "10.1.1", "Volo.Abp.Auditing": "4.3.0", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.AutoMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BackgroundJobs.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BlobStoring/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Threading": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.BlobStoring.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BlobStoring.Database.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.BlobStoring.Database.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Caching/4.3.0": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "5.0.0", "Volo.Abp.Json": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Serialization": "4.3.0", "Volo.Abp.Threading": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Commercial.Core/4.3.0": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Portable.BouncyCastle": "1.8.9", "System.Management": "4.7.0", "Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Commercial.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Commercial.SuiteTemplates/4.3.0": {"dependencies": {"Volo.Abp.Commercial.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Commercial.SuiteTemplates.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/4.3.0": {"dependencies": {"JetBrains.Annotations": "2020.3.0", "Microsoft.Extensions.Configuration.CommandLine": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.1", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0", "Microsoft.Extensions.Localization": "5.0.5", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0", "Nito.AsyncEx.Context": "5.1.0", "Nito.AsyncEx.Coordination": "5.1.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Linq.Dynamic.Core": "1.2.9", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Data/4.3.0": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.Uow": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Application/4.3.0": {"dependencies": {"Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.GlobalFeatures": "4.3.0", "Volo.Abp.Http.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Application.Contracts/4.3.0": {"dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Domain/4.3.0": {"dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Data": "4.3.0", "Volo.Abp.EventBus": "4.3.0", "Volo.Abp.ExceptionHandling": "4.3.0", "Volo.Abp.Guids": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0", "Volo.Abp.Specifications": "4.3.0", "Volo.Abp.Threading": "4.3.0", "Volo.Abp.Timing": "4.3.0", "Volo.Abp.Uow": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus/4.3.0": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ExceptionHandling/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.FeatureManagement.Application/4.3.0": {"dependencies": {"Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.FeatureManagement.Application.Contracts": "4.3.0", "Volo.Abp.FeatureManagement.Domain": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.FeatureManagement.Application.Contracts/4.3.0": {"dependencies": {"Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.FeatureManagement.Domain.Shared": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.FeatureManagement.Domain/4.3.0": {"dependencies": {"Volo.Abp.Caching": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.Abp.FeatureManagement.Domain.Shared": "4.3.0", "Volo.Abp.Features": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.FeatureManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Features/4.3.0": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.GlobalFeatures/4.3.0": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Core": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Guids/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Http.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Users.Domain.Shared": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Pro.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Identity.Domain.Shared": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Pro.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.IdentityServer.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json/4.3.0": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.Timing": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.LanguageManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.LanguageManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization/4.3.0": {"dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy/4.3.0": {"dependencies": {"Volo.Abp.Data": "4.3.0", "Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectExtending/4.3.0": {"dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.Validation.Abstractions": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectMapping/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.PermissionManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Security/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Serialization/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.SettingManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Localization": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Settings/4.3.0": {"dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Specifications/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Specifications.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.TextTemplateManagement.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.TextTemplateManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Threading/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Timing/4.3.0": {"dependencies": {"TimeZoneConverter": "3.4.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Settings": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Uow/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Domain.Shared/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation/4.3.0": {"dependencies": {"Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation.Abstractions": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation.Abstractions/4.3.0": {"dependencies": {"Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.VirtualFileSystem/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "5.0.0", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Volo.Abp.Core": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Saas.Domain/4.3.0": {"dependencies": {"Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Data": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.Abp.FeatureManagement.Domain": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Saas.Domain.Shared": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Saas.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Saas.Domain.Shared/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.FeatureManagement.Domain.Shared": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Saas.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Saas.Host.Application/4.3.0": {"dependencies": {"Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.FeatureManagement.Application": "4.3.0", "Volo.Saas.Domain": "4.3.0", "Volo.Saas.Host.Application.Contracts": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Saas.Host.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Saas.Host.Application.Contracts/4.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.FeatureManagement.Application.Contracts": "4.3.0", "Volo.Saas.Domain.Shared": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Saas.Host.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ZXing.Net/0.16.10": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard2.0/zxing.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "AOMS.Domain.Shared/1.0.0": {"dependencies": {"BondedModuleV2.Domain.Shared": "0.1.0", "GeneralModule.Domain.Shared": "0.1.0", "InboundModule.Domain.Shared": "0.1.0", "MasterDataModule.Domain.Shared": "0.1.0", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "NPOI": "2.5.5", "OutboundModule.Domain.Shared": "0.1.0", "ReportModule.Domain.Shared": "0.1.0", "SeaModule.Domain.Shared": "0.1.0", "ShareDataModule.Domain.Shared": "0.1.0", "TrayModule.Domain.Shared": "0.1.0", "Volo.Abp.AuditLogging.Domain.Shared": "4.3.0", "Volo.Abp.BackgroundJobs.Domain.Shared": "4.3.0", "Volo.Abp.BlobStoring.Database.Domain.Shared": "4.3.0", "Volo.Abp.Commercial.SuiteTemplates": "4.3.0", "Volo.Abp.FeatureManagement.Domain.Shared": "4.3.0", "Volo.Abp.GlobalFeatures": "4.3.0", "Volo.Abp.Identity.Pro.Domain.Shared": "4.3.0", "Volo.Abp.IdentityServer.Domain.Shared": "4.3.0", "Volo.Abp.LanguageManagement.Domain.Shared": "4.3.0", "Volo.Abp.LeptonTheme.Management.Domain.Shared": "1.0.0", "Volo.Abp.PermissionManagement.Domain.Shared": "4.3.0", "Volo.Abp.SettingManagement.Domain.Shared": "4.3.0", "Volo.Abp.TextTemplateManagement.Domain.Shared": "4.3.0", "Volo.Chat.Domain.Shared": "1.0.0", "Volo.FileManagement.Domain.Shared": "1.0.0", "Volo.Saas.Domain.Shared": "4.3.0"}, "runtime": {"AOMS.Domain.Shared.dll": {}}}, "BondedModuleV2.Application/0.1.0": {"dependencies": {"BondedModuleV2.Application.Contracts": "0.1.0", "BondedModuleV2.Domain": "0.1.0", "MasterDataModule.Application": "0.1.0", "NPOI": "2.5.5", "OutboundModule.Application.Contracts": "0.1.0", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0"}, "runtime": {"BondedModuleV2.Application.dll": {}}}, "BondedModuleV2.Application.Contracts/0.1.0": {"dependencies": {"BondedModuleV2.Domain": "0.1.0", "BondedModuleV2.Domain.Shared": "0.1.0", "MasterDataModule.Application.Contracts": "0.1.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"BondedModuleV2.Application.Contracts.dll": {}}}, "BondedModuleV2.Domain/0.1.0": {"dependencies": {"BondedModuleV2.Domain.Shared": "0.1.0", "MasterDataModule.Domain": "0.1.0", "ShareDataModule.Application.Contracts": "0.1.0", "ShareDataModule.Domain": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"BondedModuleV2.Domain.dll": {}}}, "BondedModuleV2.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"BondedModuleV2.Domain.Shared.dll": {}}}, "GeneralModule.Application.Contracts/0.1.0": {"dependencies": {"GeneralModule.Domain": "0.1.0", "GeneralModule.Domain.Shared": "0.1.0", "MasterDataModule.Application.Contracts": "0.1.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"GeneralModule.Application.Contracts.dll": {}}}, "GeneralModule.Domain/0.1.0": {"dependencies": {"GeneralModule.Domain.Shared": "0.1.0", "MasterDataModule.Domain": "0.1.0", "ShareDataModule.Application.Contracts": "0.1.0", "ShareDataModule.Domain": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"GeneralModule.Domain.dll": {}}}, "GeneralModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"GeneralModule.Domain.Shared.dll": {}}}, "InboundModule.Application/0.1.0": {"dependencies": {"InboundModule.Application.Contracts": "0.1.0", "InboundModule.Domain": "0.1.0", "MasterDataModule.Application": "0.1.0", "NPOI": "2.5.5", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0"}, "runtime": {"InboundModule.Application.dll": {}}}, "InboundModule.Application.Contracts/0.1.0": {"dependencies": {"InboundModule.Domain": "0.1.0", "InboundModule.Domain.Shared": "0.1.0", "MasterDataModule.Application.Contracts": "0.1.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"InboundModule.Application.Contracts.dll": {}}}, "InboundModule.Domain/0.1.0": {"dependencies": {"InboundModule.Domain.Shared": "0.1.0", "MasterDataModule.Domain": "0.1.0", "ShareDataModule.Application.Contracts": "0.1.0", "ShareDataModule.Domain": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"InboundModule.Domain.dll": {}}}, "InboundModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"InboundModule.Domain.Shared.dll": {}}}, "MasterDataModule.Application/0.1.0": {"dependencies": {"AOMS.Domain.Shared": "1.0.0", "FirebaseAdmin": "2.2.0", "InboundModule.Domain": "0.1.0", "MasterDataModule.Application.Contracts": "0.1.0", "MasterDataModule.Domain": "0.1.0", "OutboundModule.Domain": "0.1.0", "ShareDataModule.Application": "0.1.0", "System.ServiceModel.Duplex": "4.4.4", "System.ServiceModel.Http": "4.4.4", "System.ServiceModel.NetTcp": "4.4.4", "System.ServiceModel.Security": "4.4.4", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.BlobStoring": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.FileManagement.Domain": "1.0.0", "Volo.Saas.Host.Application": "4.3.0"}, "runtime": {"MasterDataModule.Application.dll": {}}}, "MasterDataModule.Application.Contracts/0.1.0": {"dependencies": {"MasterDataModule.Domain.Shared": "0.1.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0", "Volo.FileManagement.Application.Contracts": "1.0.0"}, "runtime": {"MasterDataModule.Application.Contracts.dll": {}}}, "MasterDataModule.Domain/0.1.0": {"dependencies": {"MasterDataModule.Domain.Shared": "0.1.0", "Microsoft.CSharp": "4.7.0", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"MasterDataModule.Domain.dll": {}}}, "MasterDataModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"MasterDataModule.Domain.Shared.dll": {}}}, "OutboundModule.Application/0.1.0": {"dependencies": {"MasterDataModule.Application": "0.1.0", "OutboundModule.Application.Contracts": "0.1.0", "OutboundModule.Domain": "0.1.0", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0"}, "runtime": {"OutboundModule.Application.dll": {}}}, "OutboundModule.Application.Contracts/0.1.0": {"dependencies": {"MasterDataModule.Application.Contracts": "0.1.0", "Microsoft.AspNet.Mvc": "5.2.7", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "OutboundModule.Domain": "0.1.0", "OutboundModule.Domain.Shared": "0.1.0", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"OutboundModule.Application.Contracts.dll": {}}}, "OutboundModule.Domain/0.1.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "OutboundModule.Domain.Shared": "0.1.0", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"OutboundModule.Domain.dll": {}}}, "OutboundModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"OutboundModule.Domain.Shared.dll": {}}}, "ReportModule.Application.Contracts/0.1.0": {"dependencies": {"MasterDataModule.Application.Contracts": "0.1.0", "ReportModule.Domain.Shared": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"ReportModule.Application.Contracts.dll": {}}}, "ReportModule.Domain/0.1.0": {"dependencies": {"ReportModule.Domain.Shared": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"ReportModule.Domain.dll": {}}}, "ReportModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"ReportModule.Domain.Shared.dll": {}}}, "SeaModule.Application.Contracts/0.1.0": {"dependencies": {"OutboundModule.Domain": "0.1.0", "SeaModule.Domain.Shared": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"SeaModule.Application.Contracts.dll": {}}}, "SeaModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"SeaModule.Domain.Shared.dll": {}}}, "ShareDataModule.Application/0.1.0": {"dependencies": {"ShareDataModule.Application.Contracts": "0.1.0", "ShareDataModule.Domain": "0.1.0", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0"}, "runtime": {"ShareDataModule.Application.dll": {}}}, "ShareDataModule.Application.Contracts/0.1.0": {"dependencies": {"ShareDataModule.Domain.Shared": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"ShareDataModule.Application.Contracts.dll": {}}}, "ShareDataModule.Domain/0.1.0": {"dependencies": {"ShareDataModule.Domain.Shared": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"ShareDataModule.Domain.dll": {}}}, "ShareDataModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"ShareDataModule.Domain.Shared.dll": {}}}, "TrayModule.Application.Contracts/0.1.0": {"dependencies": {"MasterDataModule.Application.Contracts": "0.1.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "ShareDataModule.Application.Contracts": "0.1.0", "TrayModule.Domain": "0.1.0", "TrayModule.Domain.Shared": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "runtime": {"TrayModule.Application.Contracts.dll": {}}}, "TrayModule.Domain/0.1.0": {"dependencies": {"MasterDataModule.Domain": "0.1.0", "ShareDataModule.Application.Contracts": "0.1.0", "ShareDataModule.Domain": "0.1.0", "TrayModule.Domain.Shared": "0.1.0", "Volo.Abp.Ddd.Domain": "4.3.0"}, "runtime": {"TrayModule.Domain.dll": {}}}, "TrayModule.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"TrayModule.Domain.Shared.dll": {}}}, "Volo.Abp.LeptonTheme.Management.Domain.Shared/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"Volo.Abp.LeptonTheme.Management.Domain.Shared.dll": {}}}, "Volo.Chat.Domain.Shared/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"Volo.Chat.Domain.Shared.dll": {}}}, "Volo.FileManagement.Application/1.0.0": {"dependencies": {"EPPlus": "4.5.3.3", "OutboundModule.Application.Contracts": "0.1.0", "SeaModule.Application.Contracts": "0.1.0", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.Caching": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.FileManagement.Application.Contracts": "1.0.0", "Volo.FileManagement.Domain": "1.0.0"}, "runtime": {"Volo.FileManagement.Application.dll": {}}}, "Volo.FileManagement.Application.Contracts/1.0.0": {"dependencies": {"EPPlus": "4.5.3.3", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "ShareDataModule.Application.Contracts": "0.1.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0", "Volo.FileManagement.Domain.Shared": "1.0.0"}, "runtime": {"Volo.FileManagement.Application.Contracts.dll": {}}}, "Volo.FileManagement.Domain/1.0.0": {"dependencies": {"EPPlus": "4.5.3.3", "Volo.Abp.AutoMapper": "4.3.0", "Volo.Abp.BlobStoring": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.FileManagement.Domain.Shared": "1.0.0"}, "runtime": {"Volo.FileManagement.Domain.dll": {}}}, "Volo.FileManagement.Domain.Shared/1.0.0": {"dependencies": {"EPPlus": "4.5.3.3", "Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "runtime": {"Volo.FileManagement.Domain.Shared.dll": {}}}}}, "libraries": {"ReportModule.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/10.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "path": "automapper/10.1.1", "hashPath": "automapper.10.1.1.nupkg.sha512"}, "ConfigureAwait.Fody/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-R9PQYf0AT4RBZcUXm22xWkCpSmNHdTzQ0dOyLIsxIK6dwXH4S9pY/rZdXU/63i8vZvSzZ99sB1kP7xer8MCe6w==", "path": "configureawait.fody/3.3.1", "hashPath": "configureawait.fody.3.3.1.nupkg.sha512"}, "EPPlus/4.5.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-aZnWmrQNRQuV/2Kwu+pTv1HVX1ZBvoGKw2GuzUOmBRpOD09sYJFGHww8NVQd3IIeb1vqosPDFZgND/1nxTzFKQ==", "path": "epplus/4.5.3.3", "hashPath": "epplus.4.5.3.3.nupkg.sha512"}, "FirebaseAdmin/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nF+7Z7kJ2ikjh75vjKkSmuqgI9b0O8Azygu3GMvx3/z3v5dnk5b6O9nL5/WkuCenYDzyz35r85pkN3C9K93+2g==", "path": "firebaseadmin/2.2.0", "hashPath": "firebaseadmin.2.2.0.nupkg.sha512"}, "Fody/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+bc+fzXFum7/MPjhEsxw+kIGcwfnI3JHv4w2T0jLLvk9HfO/xoq/aIjF1TSPlUWl+A1k2odtwdEdKZXJZXZzbQ==", "path": "fody/6.5.0", "hashPath": "fody.6.5.0.nupkg.sha512"}, "FreeSpire.XLS/14.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hh27gKg+nRO2QM8ZB+P0T7UL54SWbk45Z/1i5vHetYqxQMUI4JxpCxTVtADxwMgIVU0oLExUTBn1+GO8bWaSaA==", "path": "freespire.xls/14.2.0", "hashPath": "freespire.xls.14.2.0.nupkg.sha512"}, "Google.Api.Gax/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-0<PERSON>jahFAHTOoprSgvJiQ6/fIQLrUYU4QIFgkuJ51/lcmhZbuXxB3ycPk3JTVEvx6A5yQBL14wgmHgwXLcEsnu3Q==", "path": "google.api.gax/3.2.0", "hashPath": "google.api.gax.3.2.0.nupkg.sha512"}, "Google.Api.Gax.Rest/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YY4mD0nGxTx1uez7Perm+zAd3FH50dd3+7HTYsRFCywDEtj3RkrMjcAmw6mNpKkw2sRICu7aYNy1mgMjd3nVbw==", "path": "google.api.gax.rest/3.2.0", "hashPath": "google.api.gax.rest.3.2.0.nupkg.sha512"}, "Google.Apis/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-fmXQQTxZFOBlnvokvdQMzq0Lt+g2QzpZ4H6U8lMMFHgAR8ZqxQnPN5yHDpoHf7a++hJHb5W3pALxauGsq+afKQ==", "path": "google.apis/1.49.0", "hashPath": "google.apis.1.49.0.nupkg.sha512"}, "Google.Apis.Auth/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-3V9ohvixQtjaEvk7T9Ac7E/KvwEPa7eL4aMNreJDI0CEPzCdQdk2zCvaJPRrNIjhe+UuBeOeom1oAOMFB74JHg==", "path": "google.apis.auth/1.49.0", "hashPath": "google.apis.auth.1.49.0.nupkg.sha512"}, "Google.Apis.Core/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-9DgGdtyzgrCfHWwc/HiDXDbykNMeKQozbEHYEUEcezRuH+YR3fvq7YGVBDmUM8g6qEL3kDk6h5EU4h0IJwue1w==", "path": "google.apis.core/1.49.0", "hashPath": "google.apis.core.1.49.0.nupkg.sha512"}, "HtmlAgilityPack/1.11.67": {"type": "package", "serviceable": true, "sha512": "sha512-xnt6f8E56oK9nLqgkF42bsVSAkUuIuJnSfvzimK7GwvbhdXWRmGsx4A8AKmLOlCUq1rVSUADKalxnSMizHt4Mg==", "path": "htmlagilitypack/1.11.67", "hashPath": "htmlagilitypack.1.11.67.nupkg.sha512"}, "JetBrains.Annotations/2020.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FnX06vtxuoZnhZdR6UHt5kJ7HUC/syODfGLnhPDn1x5sXvvepNyCl4jMtPUzJfsPWh7q0Jo+AIYz5xaVbbyikA==", "path": "jetbrains.annotations/2020.3.0", "hashPath": "jetbrains.annotations.2020.3.0.nupkg.sha512"}, "Microsoft.AspNet.Mvc/5.2.7": {"type": "package", "serviceable": true, "sha512": "sha512-m3hUsn48k6Qb5El9A8naTwGTLCdSQKGSpGweqIfiVKltGJzAJJxuXvhhaQQtLCGg3i5V88iGjz0JG72/tlFKlg==", "path": "microsoft.aspnet.mvc/5.2.7", "hashPath": "microsoft.aspnet.mvc.5.2.7.nupkg.sha512"}, "Microsoft.AspNet.Razor/3.2.7": {"type": "package", "serviceable": true, "sha512": "sha512-BpWE<PERSON>+Ys7g9VAkbAfpG5jr5A1fKcmadCCXv3fYfps5YfTVABJIGV4uc9yvmXKxsNsjL+BzDld2gs+xtgT2gg1g==", "path": "microsoft.aspnet.razor/3.2.7", "hashPath": "microsoft.aspnet.razor.3.2.7.nupkg.sha512"}, "Microsoft.AspNet.WebPages/3.2.7": {"type": "package", "serviceable": true, "sha512": "sha512-2jwZFB7PvY+tbdz0ZP4iEo7gMrJxkCQUzoGLh6swUc6ZXl6DoKyDslmGcC/j9PFmJXCRMVIqtRorlPSMU2DuRA==", "path": "microsoft.aspnet.webpages/3.2.7", "hashPath": "microsoft.aspnet.webpages.3.2.7.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pQSx1MrLJlKwlEclliO9aUwKxe9EKI2Mff39VE1t5VYOjqsMyI2ujWKGI6XAUsnmC0Bta67GZ1k4DbQZd7tJKg==", "path": "microsoft.aspnetcore.authorization/5.0.5", "hashPath": "microsoft.aspnetcore.authorization.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9BB9hftnCsyJalz9IT0DUFxz8Xvgh3TOfGWolpuf19duxB4FySq7c25XDYBmBMS+sun5/PsEUAi58ra4iJAoA==", "path": "microsoft.aspnetcore.jsonpatch/2.2.0", "hashPath": "microsoft.aspnetcore.jsonpatch.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-SD+puPsFLQXWwzoMiXv8lpFGHyZg0gKp3OP2EXg3eRlwJQOMaUGMIqbCdfHGR4MBaUZtqY/tU68H1bzb6+FSxA==", "path": "microsoft.aspnetcore.metadata/5.0.5", "hashPath": "microsoft.aspnetcore.metadata.5.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "path": "microsoft.aspnetcore.mvc.core/2.2.5", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ScWwXrkAvw6PekWUFkIr5qa9NKn4uZGRvxtt3DvtUrBYW5Iu2y4SS/vx79JN0XDHNYgAJ81nVs+4M7UE1Y/O+g==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "path": "microsoft.extensions.caching.abstractions/5.0.0", "hashPath": "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "path": "microsoft.extensions.caching.memory/5.0.0", "hashPath": "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "path": "microsoft.extensions.configuration/5.0.0", "hashPath": "microsoft.extensions.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Of1Irt1+NzWO+yEYkuDh5TpT4On7LKl98Q9iLqCdOZps6XXEWDj3AKtmyvzJPVXZe4apmkJJIiDL7rR1yC+hjQ==", "path": "microsoft.extensions.configuration.binder/5.0.0", "hashPath": "microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OelM+VQdhZ0XMXsEQBq/bt3kFzD+EBGqR4TAgFDRAye0JfvHAaRi+3BxCRcwqUAwDhV0U0HieljBGHlTgYseRA==", "path": "microsoft.extensions.configuration.commandline/5.0.0", "hashPath": "microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fqh6y6hAi0Z0fRsb4B/mP9OkKkSlifh5osa+N/YSQ+/S2a//+zYApZMUC1XeP9fdjlgZoPQoZ72Q2eLHyKLddQ==", "path": "microsoft.extensions.configuration.environmentvariables/5.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "path": "microsoft.extensions.configuration.json/5.0.0", "hashPath": "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+tK3seG68106lN277YWQvqmfyI/89w0uTu/5Gz5VYSUu5TI4mqwsaWLlSmT9Bl1yW/i1Nr06gHJxqaqB5NU9Tw==", "path": "microsoft.extensions.configuration.usersecrets/5.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-//mDNrYeiJ0eh/awFhDFJQzkRVra/njU5Y4fyK7X29g5HScrzbUkKOKlyTtygthcGFt4zNC8G5CFCjb/oizomA==", "path": "microsoft.extensions.dependencyinjection/5.0.1", "hashPath": "microsoft.extensions.dependencyinjection.5.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0IoXXfkgKpYJB1t2lC0jPXAxuaywRNc9y2Mq96ZZNKBthL38vusa2UK73+Bm6Kq/9a5xNHJS6NhsSN+i5TEtkA==", "path": "microsoft.extensions.fileproviders.composite/5.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-sSf5oTuE/BYju9hqvwL6CSwArv76mONplyVpYV2J8Il/m2mtuabx2o3YmTeO8aa5+2JaFWZlOX+2X3fWYEp79w==", "path": "microsoft.extensions.fileproviders.embedded/5.0.17", "hashPath": "microsoft.extensions.fileproviders.embedded.5.0.17.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbUOCePYBl1UhM+N2zmDSUyJ6cODulbtUd9gEzMFIK3RQDtP/gJsE08oLcBSXH3Q1RAQ0ex7OAB3HeTKB9bXpg==", "path": "microsoft.extensions.hosting.abstractions/5.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-XBX02xG84g6q+sGnUnBLuRHZt+ZfKIKeY+oLsSqSb/0Hy53lmCGiufCpMH4TZVqmpT3xmFb47YKhA4ROt0SwVQ==", "path": "microsoft.extensions.localization/5.0.5", "hashPath": "microsoft.extensions.localization.5.0.5.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-NIKR1AE1gt4QfThQCxJnzQIYcp0sDijX61GtkrgDce0kqatAME7oZDnYQAZTYlm/QYXKNYqu+S58BW53QRM7oQ==", "path": "microsoft.extensions.localization.abstractions/5.0.5", "hashPath": "microsoft.extensions.localization.abstractions.5.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-280RxNJqOeQqq47aJLy5D9LN61CAWeuRA83gPToQ8B9jl9SNdQ5EXjlfvF66zQI5AXMl+C/3hGnbtIEN+X3mqA==", "path": "microsoft.extensions.options.configurationextensions/5.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VdLJOCXhZaEMY7Hm2GKiULmn7IEPFE4XC5LPSfBVCUIA8YLZVh846gtfBJalsPQF2PlzdD7ecX7DZEulJ402ZQ==", "path": "microsoft.netcore.platforms/2.0.0", "hashPath": "microsoft.netcore.platforms.2.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Web.Infrastructure/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FNmvLn5m2LTU/Rs2KWVo0SIIh9Ek+U0ojex7xeDaSHw/zgEP77A8vY5cVWgUtBGS8MJfDGNn8rpXJWEIQaPwTg==", "path": "microsoft.web.infrastructure/1.0.0", "hashPath": "microsoft.web.infrastructure.1.0.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-EE7M37c5E/kvulzEkpUR6v1AnK34b2wysOLJHSjl78p/3hL7grte0XCPRqCfLZDwq98AD9GHMTCRfZy7TEeHhw==", "path": "nito.asyncex.context/5.1.0", "hashPath": "nito.asyncex.context.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Coordination/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nv+oA+cSxidjOImiKcz2FJgMIDxiK0A6xormKmsUklUBjTNqQpjtdJsACMgTQG56PkTHdbMi5QijPTTUsmcCeg==", "path": "nito.asyncex.coordination/5.1.0", "hashPath": "nito.asyncex.coordination.5.1.0.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tU3Ib4zs8ivM+uS8n7F7ReWZlA3mODyLqwPE+v+WJI94hZ8xLXl+a9npfj/IcmeXo9a6fGKLWkswKQHOeTWqwA==", "path": "nito.asyncex.tasks/5.1.0", "hashPath": "nito.asyncex.tasks.5.1.0.nupkg.sha512"}, "Nito.Collections.Deque/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-RXHe531Oaw2IathDr0Q2kbid0iuudBxtgZsfBZ2eUPuFI8I1P7HMiuUeaIefqYykcDYFTDQsFAPAljduIjihLA==", "path": "nito.collections.deque/1.1.0", "hashPath": "nito.collections.deque.1.1.0.nupkg.sha512"}, "Nito.Disposables/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-QcL+uBwUCEoK8GKp/WzjdCiG8/3G1WLlVNJgLJUNG7bIIVAcEV+Mro4s53VT4Nd8xMSplv0gy+Priw44vRvLaA==", "path": "nito.disposables/2.2.0", "hashPath": "nito.disposables.2.2.0.nupkg.sha512"}, "NPOI/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-xTCJOXdcBWV9GUAVRJTC73L5bU3Ahgtuk13U/rOix8Gi05XWf9E4K+vGx63JZt35WoroXq+hM0RZ+aad2y8Ceg==", "path": "npoi/2.5.5", "hashPath": "npoi.2.5.5.nupkg.sha512"}, "Portable.BouncyCastle/1.8.9": {"type": "package", "serviceable": true, "sha512": "sha512-wlJo8aFoeyl+W93iFXTK5ShzDYk5WBqoUPjTNEM0Xv9kn1H+4hmuCjF0/n8HLm9Nnp1aY6KNndWqQTNk+NGgRQ==", "path": "portable.bouncycastle/1.8.9", "hashPath": "portable.bouncycastle.1.8.9.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SharpZipLib/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-WSdeDReL8eugMCw5BH/tFAZpgR+YsYMwm6kIvqg3J8LbfRjbbebmEzn63AbEveqyMOljBO68g6tCCv165wMkSg==", "path": "sharpziplib/1.3.2", "hashPath": "sharpziplib.1.3.2.nupkg.sha512"}, "SkiaSharp/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptuxAKk9FiClNnAgWM8hVMCYw/B0hUJWZ8W6efnIAtJmJn/Xl4jvxxDF5WOqfQYCLVzxXw5gvBPVxvTLblFp0g==", "path": "skiasharp/1.68.0", "hashPath": "skiasharp.1.68.0.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.CodeDom/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hs9pw/kmvH3lXaZ1LFKj3pLQsiGfj2xo3sxSzwiLlRL6UcMZUTeCfoJ9Udalvn3yq5dLlPEZzYegrTQ1/LhPOQ==", "path": "system.codedom/4.7.0", "hashPath": "system.codedom.4.7.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "path": "system.configuration.configurationmanager/4.5.0", "hashPath": "system.configuration.configurationmanager.4.5.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tCQTzPsGZh/A9LhhA6zrqCRV4hOHsK90/G7q3Khxmn6tnB1PuNU0cRaKANP2AWcF9bn0zsuOoZOSrHuJk6oNBA==", "path": "system.diagnostics.diagnosticsource/5.0.0", "hashPath": "system.diagnostics.diagnosticsource.5.0.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-v+XbyYHaZjDfn0ENmJEV1VYLgGgCTx1gnfOBcppowbpOAriglYgGCvFCPr2EEZyBvXlpxbEsTwkOlInl107ahA==", "path": "system.drawing.common/4.7.0", "hashPath": "system.drawing.common.4.7.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "path": "system.dynamic.runtime/4.0.11", "hashPath": "system.dynamic.runtime.4.0.11.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.2.9": {"type": "package", "serviceable": true, "sha512": "sha512-GjR4CCtIkiJSU6N8cidG4aa0ph+HBzFOq3uhLybuq4zjlKy3hjDrGbcEUeBiGpBmUrnUhTAJ5SCZGDoZS7d9SA==", "path": "system.linq.dynamic.core/1.2.9", "hashPath": "system.linq.dynamic.core.1.2.9.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Management/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-IY+uuGhgzWiCg21i8IvQeY/Z7m1tX8VuPF+ludfn7iTCaccTtJo5HkjZbBEL8kbBubKhAKKtNXr7uMtmAc28Pw==", "path": "system.management/4.7.0", "hashPath": "system.management.4.7.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Net.Http.WinHttpHandler/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZmsFZIZ4PL2UfUlfj4KCzzAGGE2SF39ENIqtvfgu0bwMEAe3J3CqZr765E2W6eQQtNK08/8DpHcsA0sAKZdEtA==", "path": "system.net.http.winhttphandler/4.4.0", "hashPath": "system.net.http.winhttphandler.4.4.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-tsDkr5hwYwxV2LaB3H5BvCltCX57wv7JzwU5q8IV9vrFdzq/e/nPJAj4U5Ny/FzR4sMTVRprVSwl4mZqfFkt4Q==", "path": "system.private.servicemodel/4.4.4", "hashPath": "system.private.servicemodel.4.4.4.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-xK6JE0mpsBD+T0qu3V6dmVRa06PxAvIIM/zSjouqP7Sk6X+FQj+9XFRz9GmZk9aJGMU1LX6AgTZIlsYZ64QKsw==", "path": "system.reflection.dispatchproxy/4.4.0", "hashPath": "system.reflection.dispatchproxy.4.4.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "path": "system.reflection.emit.ilgeneration/4.7.0", "hashPath": "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "path": "system.security.accesscontrol/4.5.0", "hashPath": "system.security.accesscontrol.4.5.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-4WQjFuypWtxb/bl/YwEE7LYGn4fgpsikFfBU6xwEm4YBYiRAhXAEJ62lILBu2JJSFbClIAntFTGfDZafn8yZTg==", "path": "system.security.cryptography.cng/4.7.0", "hashPath": "system.security.cryptography.cng.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0Srzh6YlhjuMxaqMyeCCdZs22cucaUAG6SKDd3gNHBJmre0VZ71ekzWu9rvLD4YXPetyNdPvV6Qst+8C++9v3Q==", "path": "system.security.cryptography.pkcs/4.7.0", "hashPath": "system.security.cryptography.pkcs.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-uwlfOnvJd7rXRvP3aV126Q9XebIIEGEaZ245Rd5/ZwOg7U7AU+AmpE0vRh2F0DFjfOTuk7MAexv4nYiNP/RYnQ==", "path": "system.security.cryptography.x509certificates/4.3.2", "hashPath": "system.security.cryptography.x509certificates.4.3.2.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "path": "system.security.permissions/4.5.0", "hashPath": "system.security.permissions.4.5.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-U77HfRXlZlOeIXd//Yoj6Jnk8AXlbeisf1oq1os+hxOGVnuG+lGSfGqTwTZBoORFF6j/0q7HXIl8cqwQ9aUGqQ==", "path": "system.security.principal.windows/4.5.0", "hashPath": "system.security.principal.windows.4.5.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-y/XLnKJ+xnuEUjrgJkXeLKCH4A+EwkX2TdOcTdgsEtxWmWq1+RbCjMd0zIlyNrbGD+nM9BxNg9rLVWVAPq81RA==", "path": "system.servicemodel.duplex/4.4.4", "hashPath": "system.servicemodel.duplex.4.4.4.nupkg.sha512"}, "System.ServiceModel.Http/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-f7OWPKqfTaCzjpc6n+/xqNwv7YAHKMiBCPIgwxIXVnf0Vu9+yzfX6tXV9pSSCEFuqJ5tXGLz9MRRExrQEqVUkA==", "path": "system.servicemodel.http/4.4.4", "hashPath": "system.servicemodel.http.4.4.4.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-TZUwkBUHK+HgPVpypcnCEzenn+Hly3mQ+QDqDtHKyqVBP2Yt+DAMp3NwuW35mTSMRqna5p9hd0U0vVq5azovOQ==", "path": "system.servicemodel.nettcp/4.4.4", "hashPath": "system.servicemodel.nettcp.4.4.4.nupkg.sha512"}, "System.ServiceModel.Primitives/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-Jv882Qt+tSJ2KnLGGMSGPwBvOxfXRncXW0YV/beBKRQ2c6bDbXVXwqaS2U9h+/cK1uL7C4zc66lnC7qvSBqPHw==", "path": "system.servicemodel.primitives/4.4.4", "hashPath": "system.servicemodel.primitives.4.4.4.nupkg.sha512"}, "System.ServiceModel.Security/4.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-8mJj3lUNbBkntouQ3Eg3IF04GxiDrDK2X79+kcfq8V+W7NoYBREgWczaD60ZmW4KKRKnL0q3OnUNJlkzJJfe5w==", "path": "system.servicemodel.security/4.4.4", "hashPath": "system.servicemodel.security.4.4.4.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-aeu4FlaUTemuT1qOd1MyU4T516QR4Fy+9yDbwWMPHOHy7U8FD6SgTzdZFO7gHcfAPHtECqInbwklVvUK4RHcNg==", "path": "system.text.encoding.codepages/4.7.0", "hashPath": "system.text.encoding.codepages.4.7.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-EEslUvHKll1ftizbn20mX3Ix/l4Ygk/bdJ2LY6/X6FlGaP0RIhKMo9nS6JIGnKKT6KBP2PGj6JC3B9/ZF6ErqQ==", "path": "system.text.encodings.web/5.0.0", "hashPath": "system.text.encodings.web.5.0.0.nupkg.sha512"}, "System.Text.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+luxMQNZ2WqeffBU7Ml6njIvxc8169NW2oU+ygNudXQGZiarjE7DOtN7bILiQjTZjkmwwRZGTtLzmdrSI/Ustw==", "path": "system.text.json/5.0.0", "hashPath": "system.text.json.5.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-A/uxsWi/Ifzkmd4ArTLISMbfFs6XpRPsXZonrIqyTY70xi8t+mDtvSM5Os0RqyRDobjMBwIDHDL4NOIbkDwf7A==", "path": "system.xml.xpath.xmldocument/4.3.0", "hashPath": "system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"}, "TimeZoneConverter/3.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-4rqXd06uLqaLPGpO+whrZnSTJyG3SG5DEbeldMTvaGit0+gI6XLVb9YMnfM+5wtV0DDM1QPd/QhbE6j5vJCWDw==", "path": "timezoneconverter/3.4.0", "hashPath": "timezoneconverter.3.4.0.nupkg.sha512"}, "Volo.Abp.Auditing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-zptBxUin6U/LbdRdG6rRn+NyTQIAqN7wqjx3Ui7G9vvTHrRCreqzvuOsPC767HDUlcu+8o3LNJLQCuE981219A==", "path": "volo.abp.auditing/4.3.0", "hashPath": "volo.abp.auditing.4.3.0.nupkg.sha512"}, "Volo.Abp.AuditLogging.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hFvjlGIYGdxX2m0l9Byax+VVH1yJnUvsem2XZF4rfchbOT6ncANCmwuQXUP1Ij2Vls1cwDP4D1wJPqGDtnmbFA==", "path": "volo.abp.auditlogging.domain.shared/4.3.0", "hashPath": "volo.abp.auditlogging.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Authorization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9j5U19ntqS8JR+s86xNFm0ieH9CHz/po51d6BHxLTZzJuKbIu9xg453KvhKyuXYYnhp+/afu7Uq5EEi/s7sl9A==", "path": "volo.abp.authorization/4.3.0", "hashPath": "volo.abp.authorization.4.3.0.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-enL7a1MfJR0yjPVhf2UCgEmmQQ5wNb2GGX3k/iKzW5i//3lcnsDZQLAg17NKcbLc+imQ39RALNTOE4ALZQ4C7Q==", "path": "volo.abp.authorization.abstractions/4.3.0", "hashPath": "volo.abp.authorization.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.AutoMapper/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-mqeDGZxdhXFQMvxfM26snT2q0pbA+3OFE4rIoFmNdooK6fE0oWCcRg5vAOuaZNlzMCfpRFtNatSfhwZUXXfYCA==", "path": "volo.abp.automapper/4.3.0", "hashPath": "volo.abp.automapper.4.3.0.nupkg.sha512"}, "Volo.Abp.BackgroundJobs.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fXOJQadj8j7WYbm8gWZhdb1b0euOVbt0hwk2ov6078IdxN14mriDjmCNIg69C1LmNDc7gDs3L8YbswQlIUOsPA==", "path": "volo.abp.backgroundjobs.domain.shared/4.3.0", "hashPath": "volo.abp.backgroundjobs.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.BlobStoring/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-EGOqlOVqeTyiPrbd+SmwUlalW5mHDFbmHp2B7TAeeGW5rO3EVqkkQEcJVnIlguAVHrdNnGFE6Swf+XtNHjoNfA==", "path": "volo.abp.blobstoring/4.3.0", "hashPath": "volo.abp.blobstoring.4.3.0.nupkg.sha512"}, "Volo.Abp.BlobStoring.Database.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1d88ahE8dK9Y7SIq6vbtIdGmD1OHfpqmwpVdyH5NWKnJM5P4WkcuFheOR7S4Zp0/gRDTejxS0RMnyVrUSm8WmQ==", "path": "volo.abp.blobstoring.database.domain.shared/4.3.0", "hashPath": "volo.abp.blobstoring.database.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Caching/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-QYq+uERJoiWqGUmF5UhI3UB1SPoVV1AknnUelnBNVorfcPyT39CUhbHUlgUuCfSC2zpF5yvXyj+18ioOhht+kQ==", "path": "volo.abp.caching/4.3.0", "hashPath": "volo.abp.caching.4.3.0.nupkg.sha512"}, "Volo.Abp.Commercial.Core/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FCf1InEuqKPE36zhiy5biNtm3N+SlR9sztxLIHCJ5kCueYqFH9Te75Lj+G2dm3fLsE2Fmuvv+zmMBoNIhzeU5g==", "path": "volo.abp.commercial.core/4.3.0", "hashPath": "volo.abp.commercial.core.4.3.0.nupkg.sha512"}, "Volo.Abp.Commercial.SuiteTemplates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rw5/wElG4KB0JJ8Fd/WgmXh47AZXhN1UsujqzAFed8saUJCR4jav9M90bWbSrv+Eht6D5TPS7nHNzsge/VpCFw==", "path": "volo.abp.commercial.suitetemplates/4.3.0", "hashPath": "volo.abp.commercial.suitetemplates.4.3.0.nupkg.sha512"}, "Volo.Abp.Core/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GTXSY8W+AG5sMczOv40AR85eq9a+R/qI4Ci7h853rudprGWkwtfYw9OQnrVd3Xu74WceSTfCDtHWbu2AuJ3Q+w==", "path": "volo.abp.core/4.3.0", "hashPath": "volo.abp.core.4.3.0.nupkg.sha512"}, "Volo.Abp.Data/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2r42Sq03qH9z/B3fUZebbAeiEAPO3poHtzLslBhm4Qjjf9jlhBrkVjqZNkCDggNFNMVzRwWmrzSzIop8q8Crug==", "path": "volo.abp.data/4.3.0", "hashPath": "volo.abp.data.4.3.0.nupkg.sha512"}, "Volo.Abp.Ddd.Application/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CGyrh+hAV6RPCVk+yT+BUa2itmnjbSCkBZBrUhkFvEJior8BvLVbMT9DGOxoDE3pOzeIkcqdk2bog9Wiq19fkg==", "path": "volo.abp.ddd.application/4.3.0", "hashPath": "volo.abp.ddd.application.4.3.0.nupkg.sha512"}, "Volo.Abp.Ddd.Application.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ysaifHH09mGhQLxEBTAKu8F/GsKbZmcs78CIulkqliPM9qMtyqIuNDSPq/LvWbyRBfPWb/wFdHiBtcBGj2tZVw==", "path": "volo.abp.ddd.application.contracts/4.3.0", "hashPath": "volo.abp.ddd.application.contracts.4.3.0.nupkg.sha512"}, "Volo.Abp.Ddd.Domain/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nq5cMx0Uifg7GW8E8NedJZ7jaiPJv2Y9axYht5xbHEJ9gpsy1oovOp2VPKZ5Y5YYJY+M/1dlUEMAjM2LAANfpA==", "path": "volo.abp.ddd.domain/4.3.0", "hashPath": "volo.abp.ddd.domain.4.3.0.nupkg.sha512"}, "Volo.Abp.EventBus/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AjBsTwigVF/LJVJf/5JgwWUX7vRNvHR/HRxst4+mDS/VJZDsgqIZLwDOFnFuA+wKtOW49e9+wuaU/LLat9HPoQ==", "path": "volo.abp.eventbus/4.3.0", "hashPath": "volo.abp.eventbus.4.3.0.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DW2Cr8k/g2kWqxLBEZvBjT/BSpbUo3zw8uSBMaAGAAKn4k7cNp7FC3YESu0C3y45Nd8Lo/3JLc9Iw4NN5uFmSA==", "path": "volo.abp.eventbus.abstractions/4.3.0", "hashPath": "volo.abp.eventbus.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.ExceptionHandling/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7ht+TOh8YlBZ+06GQ9vXP0FEbCPh7suNqUeKkz034NNNBKYVga9LU6qIpSaquVsM4++8NI/5AZpNh+ocgGhUBw==", "path": "volo.abp.exceptionhandling/4.3.0", "hashPath": "volo.abp.exceptionhandling.4.3.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Application/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DwHeg8JPO4ZQAG7K8A2N0oMf2ChJ2vitKvF4Eiki2VQ2xISmyndRKpnopWhP/N7mV+iBlORGjw7AYkxpOWZ2uQ==", "path": "volo.abp.featuremanagement.application/4.3.0", "hashPath": "volo.abp.featuremanagement.application.4.3.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Application.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cCYP/q7Dbn43lBeaT9NLKumpD+19xJlMbw2129RjkC5sIj7fqSwuX20+yCY6R3yz1Rd0bmMG4vYRQ+dLgso8cQ==", "path": "volo.abp.featuremanagement.application.contracts/4.3.0", "hashPath": "volo.abp.featuremanagement.application.contracts.4.3.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Domain/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-huvba+96YGVINLqbwzpGwreIhqkAVGwhjwd9OA1tFLlNi17INSHocMyKhxU/vQIpyAkAT+UumtO8Pwr+I2NJ3g==", "path": "volo.abp.featuremanagement.domain/4.3.0", "hashPath": "volo.abp.featuremanagement.domain.4.3.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-aBtNfDG98wRuCGiNi0ylJRN+80p5t5R8Y0A2qFnYdCQAGbTE/6la6wzMc0Uvrjag9JnQldNmf9Q9qTSqWp5rSw==", "path": "volo.abp.featuremanagement.domain.shared/4.3.0", "hashPath": "volo.abp.featuremanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Features/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-8pHFDHqOQ54UEDciL8deBMg7nSIteTQWUzOT2ieuTl2WC8Mw198v3DYpXH1BTz0s9A9lM/i2h1qRVzVW+TMjrg==", "path": "volo.abp.features/4.3.0", "hashPath": "volo.abp.features.4.3.0.nupkg.sha512"}, "Volo.Abp.GlobalFeatures/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DvDZ+7aQzEB2gtcRBJVFYiobfU5Rzj3p6Wsbp8XdEOlYLQXP55Ga8wiUMtMH4crOzHYSwHbdPC1s2bHMgVM9rA==", "path": "volo.abp.globalfeatures/4.3.0", "hashPath": "volo.abp.globalfeatures.4.3.0.nupkg.sha512"}, "Volo.Abp.Guids/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Czq7YNU8zrSLQMx+8tpRbxjqN0eOmXAcr7XXy/qJSIoFLGCgh9xkH5CQhKmEFzl0vJFXf4DiHHEPAoRbW6mrhw==", "path": "volo.abp.guids/4.3.0", "hashPath": "volo.abp.guids.4.3.0.nupkg.sha512"}, "Volo.Abp.Http.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Fy32oLk5amHZlObVzCz+FWDcjqd06IOhDSN10dg6Cueaefh/BRTd72tGSHPrEpKpjLLvnCU/L19ZIZd9DJAabQ==", "path": "volo.abp.http.abstractions/4.3.0", "hashPath": "volo.abp.http.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-s9/gdb8ZUXx7VjI2OE6hiBuPcnNpFTeWjEV5VcT5gPAYOXICdFj7LqiZdDypAAiM0RpthJoUpE4zQDRlg3ZN3g==", "path": "volo.abp.identity.domain.shared/4.3.0", "hashPath": "volo.abp.identity.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Identity.Pro.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GQcVxMqPAqmP+L4P+QtCzQ5GZl+zE35+RSuvndCkFuKi/Dl8hrIsLF6USUvFDb7SSoXVZSQhzrAvVbuXdWUa2w==", "path": "volo.abp.identity.pro.domain.shared/4.3.0", "hashPath": "volo.abp.identity.pro.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.IdentityServer.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbEHuIhHlFsM8ZQRo7rgQboU43N31DQX6Ceji27JrgIvCL6PdvcAu8ISAvX2nZUyo4NO1MLDmV+PI+cpuVf5NA==", "path": "volo.abp.identityserver.domain.shared/4.3.0", "hashPath": "volo.abp.identityserver.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Json/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yZJ/JjJeEO7vaQ/5vH1k0WUDPLjr7rkJ/JkhB6yjRDVirISPWL3y2CbTh6NPSyjTBShkaETCsOeAQBbjDvEbPQ==", "path": "volo.abp.json/4.3.0", "hashPath": "volo.abp.json.4.3.0.nupkg.sha512"}, "Volo.Abp.LanguageManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DcFGUIVg732oQ6FerPdhKfTOEwoVt2X+979gsC+WTJHkyUAv+ij2AAesR2ZRpn9NcoESG87yJgyYQo0FbBaqOQ==", "path": "volo.abp.languagemanagement.domain.shared/4.3.0", "hashPath": "volo.abp.languagemanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Localization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-d5yfg69jxE+rvyhjR1RoeF5KW0gNay2xnhixa6gAOXQ/8ZG7WmgU+G5Aa8GjgFzccL9XgFJYq7Ehzwtz3BS5ZA==", "path": "volo.abp.localization/4.3.0", "hashPath": "volo.abp.localization.4.3.0.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-8edOpld98PimnPODkrWtw/chW+K+1oAQUZccaO6F1DdnUsknQ03k6tXNvdVaXDQft74SsMcaf1tlHrvfrKPhZA==", "path": "volo.abp.localization.abstractions/4.3.0", "hashPath": "volo.abp.localization.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.MultiTenancy/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/DU+5xg+7v889I86EguH+s/2C8wleVaphlTAuRgSPlWIujeOh6O7Dj+ImBb+LOEFK+6PvnzDyDCca57k2lwRsw==", "path": "volo.abp.multitenancy/4.3.0", "hashPath": "volo.abp.multitenancy.4.3.0.nupkg.sha512"}, "Volo.Abp.ObjectExtending/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBfao2YPtulP+ZaDrNe0Tx/K9bAiodv+gqYVWQ7UsrVloLIiQ1EMFv0Ln5zI9O111cgQHfLvEVYiFguP2GjSjA==", "path": "volo.abp.objectextending/4.3.0", "hashPath": "volo.abp.objectextending.4.3.0.nupkg.sha512"}, "Volo.Abp.ObjectMapping/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4QwdHZFbDiLMW4QNaQvDqjmHw5a3BEKhDmGUkWYwqNHVBTpwj/t7Fm+N0g1FXGjYE5muioko168pbsA8oPwDDw==", "path": "volo.abp.objectmapping/4.3.0", "hashPath": "volo.abp.objectmapping.4.3.0.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P4aQJiM7cmfkTSwADALSRDJbuJi6oLv2JXytmUqpxh5Qjp/xYafRWIuul7TurJY7Ps8RpAogEAkukqTfuFyoHA==", "path": "volo.abp.permissionmanagement.domain.shared/4.3.0", "hashPath": "volo.abp.permissionmanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Security/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HNShcU4urZUP4McuJFqv/X3m2tyJQkrruvuIbV+4MR9a817zo82BPqmzYG7RrZnG2Spyvqaa8GtXBfCGgFVUZA==", "path": "volo.abp.security/4.3.0", "hashPath": "volo.abp.security.4.3.0.nupkg.sha512"}, "Volo.Abp.Serialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YWnDgZbX1cvbKWKjs7reRjrxXfWNEFioHYxXxKhe/fAUbnBIA4lJWenMwsbZ5kioaiFZu6uQtj7qfs07AgxnuA==", "path": "volo.abp.serialization/4.3.0", "hashPath": "volo.abp.serialization.4.3.0.nupkg.sha512"}, "Volo.Abp.SettingManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wtv5TitvfXhR8+lOIUz1p4gAkcUxVZP8qeKW5SjaNqDFvZnJNZTj8ug+u9Hi8YfKxPpZ74n9gSZ7VUYWFwrDMQ==", "path": "volo.abp.settingmanagement.domain.shared/4.3.0", "hashPath": "volo.abp.settingmanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Settings/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-aCEhagDlTzoS+c/RI/geH7YlK+d3fYvQ/WRI75PLVJMMhMALXT25cZr71BsNwu00LLUnPf6xFfLkfwlsZfIaKA==", "path": "volo.abp.settings/4.3.0", "hashPath": "volo.abp.settings.4.3.0.nupkg.sha512"}, "Volo.Abp.Specifications/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/Yg1LiFm+JPX5sGTS/OPxTRvaaEaNlkvCAJAwjTC2CkjrdjPSKDEiB47tCWHYW0dIcIyEx/bV9L5inyLc2qAQ==", "path": "volo.abp.specifications/4.3.0", "hashPath": "volo.abp.specifications.4.3.0.nupkg.sha512"}, "Volo.Abp.TextTemplateManagement.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NPQDCSdt2CMSWKMqxeJ8kGRvCeuvs0WQqao3OPFGFoz8i38NgMhtrTyxwzBld5MaxRHo5WHUldZ/o/68eERmFQ==", "path": "volo.abp.texttemplatemanagement.domain.shared/4.3.0", "hashPath": "volo.abp.texttemplatemanagement.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HGZ7MNUNUamMKwhp9taCv8LTnsltA3WchiO0qIuf1BRAg3333muQqrtmraIB/k6T+1ecPoEIpxXzxwgMmTvvxg==", "path": "volo.abp.threading/4.3.0", "hashPath": "volo.abp.threading.4.3.0.nupkg.sha512"}, "Volo.Abp.Timing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ohLNz/1T1sKLFV0IXeqxrMlu2sf/qdM3Bj4hOzhse9MGB+jO2l8DKmzOOH70xpH6wWrRELjmRyOoR8U3jwc8Hg==", "path": "volo.abp.timing/4.3.0", "hashPath": "volo.abp.timing.4.3.0.nupkg.sha512"}, "Volo.Abp.Uow/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CwmJseLIBUcFsAiIVSr9WiNBcn/cqzJDCZmAYqzTLy7etETO+nMXSqohvAt6LlbwmjXHfMO5EZb6mYy4nnTbKQ==", "path": "volo.abp.uow/4.3.0", "hashPath": "volo.abp.uow.4.3.0.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-8Kqu7sjhWod7GuvqMYi5xYPbgKjDRBHE5MHDo3OF5FLHn2uCd597jlupvd6paSsrvDA2HgmpG88m6dkanmG+MQ==", "path": "volo.abp.users.domain.shared/4.3.0", "hashPath": "volo.abp.users.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Abp.Validation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SIXT7AImi3XRgC1XIs1QJhyEXNOEugYgpR8dpB17sIBcOBdkz9r4XO6lxlSarT5+vo7I9Qut8zmH9S2T76nNWg==", "path": "volo.abp.validation/4.3.0", "hashPath": "volo.abp.validation.4.3.0.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2dj1wA0YTVxA1wQQ0zE8R3a8nBk4fo/ibUYNV8aMRxkxX83e80BVNHi/5hMabG2ybHmTZrSLxquZyzwKvgSWkw==", "path": "volo.abp.validation.abstractions/4.3.0", "hashPath": "volo.abp.validation.abstractions.4.3.0.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YL<PERSON>rsAsqFzDOIOAGmCk78KskDcwLR8EsDxRdtgnztxGIEfok7c3/39nwbpwrkFBrwN5ZP0Qoo03VjFc+49cgqg==", "path": "volo.abp.virtualfilesystem/4.3.0", "hashPath": "volo.abp.virtualfilesystem.4.3.0.nupkg.sha512"}, "Volo.Saas.Domain/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-wYbXIKES2twqfCIKQYTr1v3EJnJqdJM/kCk4aLvDdMrsM9ztzAkmlv8oufHm0jS4dbTeogX48HajC4hSnnJdUQ==", "path": "volo.saas.domain/4.3.0", "hashPath": "volo.saas.domain.4.3.0.nupkg.sha512"}, "Volo.Saas.Domain.Shared/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9MMtuAmDoingHJoE0o+GBLvMJ20Fn5fUXgW7TsogBB1cRvsjabs4qTy6H8pF7JszEIlzuWb+0cxWKqXZeKN/cA==", "path": "volo.saas.domain.shared/4.3.0", "hashPath": "volo.saas.domain.shared.4.3.0.nupkg.sha512"}, "Volo.Saas.Host.Application/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-EoqOxL9LesEyzyltFovZUve9W14J5e9k9jmBWBfSbX9RWrK+FOe0mevJPsiCxFG9LeGUFER6Sin/297iEC16xg==", "path": "volo.saas.host.application/4.3.0", "hashPath": "volo.saas.host.application.4.3.0.nupkg.sha512"}, "Volo.Saas.Host.Application.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-wg8AzHM89se7h8PmBYyPCtgoXZl2/bppzcDOtpZx71xFrQy+J7tGp57JdNELXroqo5tjNl9ZP0Os10QWtCVk0Q==", "path": "volo.saas.host.application.contracts/4.3.0", "hashPath": "volo.saas.host.application.contracts.4.3.0.nupkg.sha512"}, "ZXing.Net/0.16.10": {"type": "package", "serviceable": true, "sha512": "sha512-9avtcn21T7Ndcl8PQ1LHR7/wEoCruX1QKKHvO6zBPTsDW9IdvR5vKOmd618AY+DtDWZz8NaFDTkpbZdgaF4l4w==", "path": "zxing.net/0.16.10", "hashPath": "zxing.net.0.16.10.nupkg.sha512"}, "AOMS.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BondedModuleV2.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "BondedModuleV2.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "BondedModuleV2.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "BondedModuleV2.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "GeneralModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "GeneralModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "GeneralModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "InboundModule.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "InboundModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "InboundModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "InboundModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "MasterDataModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "OutboundModule.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "OutboundModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "OutboundModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "OutboundModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ReportModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ReportModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ReportModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "SeaModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "SeaModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShareDataModule.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShareDataModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShareDataModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShareDataModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "TrayModule.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "TrayModule.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "TrayModule.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.Abp.LeptonTheme.Management.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.Chat.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.FileManagement.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.FileManagement.Application.Contracts/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.FileManagement.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Volo.FileManagement.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}